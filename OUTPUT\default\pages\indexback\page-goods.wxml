<page-panel bind:__l="__l" bind:clickMore="__e" class="page-goods data-v-48650d30" data-event-opts="{{[ [ '^clickMore',[ ['clickMore'] ] ] ]}}" openType="getUserInfo" title="惠花维豆" vueId="56a39982-1" vueSlots="{{['bd']}}">
    <view class="data-v-48650d30" slot="bd">
        <view class="page-goods__tip flex-row data-v-48650d30">
            <view class="data-v-48650d30"># 惠生活 兑好物 甄选品质看得见 #</view>
        </view>
        <view class="page-goods__list flex-row flex-wrap data-v-48650d30">
            <comp-button bind:__l="__l" bind:getUserInfo="__e" class="page-goods__item flex-row col-center row-center data-v-48650d30" data-event-opts="{{[ [ '^getUserInfo',[ [ 'clickItem',['$0'],[ [ ['list','',index] ] ] ] ] ] ]}}" openType="getUserInfo" vueId="{{'56a39982-2-'+index+','+'56a39982-1'}}" vueSlots="{{['default']}}" wx:for="{{list}}" wx:key="index">
                <view class="item__main flex-col data-v-48650d30">
                    <comp-image bind:__l="__l" class="data-v-48650d30" height="192rpx" isServer="{{true}}" name="{{item.img}}" vueId="{{'56a39982-3-'+index+','+'56a39982-2-'+index}}" width="192rpx"></comp-image>
                    <view class="item__name nowrap data-v-48650d30">{{item.name}}</view>
                    <view class="item__score flex-row col-center data-v-48650d30">
                        <comp-image bind:__l="__l" class="data-v-48650d30" height="33rpx" name="home-coin" vueId="{{'56a39982-4-'+index+','+'56a39982-2-'+index}}" width="33rpx"></comp-image>
                        <view class="score__number data-v-48650d30">{{item.score}}</view>
                    </view>
                </view>
            </comp-button>
        </view>
    </view>
</page-panel>
