(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["components/comp-help-card/comp-help-card"], {
    "25ee": function ee(n, e, t) {


      t.d(e, "b", function() {
        return u;
      }), t.d(e, "c", function() {
        return r;
      }), t.d(e, "a", function() {
        return c;
      });
      var c = {
          compIcon: function compIcon() {
            return t.e("components/comp-icon/comp-icon").then(t.bind(null, "84bb2"));
          }
        },
        u = function u() {
          var n = this.$createElement;
          this._self._c;
        },
        r = [];
    },
    "5d5b": function d5b(n, e, t) {


      t.r(e);
      var c = t("25ee"),
        u = t("b9b6");
      for (var r in u)["default"].indexOf(r) < 0 && function(n) {
        t.d(e, n, function() {
          return u[n];
        });
      }(r);
      t("f794");
      var o = t("828b"),
        a = Object(o["a"])(u["default"], c["b"], c["c"], !1, null, "3d299412", null, !1, c["a"], void 0);
      e["default"] = a.exports;
    },
    "72c1": function c1(n, e, t) {},
    b9b6: function b9b6(n, e, t) {


      t.r(e);
      var c = t("d102"),
        u = t.n(c);
      for (var r in c)["default"].indexOf(r) < 0 && function(n) {
        t.d(e, n, function() {
          return c[n];
        });
      }(r);
      e["default"] = u.a;
    },
    d102: function d102(n, e, t) {


      var c = t("47a9");
      Object.defineProperty(e, "__esModule", {
        value: !0
      }), e.default = void 0;
      var u = c(t("5e82")),
        r = {
          props: {
            helpId: {
              type: Number
            },
            name: {
              type: String,
              default: ""
            }
          },
          methods: {
            clickCard: function clickCard() {
              u.default.to({
                page: "helpAnswer",
                query: {
                  helpId: this.helpId
                }
              });
            }
          }
        };
      e.default = r;
    },
    f794: function f794(n, e, t) {


      var c = t("72c1"),
        u = t.n(c);
      u.a;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['components/comp-help-card/comp-help-card-create-component', {
    'components/comp-help-card/comp-help-card-create-component': function componentsCompHelpCardCompHelpCardCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("5d5b"));
    }
  },
  [
    ['components/comp-help-card/comp-help-card-create-component']
  ]
]);