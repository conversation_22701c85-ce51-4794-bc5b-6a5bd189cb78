{"entryPagePath": "pages/index/index", "pages": ["pages/index/index", "pages/indexback/index", "pages/auth/auth", "pages/discover/discover", "pages/seckill/flashsale", "pages/discover/proxy", "pages/duiba/duiba", "pages/duibaPay/duibaPay", "pages/duibaRedirect/duibaRedirect", "pages/login/login", "pages/service/service", "pages/service/proxy", "pages/user/user", "pages/webview/webview", "pages/webview/webviewKF", "pages/webview/webviewAct", "pages/webview/webviewH5", "pages/webview/webviewAR"], "permission": {"scope.userLocation": {"desc": "没有授权将影响后续体验，请前往设置修改授权"}}, "networkTimeout": {"request": 60000, "connectSocket": 60000, "uploadFile": 60000, "downloadFile": 60000}, "plugins": {}, "tabBar": {"color": "#000000", "selectedColor": "#306DFF", "borderStyle": "black", "backgroundColor": "#F9FAFB", "list": [{"text": "会员权益", "pagePath": "pages/index/index", "iconPath": "static\\icon1-auth.png", "selectedIconPath": "static\\icon1-auth-active.png"}, {"text": "服务直通车", "pagePath": "pages/service/service", "iconPath": "static\\icon1-serve.png", "selectedIconPath": "static\\icon1-serve-active.png"}, {"text": "回馈商城", "pagePath": "pages/seckill/flashsale", "iconPath": "static\\icon1-shop.png", "selectedIconPath": "static\\icon1-shop-active.png"}, {"text": "活动社区", "pagePath": "pages/discover/discover", "iconPath": "static\\icon1-activey.png", "selectedIconPath": "static\\icon1-activey-active.png"}, {"text": "我的", "pagePath": "pages/user/user", "iconPath": "static\\icon1-my.png", "selectedIconPath": "static\\icon1-my-active.png"}]}, "preloadRule": {"pages/index/index": {"network": "all", "packages": ["pages-common", "pages-user"]}}, "style": "v2", "renderer": "webview", "requiredPrivateInfos": ["getLocation", "chooseLocation"], "componentFramework": "exparser", "window": {"enablePullDownRefresh": true, "navigationStyle": "custom"}, "subPackages": [{"root": "pages-activity/", "pages": ["center/index", "ele/ele", "promote/index", "promote/page-detail", "Trade-in/index", "Trade-in/assess", "Trade-in/address", "Trade-in/coupons", "Trade-in/useCoupon", "eleTradein/entry", "eleTradein/index", "eleTradein/address", "reputation/point", "reputation/collect", "reputation/form-sumbit", "reputation/detail", "eleTradein/posts", "comment/index", "comment/collect", "comment/detail", "comment/my-device", "invoice/index", "invoice/collect", "invoice/detail", "zhongcao/form-sumbit", "zhongcao/list", "zhongcao/detail", "zhongcao/show", "jingdong/form-sumbit", "jingdong/list", "jingdong/detail", "jingdong/show", "category/index"]}, {"root": "pages-common/", "pages": ["develop/develop", "search/search", "simple-list/simple-list"]}, {"root": "pages-community/", "pages": ["template/article", "article/article", "comment/comment", "community/community", "video/video", "play-video/play-video"]}, {"root": "pages-course/", "pages": ["course/course"]}, {"root": "pages-education/", "pages": ["course-major/course-major", "index/index", "reserve/reserve", "sign/sign"]}, {"root": "pages-queryParam/", "pages": ["entry/index", "models/index", "compare/index", "back-pannel/page-list", "jieneng-model/page-list", "punching-pic/series"]}, {"root": "pages-guarantee/", "pages": ["activation/activation", "activation/offline", "appeal/appeal", "appeal-history/appeal-history", "cdkey/cdkey", "device/device", "device/page-about-starvip", "form/add", "form/detail", "grid/grid", "guarantee/guarantee", "profile/profile", "redirect/redirect", "tutorial/online", "welcome/welcome", "welcome/offline"]}, {"root": "pages-help/", "pages": ["entry/index", "help/help", "help-answer/help-answer", "feedback/feedback", "complaintAndSuggest/complaintAndSuggest", "policyFee/policyFee", "cwnews/cwnews"]}, {"root": "pages-picture/", "pages": ["picture/page-category", "picture/webviewAct", "picture/page-list", "picture/page-add-pic", "picture/page-add-video", "wallpaper/page-entry", "wallpaper/page-category", "wallpaper/page-list", "wallpaper/page-add-pic", "myIncome/page-income", "myIncome/page-withdrawal", "myIncome/page-certification", "picture/all-category", "picture/video-category", "picture/video-list", "picture/page-picture-detail"]}, {"root": "pages-invite/", "pages": ["invite/invite"]}, {"root": "pages-landing/", "pages": ["qrcode/qrcode"]}, {"root": "pages-member/", "pages": ["member/member"]}, {"root": "pages-product/", "pages": ["appoint/install", "suggest/suggest", "mySuggestion/mySuggestion", "mySuggestion/suggesDetail", "myComplaint/myComplaint", "myComplaint/complaintDetail", "myActivities/myActivities", "appoint/contacts", "appoint/my-sales<PERSON><PERSON><PERSON>", "appoint/search-model", "appoint/my-device", "appoint/repair", "appoint/page-survey-entry", "appoint/survey", "appoint/calculator", "appoint/calculation-report", "question/question", "appoint/calculation-fail", "appraise/engineer-detail", "appraise/engineer", "appraise/salesman-detail", "appraise/salesman", "complain/complain", "invoice/add", "invoice/detail", "product/add", "product/detail", "product-list/product-list", "welcome/engineer", "welcome/salesman", "worksheet/worksheet", "worksheet/progress", "coupon/get-coupon", "coupon/get-coupon-yj", "coupon/my-coupon", "coupon/exchange", "explain/search-model", "explain/search", "explain/explain", "explain/result", "policyFees/main", "policyFees/servicePolicy", "policyFees/installFees", "policyFees/repairFees", "policyFees/parts", "policyFees/articleDetail", "serviceBox/serviceBox"]}, {"root": "pages-user/", "pages": ["phone/phone", "auth-goods/auth-goods", "member-card/member-card", "get-wd/get-wd", "more-auth/more-auth", "my-product/my-product", "record/record", "profile/profile", "setting/setting", "sign-phone/sign-phone", "user-growth/user-growth", "user-level/rights", "user-score/user-score", "user-task/user-task", "user-task/task-center"]}, {"root": "pages-cwshop/", "pages": ["order/list/list", "goods/detail/detail", "order/detail/detail", "order/logistics/logistics", "order/aftersale/aftersale", "order/list/after-sales", "order/detail/after-sales-detail", "order/detail/evaluation-apply", "order/aftersale/shipping"]}]}