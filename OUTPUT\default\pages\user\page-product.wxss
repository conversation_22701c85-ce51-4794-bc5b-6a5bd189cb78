.page-product.data-v-07af3e95 {
    background: #fff;
    border-radius: 24rpx;
    height: 502rpx;
    margin: 0 auto;
    padding-bottom: 24rpx;
    width: 702rpx
}

.hd.data-v-07af3e95 {
    padding: 30rpx 40rpx
}

.title.data-v-07af3e95 {
    font-size: var(--font-size-40);
    font-weight: var(--font-weight-bold)
}

.more.data-v-07af3e95 {
    color: rgba(0,0,0,.3);
    font-size: var(--font-size-28);
    line-height: 40rpx
}

.empty__wrap.data-v-07af3e95 {
    padding: 0 40rpx 40rpx
}

.empty.data-v-07af3e95 {
    background: #f6f7f8;
    border-radius: 16rpx;
    min-height: 330rpx
}

.empty__name.data-v-07af3e95 {
    font-size: var(--font-size-40);
    font-weight: var(--font-weight-bold)
}

.empty__add.data-v-07af3e95 {
    background: var(--color-brand-1);
    border-radius: 32rpx;
    color: var(--color-fill-grey-inverse);
    font-size: var(--font-size-28);
    font-weight: var(--font-weight-bold);
    height: 64rpx;
    margin-top: 20rpx;
    padding: 0 25rpx
}

.product__wrap.data-v-07af3e95 {
    margin: 0 0 0 24rpx;
    padding-bottom: 40rpx
}

.product.data-v-07af3e95 {
    background: #f6f7f8;
    border-radius: 16rpx
}

.product__hd.data-v-07af3e95 {
    padding: 32rpx;
    position: relative
}

.product__hd .info__hd.data-v-07af3e95 {
    position: relative
}

.product__hd .info__bd.data-v-07af3e95 {
    padding: 16rpx 0 16rpx 24rpx
}

.product__hd .info__mode.data-v-07af3e95 {
    font-size: var(--font-size-40);
    font-weight: var(--font-weight-bold);
    line-height: 56rpx
}

.product__hd .info__code.data-v-07af3e95 {
    color: var(--color-text-sub);
    font-size: var(--font-size-24);
    line-height: 33rpx
}

.product__hd .info__hd .info__tag.data-v-07af3e95 {
    background: var(--color-brand-1);
    color: var(--color-fill-grey-inverse);
    font-size: 20rpx;
    height: 32rpx;
    left: 0;
    padding: 0 12rpx;
    position: absolute;
    top: 0
}

.product__hd .info__index.data-v-07af3e95 {
    color: var(--color-text-sub);
    font-size: var(--font-size-24);
    line-height: 1;
    position: absolute;
    right: 32rpx;
    top: 32rpx
}

.product__hd .info__index .current.data-v-07af3e95 {
    color: var(--color-text-title);
    font-size: var(--font-size-40);
    font-weight: var(--font-weight-bold);
    line-height: 1
}

.product__bd.data-v-07af3e95 {
    border-top: 2rpx solid var(--color-divider-line)
}

.product__btn.data-v-07af3e95 {
    min-width: 210rpx;
    padding: 30rpx 0 40rpx;
    position: relative
}

.product .btn__name.data-v-07af3e95 {
    font-size: var(--font-size-22);
    line-height: 30rpx;
    margin-top: 14rpx
}

.product .btn__tag.data-v-07af3e95 {
    background: var(--color-red-1);
    border-radius: 16rpx 16rpx 16rpx 0;
    color: var(--color-fill-grey-inverse);
    font-size: 16rpx;
    height: 32rpx;
    line-height: 32rpx;
    padding: 0 8rpx;
    position: absolute;
    right: -25rpx;
    top: 12rpx
}
