.container {
    position: relative
}

.video_demo {
    width: 100%;
    height: 100%
}

.container-user {
    height: 100%;
    background: linear-gradient(90deg,hsla(0,0%,100%,0),rgba(0,0,0,.25));
    z-index: 10000;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
    -webkit-align-items: center;
    align-items: center
}

.user-image {
    border-radius: 50%;
    background-color: rgba(0,0,0,.5)
}

.mi-lin-company,.user-friends-circle,.user-like,.user-share {
    width: 100%;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-align-items: center;
    align-items: center
}

.user-like-num {
    font-size: 24rpx;
    color: #fff;
    margin-top: 10rpx;
    font-family: PingFang SC;
    font-weight: 400;
    text-shadow: 0rpx 1rpx 4rpx rgba(0,0,0,.5)
}

.user-share-icon {
    border-radius: 50%
}

.mi-lin-info,.user-friends-info,.user-share-info {
    margin-top: 8rpx;
    font-size: 22rpx;
    font-family: PingFang SC;
    font-weight: 400;
    color: #fff;
    text-shadow: 0rpx 1rpx 4rpx rgba(0,0,0,.5)
}

.user-friends-info {
    margin-top: 4rpx
}

.user-friends-circle-icon {
    border-radius: 50%
}

.bottom-info {
    height: 200rpx;
    padding-left: 20rpx;
    box-sizing: border-box;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
    color: #fff
}

.bottom-info,.bottom-title {
    display: -webkit-flex;
    display: flex
}

.bottom-title {
    width: 100%;
    height: 40rpx;
    -webkit-align-items: center;
    align-items: center
}

.bottom-title,.title-info {
    font-family: PingFang SC;
    font-weight: 500;
    color: #fff;
    text-shadow: 0rpx 1rpx 4rpx rgba(0,0,0,.5)
}

.title-icon {
    width: 30rpx
}

.title-info {
    margin-left: 20rpx;
    -webkit-flex: 1;
    flex: 1;
    height: 100%;
    white-space: nowrap
}

.bottom-direction,.title-info {
    overflow: hidden;
    text-overflow: ellipsis
}

.bottom-direction {
    width: 100%;
    padding-left: 50rpx;
    box-sizing: border-box;
    margin-top: 18rpx;
    font-family: PingFang SC;
    font-weight: 400;
    color: #fff;
    text-shadow: 0rpx 1rpx 4rpx rgba(0,0,0,.5);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical
}

.video-scale {
    position: absolute;
    right: 0rpx;
    bottom: 0rpx;
    background: linear-gradient(90deg,hsla(0,0%,100%,0),rgba(0,0,0,.25));
    display: -webkit-flex;
    display: flex;
    -webkit-align-items: center;
    align-items: center;
    -webkit-justify-content: center;
    justify-content: center;
    z-index: 1000
}
