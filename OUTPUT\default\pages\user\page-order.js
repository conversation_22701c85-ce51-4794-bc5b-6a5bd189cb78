(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/user/page-order"], {
    "1a9aa": function a9aa(e, n, t) {


      t.r(n);
      var r = t("6d89"),
        o = t.n(r);
      for (var u in r)["default"].indexOf(u) < 0 && function(e) {
        t.d(n, e, function() {
          return r[e];
        });
      }(u);
      n["default"] = o.a;
    },
    "1e89": function e89(e, n, t) {},
    "63ff": function ff(e, n, t) {


      var r = t("1e89"),
        o = t.n(r);
      o.a;
    },
    "6d89": function d89(e, n, t) {


      var r = t("47a9");
      Object.defineProperty(n, "__esModule", {
        value: !0
      }), n.default = void 0;
      var o = r(t("7ca3")),
        u = r(t("5e82")),
        i = t("8f59");

      function a(e, n) {
        var t = Object.keys(e);
        if (Object.getOwnPropertySymbols) {
          var r = Object.getOwnPropertySymbols(e);
          n && (r = r.filter(function(n) {
            return Object.getOwnPropertyDescriptor(e, n).enumerable;
          })), t.push.apply(t, r);
        }
        return t;
      }
      var c = {
        props: {
          orderCounts: {
            type: Object,
            default: function _default() {
              return {
                pendingPayNum: 0,
                pendingSendNum: 0,
                pendingReceiveNum: 0,
                pendingCommentNum: 0,
                refundNum: 0
              };
            }
          }
        },
        data: function data() {
          return {
            isFirst: !0,
            pendingPayNum: 0,
            pendingSendNum: 0,
            pendingReceiveNum: 0,
            pendingCommentNum: 0,
            refundNum: 0,
            orderTypes: [{
              key: "pendingPay",
              label: "待付款",
              icon: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/user-order-1.png",
              url: "/pages-cwshop/order/list/list?type=1",
              badge: 0
            }, {
              key: "pendingSend",
              label: "待发货",
              icon: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/user-order-2.png",
              url: "/pages-cwshop/order/list/list?type=4",
              badge: 0
            }, {
              key: "pendingReceive",
              label: "待签收",
              icon: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/user-order-3.png",
              url: "/pages-cwshop/order/list/list?type=6",
              badge: 0
            }, {
              key: "pendingComment",
              label: "待评价",
              icon: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/user-order-4.png",
              url: "/pages-cwshop/order/list/list?type=7",
              badge: 0
            }, {
              key: "refund",
              label: "退款/售后",
              icon: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/user-order-5.png",
              url: "/pages-cwshop/order/list/after-sales",
              badge: 0
            }]
          };
        },
        computed: function(e) {
          for (var n = 1; n < arguments.length; n++) {
            var t = null != arguments[n] ? arguments[n] : {};
            n % 2 ? a(Object(t), !0).forEach(function(n) {
              (0, o.default)(e, n, t[n]);
            }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : a(Object(t)).forEach(function(n) {
              Object.defineProperty(e, n, Object.getOwnPropertyDescriptor(t, n));
            });
          }
          return e;
        }({}, (0, i.mapState)(["token"])),
        watch: {
          orderCounts: {
            handler: function handler(e) {
              this.updateOrderBadges(e);
            },
            immediate: !0,
            deep: !0
          }
        },
        methods: {
          toAllOrder: function toAllOrder() {
            u.default.to({
              fullPath: "/pages-cwshop/order/list/list"
            });
          },
          onOrderType: function onOrderType(e) {
            console.log(e), this.token ? u.default.to({
              fullPath: e.url
            }) : u.default.to({
              page: "auth"
            });
          },
          updateOrderBadges: function updateOrderBadges(e) {
            this.orderTypes.forEach(function(n) {
              switch (n.key) {
                case "pendingPay":
                  n.badge = e.pendingPayNum;
                  break;
                case "pendingSend":
                  n.badge = e.pendingSendNum;
                  break;
                case "pendingReceive":
                  n.badge = e.pendingReceiveNum;
                  break;
                case "pendingComment":
                  n.badge = e.pendingCommentNum;
                  break;
                case "refund":
                  n.badge = e.refundNum;
                  break;
              }
            });
          },
          getBadgeCount: function getBadgeCount(e) {
            switch (e) {
              case "pendingPay":
                return this.orderCounts.pendingPayNum;
              case "pendingSend":
                return this.orderCounts.pendingSendNum;
              case "pendingReceive":
                return this.orderCounts.pendingReceiveNum;
              case "pendingComment":
                return this.orderCounts.pendingCommentNum;
              case "refund":
                return this.orderCounts.refundNum;
              default:
                return 0;
            }
          }
        }
      };
      n.default = c;
    },
    b002: function b002(e, n, t) {


      t.d(n, "b", function() {
        return o;
      }), t.d(n, "c", function() {
        return u;
      }), t.d(n, "a", function() {
        return r;
      });
      var r = {
          compButton: function compButton() {
            return Promise.all([t.e("common/vendor"), t.e("components/comp-button/comp-button")]).then(t.bind(null, "ca5a"));
          },
          compIcon: function compIcon() {
            return t.e("components/comp-icon/comp-icon").then(t.bind(null, "84bb2"));
          }
        },
        o = function o() {
          var e = this,
            n = e.$createElement,
            t = (e._self._c, e.__map(e.orderTypes, function(n, t) {
              var r = e.__get_orig(n),
                o = e.getBadgeCount(n.key) && e.token,
                u = o ? e.getBadgeCount(n.key) : null,
                i = !o || u > 99 ? null : e.getBadgeCount(n.key);
              return {
                $orig: r,
                m0: o,
                m1: u,
                m2: i
              };
            }));
          e.$mp.data = Object.assign({}, {
            $root: {
              l0: t
            }
          });
        },
        u = [];
    },
    fa38: function fa38(e, n, t) {


      t.r(n);
      var r = t("b002"),
        o = t("1a9aa");
      for (var u in o)["default"].indexOf(u) < 0 && function(e) {
        t.d(n, e, function() {
          return o[e];
        });
      }(u);
      t("63ff");
      var i = t("828b"),
        a = Object(i["a"])(o["default"], r["b"], r["c"], !1, null, "18fd57ba", null, !1, r["a"], void 0);
      n["default"] = a.exports;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/user/page-order-create-component', {
    'pages/user/page-order-create-component': function pagesUserPageOrderCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("fa38"));
    }
  },
  [
    ['pages/user/page-order-create-component']
  ]
]);