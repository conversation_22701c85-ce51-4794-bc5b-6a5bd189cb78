<view class="comp-swiper data-v-d0047d96">
    <swiper autoplay="{{true}}" bindchange="__e" circular="{{true}}" class="data-v-d0047d96" data-event-opts="{{[ [ 'change',[ [ 'change',['$event'] ] ] ] ]}}" interval="{{interval}}" style="{{'height:'+height+'px'+';'}}">
        <swiper-item class="data-v-d0047d96" wx:for="{{$root.l0}}" wx:key="index">
            <comp-button bind:__l="__l" bind:getUserInfo="__e" bind:onClick="__e" class="comp-swiper__banner data-v-d0047d96" data-event-opts="{{[ [ '^getUserInfo',[ [ 'authClickItem',['$0'],[ [ ['list','',index] ] ] ] ] ],[ '^onClick',[ [ 'clickItem',['$0'],[ [ ['list','',index] ] ] ] ] ] ]}}" openType="{{item[$orig].openType}}" userInfoIsAuth="{{item.g0===-1}}" vueId="{{'7beaac58-1-'+index}}" vueSlots="{{['default']}}">
                <comp-image bind:__l="__l" class="banner__img data-v-d0047d96" height="{{height+'px'}}" isServer="{{true}}" name="{{item[$orig].img}}" radius="{{radius}}" vueId="{{'7beaac58-2-'+index+','+'7beaac58-1-'+index}}" width="100%"></comp-image>
            </comp-button>
        </swiper-item>
    </swiper>
    <view class="{{['comp-swiper__indicator','flex-row','data-v-d0047d96',isIndicatorLeft?'is-left':'',isIndicatorCenter?'is-center':'']}}" style="{{'bottom:'+indicatorBottom+';'}}">
        <view class="{{['indicator__item','data-v-d0047d96',index===current?'is-active':'']}}" wx:for="{{list}}" wx:key="index"></view>
    </view>
</view>
