<view class="page-panel data-v-59ade3f5">
    <view class="page-panel__hd flex-row space-between data-v-59ade3f5">
        <view class="page-panel__title data-v-59ade3f5">{{title}}</view>
        <comp-button bind:__l="__l" bind:getUserInfo="__e" bind:onClick="__e" class="data-v-59ade3f5" data-event-opts="{{[ [ '^getUserInfo',[ ['clickMore'] ] ],[ '^onClick',[ ['clickMore'] ] ] ]}}" openType="{{openType}}" vueId="10983d51-1" vueSlots="{{['default']}}" wx:if="{{isMore}}">
            <view class="page-panel__more flex-row col-center row-center data-v-59ade3f5">
                <view class="more__name data-v-59ade3f5">更多</view>
                <comp-icon bind:__l="__l" class="data-v-59ade3f5" color="rgba(0, 0, 0, 0.30)" icon="iconarrow_right_o" size="24rpx" vueId="{{'10983d51-2'+','+'10983d51-1'}}"></comp-icon>
            </view>
        </comp-button>
    </view>
    <view class="page-panel__bd data-v-59ade3f5">
        <slot name="bd"></slot>
    </view>
    <view class="page-panel__ft data-v-59ade3f5">
        <slot name="ft"></slot>
    </view>
</view>
