(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/indexback/page-nav"], {
    5937: function _(t, e, n) {


      n.r(e);
      var r = n("62e1"),
        o = n.n(r);
      for (var c in r)["default"].indexOf(c) < 0 && function(t) {
        n.d(e, t, function() {
          return r[t];
        });
      }(c);
      e["default"] = o.a;
    },
    "62e1": function e1(t, e, n) {


      var r = n("47a9");
      Object.defineProperty(e, "__esModule", {
        value: !0
      }), e.default = void 0;
      var o = r(n("7ca3")),
        c = n("8f59"),
        a = r(n("5e82")),
        i = r(n("a23b"));

      function u(t, e) {
        var n = Object.keys(t);
        if (Object.getOwnPropertySymbols) {
          var r = Object.getOwnPropertySymbols(t);
          e && (r = r.filter(function(e) {
            return Object.getOwnPropertyDescriptor(t, e).enumerable;
          })), n.push.apply(n, r);
        }
        return n;
      }
      var f = {
        props: {
          navs: {
            type: Array,
            default: function _default() {
              return [];
            }
          }
        },
        computed: function(t) {
          for (var e = 1; e < arguments.length; e++) {
            var n = null != arguments[e] ? arguments[e] : {};
            e % 2 ? u(Object(n), !0).forEach(function(e) {
              (0, o.default)(t, e, n[e]);
            }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(n)) : u(Object(n)).forEach(function(e) {
              Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(n, e));
            });
          }
          return t;
        }({}, (0, c.mapState)(["remoteConfig"])),
        methods: {
          clickNav: function clickNav(t) {
            t.url && ("bzzx" == t.id ? a.default.to({
              fullPath: "/pages-activity/category/index?type=helpCenter"
            }) : "syjc" == t.id ? a.default.to({
              fullPath: "/pages-activity/category/index?type=wjjc"
            }) : a.default.to({
              fullPath: t.url
            })), this.$emit("clickNav", t);
          },
          checkIsNav: function checkIsNav(t) {
            return "yqhl" !== t.id || i.default.get(this.remoteConfig, "switch.invite", !0);
          }
        }
      };
      e.default = f;
    },
    "913e1": function e1(t, e, n) {


      n.r(e);
      var r = n("af3b3"),
        o = n("5937");
      for (var c in o)["default"].indexOf(c) < 0 && function(t) {
        n.d(e, t, function() {
          return o[t];
        });
      }(c);
      n("bff3");
      var a = n("828b"),
        i = Object(a["a"])(o["default"], r["b"], r["c"], !1, null, "df45385c", null, !1, r["a"], void 0);
      e["default"] = i.exports;
    },
    "9b37": function b37(t, e, n) {},
    af3b3: function af3b3(t, e, n) {


      n.d(e, "b", function() {
        return o;
      }), n.d(e, "c", function() {
        return c;
      }), n.d(e, "a", function() {
        return r;
      });
      var r = {
          compButton: function compButton() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-button/comp-button")]).then(n.bind(null, "ca5a"));
          },
          compImage: function compImage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-image/comp-image")]).then(n.bind(null, "31bf"));
          }
        },
        o = function o() {
          var t = this,
            e = t.$createElement,
            n = (t._self._c, t.__map(t.navs, function(e, n) {
              var r = t.__get_orig(e),
                o = t.checkIsNav(e);
              return {
                $orig: r,
                m0: o
              };
            }));
          t.$mp.data = Object.assign({}, {
            $root: {
              l0: n
            }
          });
        },
        c = [];
    },
    bff3: function bff3(t, e, n) {


      var r = n("9b37"),
        o = n.n(r);
      o.a;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/indexback/page-nav-create-component', {
    'pages/indexback/page-nav-create-component': function pagesIndexbackPageNavCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("913e1"));
    }
  },
  [
    ['pages/indexback/page-nav-create-component']
  ]
]);