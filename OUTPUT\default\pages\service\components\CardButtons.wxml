<view class="card-buttons data-v-32a38080">
    <view bindtap="__e" class="{{['data-v-32a38080','card-btn',size]}}" data-event-opts="{{[ [ 'tap',[ [ 'handleClick',['$0'],[ [ ['buttons','',idx] ] ] ] ] ] ]}}" style="{{'flex-basis:'+flexBasis+';'}}" wx:for="{{buttons}}" wx:for-index="idx" wx:for-item="btn" wx:key="idx">
        <image class="data-v-32a38080" src="{{btn.icon}}" style="{{'width:'+imgSize+';'+'height:'+imgSize+';'}}"></image>
        <view class="btn-title data-v-32a38080" wx:if="{{size==='small'}}">{{''+btn.title+''}}</view>
        <view class="btn-title data-v-32a38080" wx:if="{{btn.description&&size==='small'}}">{{''+btn.description+''}}</view>
    </view>
</view>
