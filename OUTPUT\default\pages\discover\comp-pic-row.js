(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/discover/comp-pic-row"], {
    "08ba": function ba(t, e, n) {


      var r = n("47a9");
      Object.defineProperty(e, "__esModule", {
        value: !0
      }), e.default = void 0;
      var u = r(n("7eb4")),
        c = r(n("ee10")),
        a = r(n("5e82")),
        i = (r(n("a23b")), {
          components: {},
          props: {
            banner: {
              type: Array,
              default: function _default() {
                return [];
              }
            },
            list: {
              type: Array,
              default: function _default() {
                return [];
              }
            }
          },
          data: function data() {
            return {
              isExpand: !1,
              current: 0
            };
          },
          computed: {
            innerList: function innerList() {
              var t = this.list;
              return this.isExpand ? t : t.slice(0, 3);
            }
          },
          methods: {
            change: function change(t) {
              this.current = t.detail.current;
            },
            clickAct: function clickAct(t) {
              return (0, c.default)(u.default.mark(function e() {
                return u.default.wrap(function(e) {
                  while (1) switch (e.prev = e.next) {
                    case 0:
                      if ("功能视频" !== t.columnName) {
                        e.next = 3;
                        break;
                      }
                      return a.default.to({
                        page: "course"
                      }), e.abrupt("return");
                    case 3:
                      a.default.to({
                        page: "templateArticle",
                        query: {
                          id: t.id,
                          title: t.columnName,
                          articleType: t.articleType,
                          columnLabel: t.columnLabel
                        }
                      });
                    case 4:
                    case "end":
                      return e.stop();
                  }
                }, e);
              }))();
            }
          }
        });
      e.default = i;
    },
    4784: function _(t, e, n) {},
    "53c9": function c9(t, e, n) {


      n.d(e, "b", function() {
        return u;
      }), n.d(e, "c", function() {
        return c;
      }), n.d(e, "a", function() {
        return r;
      });
      var r = {
          compButton: function compButton() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-button/comp-button")]).then(n.bind(null, "ca5a"));
          }
        },
        u = function u() {
          var t = this.$createElement;
          this._self._c;
        },
        c = [];
    },
    "606f": function f(t, e, n) {


      var r = n("4784"),
        u = n.n(r);
      u.a;
    },
    6894: function _(t, e, n) {


      n.r(e);
      var r = n("53c9"),
        u = n("f47f");
      for (var c in u)["default"].indexOf(c) < 0 && function(t) {
        n.d(e, t, function() {
          return u[t];
        });
      }(c);
      n("606f");
      var a = n("828b"),
        i = Object(a["a"])(u["default"], r["b"], r["c"], !1, null, "de8c5bac", null, !1, r["a"], void 0);
      e["default"] = i.exports;
    },
    f47f: function f47f(t, e, n) {


      n.r(e);
      var r = n("08ba"),
        u = n.n(r);
      for (var c in r)["default"].indexOf(c) < 0 && function(t) {
        n.d(e, t, function() {
          return r[t];
        });
      }(c);
      e["default"] = u.a;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/discover/comp-pic-row-create-component', {
    'pages/discover/comp-pic-row-create-component': function pagesDiscoverCompPicRowCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("6894"));
    }
  },
  [
    ['pages/discover/comp-pic-row-create-component']
  ]
]);