<view class="page-nav flex flex-row space-between data-v-38a402f0">
    <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-38a402f0" data-event-opts="{{[ [ '^getUserInfo',[ ['clickMyScore'] ] ] ]}}" openType="getUserInfo" vueId="599cf2f4-1" vueSlots="{{['default']}}">
        <view class="page-nav__item my-score flex-col row-center data-v-38a402f0">
            <view class="item__hd item__name flex-row col-center data-v-38a402f0">
                <text class="mar-r4 data-v-38a402f0">我的优惠券</text>
                <comp-image bind:__l="__l" class="data-v-38a402f0" height="26rpx" name="v2_icon_next_arrow1" vueId="{{'599cf2f4-2'+','+'599cf2f4-1'}}" width="26rpx"></comp-image>
            </view>
            <view class="item__bd flex-row col-center data-v-38a402f0">
                <view class="item__subname data-v-38a402f0">{{(couponNum||0)+'张可用'}}</view>
            </view>
        </view>
    </comp-button>
    <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-38a402f0" data-event-opts="{{[ [ '^getUserInfo',[ ['clickScoreExchange'] ] ] ]}}" openType="getUserInfo" vueId="599cf2f4-3" vueSlots="{{['default']}}">
        <view class="page-nav__item score-exchange flex-col row-center data-v-38a402f0">
            <view class="item__hd item__name flex-row col-center data-v-38a402f0">
                <text class="mar-r4 data-v-38a402f0">维豆兑换</text>
                <comp-image bind:__l="__l" class="data-v-38a402f0" height="26rpx" name="v2_icon_next_arrow1" vueId="{{'599cf2f4-4'+','+'599cf2f4-3'}}" width="26rpx"></comp-image>
            </view>
            <view class="item__bd flex-row col-center data-v-38a402f0">
                <view class="item__subname data-v-38a402f0">兑换记录</view>
            </view>
        </view>
    </comp-button>
    <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-38a402f0" data-event-opts="{{[ [ '^getUserInfo',[ ['clickMemberTask'] ] ] ]}}" openType="getUserInfo" vueId="599cf2f4-5" vueSlots="{{['default']}}">
        <view class="page-nav__item member-task flex-col row-center data-v-38a402f0">
            <view class="item__hd item__name flex-row col-center data-v-38a402f0">
                <text class="mar-r4 data-v-38a402f0">我的图册</text>
                <comp-image bind:__l="__l" class="data-v-38a402f0" height="26rpx" name="v2_icon_next_arrow1" vueId="{{'599cf2f4-6'+','+'599cf2f4-5'}}" width="26rpx"></comp-image>
            </view>
            <view class="item__bd flex-row col-center data-v-38a402f0">
                <view class="item__subname data-v-38a402f0">精彩瞬间</view>
            </view>
        </view>
    </comp-button>
</view>
