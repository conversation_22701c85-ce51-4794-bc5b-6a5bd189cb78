(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["components/comp-contact/comp-contact"], {
    "0d10": function d10(t, n, e) {


      e.r(n);
      var c = e("ce4f"),
        o = e("52bb");
      for (var f in o)["default"].indexOf(f) < 0 && function(t) {
        e.d(n, t, function() {
          return o[t];
        });
      }(f);
      e("89d1c");
      var u = e("828b"),
        a = Object(u["a"])(o["default"], c["b"], c["c"], !1, null, "5f1694c0", null, !1, c["a"], void 0);
      n["default"] = a.exports;
    },
    "52bb": function bb(t, n, e) {


      e.r(n);
      var c = e("fb4f"),
        o = e.n(c);
      for (var f in c)["default"].indexOf(f) < 0 && function(t) {
        e.d(n, t, function() {
          return c[t];
        });
      }(f);
      n["default"] = o.a;
    },
    "89d1c": function d1c(t, n, e) {


      var c = e("99e7"),
        o = e.n(c);
      o.a;
    },
    "99e7": function e7(t, n, e) {},
    ce4f: function ce4f(t, n, e) {


      e.d(n, "b", function() {
        return c;
      }), e.d(n, "c", function() {
        return o;
      }), e.d(n, "a", function() {});
      var c = function c() {
          var t = this.$createElement;
          this._self._c;
        },
        o = [];
    },
    fb4f: function fb4f(t, n, e) {


      (function(t) {
        Object.defineProperty(n, "__esModule", {
          value: !0
        }), n.default = void 0;
        var e = {
          data: function data() {
            return {};
          },
          methods: {
            contact: function contact() {
              t.openCustomerServiceChat ? t.openCustomerServiceChat({
                extInfo: {
                  url: "https://work.weixin.qq.com/kfid/kfcd7f58e9513ad72ad"
                },
                corpId: "ww48df30f06c821750"
              }) : t.showModal({
                title: "提示",
                content: "当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。"
              });
            }
          }
        };
        n.default = e;
      }).call(this, e("3223")["default"]);
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['components/comp-contact/comp-contact-create-component', {
    'components/comp-contact/comp-contact-create-component': function componentsCompContactCompContactCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("0d10"));
    }
  },
  [
    ['components/comp-contact/comp-contact-create-component']
  ]
]);