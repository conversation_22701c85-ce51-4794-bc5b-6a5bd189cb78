.comp-error.data-v-22dacf18 {
    bottom: 0;
    left: 0;
    padding-top: 124rpx;
    position: fixed;
    right: 0;
    z-index: 0
}

.name.data-v-22dacf18 {
    color: var(--color-text-subtitle);
    font-size: 48rpx;
    font-weight: var(--font-weight-bold);
    line-height: 67rpx;
    margin-top: 32rpx
}

.subname.data-v-22dacf18 {
    margin-top: 20rpx
}

.btn.data-v-22dacf18,.subname.data-v-22dacf18 {
    color: var(--color-text-weak);
    line-height: 42rpx
}

.btn.data-v-22dacf18 {
    border: 2rpx solid var(--color-divider-line);
    border-radius: 36rpx;
    height: 72rpx;
    margin-top: 45rpx;
    min-width: 240rpx
}

.rotate.data-v-22dacf18 {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
    transition: all 1s
}
