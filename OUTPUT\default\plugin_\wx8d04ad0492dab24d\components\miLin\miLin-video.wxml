<video adUnitId="{{adUnitId}}" autoPauseIfNavigate="{{autoPauseIfNavigate}}" autoPauseIfOpenNative="{{autoPauseIfOpenNative}}" autoplay="{{autoplay}}" bindcontrolstoggle="bindControlStoggle" bindended="bindEnded" bindenterpictureinpicture="bindEnterPictureInPicture" binderror="bindError" bindfullscreenchange="bindFullScreenChange" bindleavepictureinpicture="bindLeavePictureInPicture" bindloadedmetadata="bindLoadedMetaData" bindpause="bindPause" bindplay="bindPlay" bindprogress="bindProgress" bindseekcomplete="bindSeekComplete" bindtimeupdate="bindTimeUpdate" bindwaiting="bindWaiting" class="video_class" controls="{{controls}}" danmuBtn="{{danmuBtn}}" danmuList="{{danmuList}}" direction="{{direction}}" duration="{{duration}}" enableAutoRotation="{{enableAutoRotation}}" enableDanmu="{{enableDanmu}}" enablePlayGesture="{{enablePlayGesture}}" enableProgressGesture="{{enableProgressGesture}}" id="{{id}}" initialTime="{{initialTime}}" loop="{{loop}}" miLinToken="{{miLinToken}}" muted="{{muted}}" objectFit="{{objectFit}}" pageGesture="{{pageGesture}}" pictureInPictureMode="{{pictureInPictureMode}}" pictureInPictureShowProgress="{{pictureInPictureShowProgress}}" playBtnPosition="{{playBtnPosition}}" poster="{{poster}}" posterForCrawler="{{posterForCrawler}}" showCastingButton="{{showCastingButton}}" showCenterPlayBtn="{{showCenterPlayBtn}}" showFullscreenBtn="{{showFullscreenBtn}}" showMuteBtn="{{showMuteBtn}}" showPlayBtn="{{showPlayBtn}}" showProgress="{{showProgress}}" showScreenLockButton="{{showScreenLockButton}}" showSnapshotButton="{{showSnapshotButton}}" src="{{miLinSrc}}" title="{{title}}" vslideGesture="{{vslideGesture}}" vslideGestureInFullscreen="{{vslideGestureInFullscreen}}">
    <slot name="{{slotName}}"></slot>
</video>
