.comp-store-card.data-v-921ef98a {
    border: 2rpx solid var(--color-divider-line);
    border-radius: 8rpx;
    overflow: hidden;
    padding: 24rpx 32rpx
}

.is-checked.data-v-921ef98a {
    border-color: var(--color-brand-1)
}

.name.data-v-921ef98a {
    color: #1d1d1d;
    font-size: 30rpx;
    font-weight: var(--font-weight-bold);
    line-height: 42rpx
}

.subname.data-v-921ef98a {
    color: var(--color-text-sub);
    font-size: 26rpx;
    line-height: 37rpx;
    margin-top: 12rpx
}

.icon-checked.data-v-921ef98a {
    background: var(--color-skyblue-2);
    border-radius: 0 0 0 24rpx;
    height: 56rpx;
    position: absolute;
    right: 0;
    top: 0;
    width: 56rpx
}

.hd.data-v-e92ae3ca {
    color: var(--color-text-subtitle);
    font-size: 32rpx;
    height: 110rpx;
    line-height: 46rpx;
    padding: 0 40rpx
}

.triangle.data-v-e92ae3ca {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
    transition: -webkit-transform .5s;
    transition: transform .5s;
    transition: transform .5s,-webkit-transform .5s
}

.triangle.is-dropdown.data-v-e92ae3ca {
    -webkit-transform: rotate(-180deg);
    transform: rotate(-180deg)
}

.store.data-v-e92ae3ca {
    padding: 0 40rpx 32rpx
}

.bd.data-v-e92ae3ca {
    height: 2rpx;
    overflow: hidden;
    transition: height .5s
}

.bd.data-v-e92ae3ca::after {
    background: var(--color-divider-line);
    bottom: 0rpx;
    content: "";
    height: 2rpx;
    left: 40rpx;
    position: absolute;
    right: 40rpx
}

.bd.is-dropdown.data-v-e92ae3ca {
    height: auto
}
