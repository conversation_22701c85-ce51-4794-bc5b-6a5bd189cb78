<comp-page bind:__l="__l" class="vue-ref" data-ref="page" ftZindex="{{999}}" isBrand="{{false}}" isNavbar="{{false}}" mainBackground="#F5F5F5" mainIsCover="{{true}}" navbarIsBack="{{false}}" navbarIsTransparent="{{true}}" navbarTitle="{{pageTitle}}" vueId="2750c018-1" vueSlots="{{['default']}}">
    <view class="header-area">
        <image class="header-image" mode="widthFix" src="https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/pic_头图@2x.png"></image>
        <view class="countdown-section">
            <view class="countdown-container">
                <view class="countdown-item">
                    <text class="countdown-label">{{countdownLabel}}</text>
                    <view class="countdown-time">
                        <text class="countdown-number">{{countdownDays}}</text>
                    </view>
                    <text class="countdown-unit">天</text>
                </view>
                <view class="countdown-item">
                    <text class="countdown-label"></text>
                    <view class="countdown-time">
                        <text class="countdown-number">{{countdownHours}}</text>
                    </view>
                    <text class="countdown-unit">时</text>
                </view>
                <view class="countdown-item">
                    <text class="countdown-label"></text>
                    <view class="countdown-time">
                        <text class="countdown-number">{{countdownMinutes}}</text>
                    </view>
                    <text class="countdown-unit">分</text>
                </view>
                <view class="countdown-item">
                    <text class="countdown-label"></text>
                    <view class="countdown-time">
                        <text class="countdown-number">{{countdownSeconds}}</text>
                    </view>
                    <text class="countdown-unit">秒</text>
                </view>
            </view>
        </view>
    </view>
    <view>
        <view class="activity-tabs-color">
            <view class="activity-tabs">
                <view bindtap="__e" class="{{['tab-item','tab-item-color',currentTabIndex===index?'active':'',$root.g0===1?'tab-item-single':'',item.g1?'tab-item-color-first-1':'',item.g2?'tab-item-color-first-2':'',item.g3?'tab-item-color-second-1':'',item.g4?'tab-item-color-second-2':'']}}" data-event-opts="{{[ [ 'tap',[ [ 'changeTab',[index] ] ] ] ]}}" wx:for="{{$root.l0}}" wx:key="index">
                    <view class="{{[$root.g5===1?'tab-item-single-inner':'',item.g6?'tab-item-second-2':'',item.g7?'tab-item-first-2':'',item.g8?'tab-item-second-1':'',item.g9?'tab-item-first-1':'']}}">
                        <text class="tab-title">{{item.m0}}</text>
                        <view class="tab-status">
                            <text class="tab-status-text">{{item.m1+' '+item.m2}}</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="product-list" wx:if="{{$root.g10>0}}">
            <view bindtap="__e" class="product-item" data-event-opts="{{[ [ 'tap',[ [ 'goToProductDetail',['$0'],[ [ ['productList','',index] ] ] ] ] ] ]}}" wx:for="{{$root.l1}}" wx:key="index">
                <view class="product-image-container">
                    <image class="product-image" src="{{item[$orig].activityImg}}"></image>x</view>
                <view class="product-info">
                    <text class="product-title">{{item[$orig].productName}}</text>
                    <view class="product-price-container">
                        <view class="product-price">
                            <text class="price-symbol">￥</text>
                            <text class="price-value">{{item[$orig].activityPrice}}</text>
                        </view>
                        <view class="product-save-price">
                            <view class="product-tag">
                                <text class="tag-text">{{'省'+item[$orig].basePrice-item[$orig].activityPrice+'元'}}</text>
                            </view>
                        </view>
                        <text class="product-original-price">{{'日常价¥'+item[$orig].basePrice}}</text>
                    </view>
                    <view catchtap="__e" class="{{['reservation-button',item.m3?'reservation-button.reserved':'']}}" data-event-opts="{{[ [ 'tap',[ [ 'e0',['$event'] ] ] ] ]}}" data-event-params="{{({ item:item[$orig] })}}" wx:if="{{!item[$orig].begin}}">
                        <text class="reservation-text">{{'限量'+item[$orig].stockNum+'件 限时抢购'}}</text>
                    </view>
                    <view class="{{['product-progress',item[$orig].productPercent>=1?'reservation-button.disabled':'']}}" wx:else>
                        <view class="progress-bar" wx:if="{{item[$orig].productPercent<1}}">
                            <view class="progress-inner" style="{{'width:'+item[$orig].productPercent*100+'%'+';'}}"></view>
                        </view>
                        <text class="progress-text">{{item[$orig].productPercent>=1?'商品被抢光啦~':'已抢'+item.g11+'%'}}</text>
                    </view>
                </view>
            </view>
        </view>
        <view class="empty-state" wx:else>
            <image class="empty-image" mode="widthFix" src="https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/pic_缺省页@2x.png"></image>
            <text class="empty-text">一大波福利商品采购中~</text>
            <text class="empty-text">请关注【创维电视+】公众号或【创维会员中心】小程序</text>
            <text class="empty-text">福利活动敬请期待…</text>
        </view>
        <view class="loading-more" wx:if="{{$root.g12>0}}">
            <view class="loading-text" wx:if="{{isLoading}}">
                <text>加载中...</text>
            </view>
            <block wx:else>
                <view class="loading-text" wx:if="{{hasMore}}">
                    <text>上拉加载更多</text>
                </view>
                <view class="loading-text" wx:else>
                    <text>没有更多数据了</text>
                </view>
            </block>
        </view>
    </view>
</comp-page>
