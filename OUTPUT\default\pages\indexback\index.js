require("../../@babel/runtime/helpers/Arrayincludes");
(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/indexback/index"], {
    2410: function _(e, t, n) {


      n.d(t, "b", function() {
        return r;
      }), n.d(t, "c", function() {
        return i;
      }), n.d(t, "a", function() {
        return a;
      });
      var a = {
          compPage: function compPage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-page/comp-page")]).then(n.bind(null, "fccb"));
          },
          compEntryTip: function compEntryTip() {
            return n.e("components/comp-entry-tip/comp-entry-tip").then(n.bind(null, "518e"));
          }
        },
        r = function r() {
          var e = this.$createElement,
            t = (this._self._c, this.adData.list.length),
            n = this.hpaList.length;
          this.$mp.data = Object.assign({}, {
            $root: {
              g0: t,
              g1: n
            }
          });
        },
        i = [];
    },
    "5cc0": function cc0(e, t, n) {


      n.r(t);
      var a = n("73ce"),
        r = n.n(a);
      for (var i in a)["default"].indexOf(i) < 0 && function(e) {
        n.d(t, e, function() {
          return a[e];
        });
      }(i);
      t["default"] = r.a;
    },
    "73ce": function ce(e, t, n) {


      (function(e) {
        var a = n("47a9");
        Object.defineProperty(t, "__esModule", {
          value: !0
        }), t.default = void 0;
        var r = a(n("7eb4")),
          i = a(n("ee10")),
          u = a(n("7ca3")),
          c = n("8f59"),
          s = a(n("bdc5")),
          o = a(n("8b9c")),
          d = a(n("c148")),
          l = a(n("a23b")),
          f = a(n("5e82")),
          p = n("b3c5");

        function g(e, t) {
          var n = Object.keys(e);
          if (Object.getOwnPropertySymbols) {
            var a = Object.getOwnPropertySymbols(e);
            t && (a = a.filter(function(t) {
              return Object.getOwnPropertyDescriptor(e, t).enumerable;
            })), n.push.apply(n, a);
          }
          return n;
        }

        function h(e) {
          for (var t = 1; t < arguments.length; t++) {
            var n = null != arguments[t] ? arguments[t] : {};
            t % 2 ? g(Object(n), !0).forEach(function(t) {
              (0, u.default)(e, t, n[t]);
            }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : g(Object(n)).forEach(function(t) {
              Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
            });
          }
          return e;
        }
        var m = (0, p.getConfig)(),
          v = {
            components: {
              PageHeader: function PageHeader() {
                n.e("pages/indexback/page-header").then(function() {
                  return resolve(n("75888"));
                }.bind(null, n)).catch(n.oe);
              },
              PageNav: function PageNav() {
                n.e("pages/indexback/page-nav").then(function() {
                  return resolve(n("913e1"));
                }.bind(null, n)).catch(n.oe);
              },
              PageAd: function PageAd() {
                n.e("pages/indexback/page-ad").then(function() {
                  return resolve(n("b466"));
                }.bind(null, n)).catch(n.oe);
              },
              CompPointAct: function CompPointAct() {
                n.e("pages/indexback/comp-pointAct").then(function() {
                  return resolve(n("8d58"));
                }.bind(null, n)).catch(n.oe);
              },
              CompPicShare: function CompPicShare() {
                n.e("pages/indexback/comp-pic-share").then(function() {
                  return resolve(n("32f8"));
                }.bind(null, n)).catch(n.oe);
              },
              CompActList: function CompActList() {
                n.e("pages/indexback/comp-act-list").then(function() {
                  return resolve(n("d4e0"));
                }.bind(null, n)).catch(n.oe);
              },
              PageGoods: function PageGoods() {
                n.e("pages/indexback/page-goods").then(function() {
                  return resolve(n("3ce5"));
                }.bind(null, n)).catch(n.oe);
              },
              PageGuide: function PageGuide() {
                n.e("pages/indexback/page-guide").then(function() {
                  return resolve(n("e4d3"));
                }.bind(null, n)).catch(n.oe);
              },
              PageCourse: function PageCourse() {
                n.e("pages/indexback/page-course").then(function() {
                  return resolve(n("9fe3"));
                }.bind(null, n)).catch(n.oe);
              }
            },
            data: function data() {
              return {
                jumpNavIndex: "",
                isProd: !1,
                isFirst: !0,
                packets: {
                  type: "open",
                  userInfo: {}
                },
                headerData: {
                  signTokenUrl: "",
                  banner: [],
                  score: 0
                },
                picList: [],
                hpaList: [],
                navsData: {
                  navs: [{
                    id: "fwdt",
                    img: "home1-fwdt",
                    name: "服务大厅",
                    url: "/pages/service/proxy"
                  }, {
                    id: "lgjz",
                    img: "home1-lgjz",
                    name: "灵感家装",
                    url: "/pages-picture/picture/all-category"
                  }, {
                    id: "dzbxk",
                    img: "home1-dzbxk",
                    name: "电子保修卡",
                    url: "/pages-guarantee/device/device",
                    openType: "getUserInfo"
                  }, {
                    id: "syjc",
                    img: "home1-syjc",
                    name: "使用教程",
                    url: "/pages-course/course/course"
                  }, {
                    id: "hksc",
                    img: "home1-hksc",
                    name: "回馈商城"
                  }, {
                    id: "xtsms",
                    img: "home1-xtsms",
                    name: "系统说明书"
                  }, {
                    id: "tsjy",
                    img: "home1-tsjy",
                    name: "投诉建议",
                    url: "/pages-help/complaintAndSuggest/complaintAndSuggest"
                  }, {
                    id: "bzzx",
                    img: "home1-bzzx",
                    name: "帮助中心",
                    url: "/pages-help/help/help"
                  }]
                },
                taskData: {
                  banner: [],
                  list: []
                },
                adData: {
                  list: []
                },
                goodsData: {
                  goodsTokenUrl: "",
                  list: []
                },
                guideData: {
                  list: []
                }
              };
            },
            onLoad: function onLoad(e) {
              var t = e.jumpNavIndex,
                n = e.jumpPageName;
              this.jumpNavIndex = t, this.jumpPageName = n;
            },
            computed: h({}, (0, c.mapState)(["profile", "isPackets", "memberCenterSwitch"])),
            mounted: function mounted() {
              this.jumpPageName ? f.default.to({
                page: this.jumpPageName
              }) : this.checkIndexGray();
            },
            onShow: function onShow() {
              var e = this;
              return (0, i.default)(r.default.mark(function t() {
                return r.default.wrap(function(t) {
                  while (1) switch (t.prev = t.next) {
                    case 0:
                      if (e.isFirst) {
                        t.next = 5;
                        break;
                      }
                      return e.getTask(), t.next = 4, s.default.getProfile();
                    case 4:
                      e.headerData.score = l.default.get(e.profile, "score", 0);
                    case 5:
                    case "end":
                      return t.stop();
                  }
                }, t);
              }))();
            },
            onShareAppMessage: function onShareAppMessage() {
              return l.default.generateShareInfo();
            },
            methods: h(h({}, (0, c.mapMutations)(["assign"])), {}, {
              checkIndexGray: function checkIndexGray() {
                var t = this;
                return (0, i.default)(r.default.mark(function n() {
                  return r.default.wrap(function(n) {
                    while (1) switch (n.prev = n.next) {
                      case 0:
                        return n.next = 2, s.default.getAppSwitchs();
                      case 2:
                        t.memberCenterSwitch.indexGray && (t.navbarBackground = "gray", e.setTabBarItem({
                          index: 0,
                          selectedIconPath: "static/icon-home-gray.png"
                        }), e.setTabBarStyle({
                          selectedColor: "#515151"
                        }));
                      case 3:
                      case "end":
                        return n.stop();
                    }
                  }, n);
                }))();
              },
              previousFetch: function previousFetch() {
                var e = this;
                return (0, i.default)(r.default.mark(function t() {
                  return r.default.wrap(function(t) {
                    while (1) switch (t.prev = t.next) {
                      case 0:
                        if (!e.profile) {
                          t.next = 3;
                          break;
                        }
                        return t.next = 3, s.default.getProfile();
                      case 3:
                      case "end":
                        return t.stop();
                    }
                  }, t);
                }))();
              },
              fetch: function fetch() {
                return Promise.all([this.getIndexNav(), this.getBanner(), this.getAward(), this.getTask(), this.getAd(), this.getGuide(), this.queryIndexData()]);
              },
              afterFetch: function afterFetch() {
                this.headerData.score = l.default.get(this.profile, "score", 0), this.isFirst = !1;
              },
              getIndexNav: function getIndexNav() {
                var e = this;
                return (0, i.default)(r.default.mark(function t() {
                  var n, a, i, u, c;
                  return r.default.wrap(function(t) {
                    while (1) switch (t.prev = t.next) {
                      case 0:
                        return t.next = 2, o.default.indexNav();
                      case 2:
                        n = t.sent, a = l.default.get(n, "data.data.storeTokenUrl", ""), i = l.default.get(n, "data.data.goodsTokenUrl", ""), u = l.default.get(n, "data.data.signTokenUrl", ""), c = e.navsData.navs.map(function(t, n) {
                          return e.jumpNavIndex == n + 1 && e.clickNav(t), t;
                        }), e.storeTokenUrl = a, e.navsData.navs = c, e.headerData.signTokenUrl = u, e.goodsData.goodsTokenUrl = i;
                      case 11:
                      case "end":
                        return t.stop();
                    }
                  }, t);
                }))();
              },
              getBanner: function getBanner() {
                var e = this;
                return (0, i.default)(r.default.mark(function t() {
                  var n, a, i;
                  return r.default.wrap(function(t) {
                    while (1) switch (t.prev = t.next) {
                      case 0:
                        return n = ["/pages-activity/eleTradein/index", "/pages-activity/comment/index"], t.next = 3, o.default.banner({
                          position: "bnindextop"
                        });
                      case 3:
                        a = t.sent, i = l.default.get(a, "data.data", []) || [], e.headerData.banner = i.map(function(e) {
                          return n.includes(e.url) ? e.openType = "" : e.openType = "getUserInfo", e;
                        });
                      case 6:
                      case "end":
                        return t.stop();
                    }
                  }, t);
                }))();
              },
              queryIndexData: function queryIndexData() {
                var e = this;
                return (0, i.default)(r.default.mark(function t() {
                  var n;
                  return r.default.wrap(function(t) {
                    while (1) switch (t.prev = t.next) {
                      case 0:
                        return t.next = 2, d.default.queryIndexData({
                          miniProgramId: m.appId
                        });
                      case 2:
                        n = t.sent, e.picList = l.default.get(n, "data.data.picList", []) || [], e.hpaList = l.default.get(n, "data.data.hpaList", []) || [];
                      case 5:
                      case "end":
                        return t.stop();
                    }
                  }, t);
                }))();
              },
              getAward: function getAward() {
                var e = this;
                return (0, i.default)(r.default.mark(function t() {
                  var n;
                  return r.default.wrap(function(t) {
                    while (1) switch (t.prev = t.next) {
                      case 0:
                        return t.next = 2, o.default.awardList({
                          type: "index"
                        });
                      case 2:
                        n = t.sent, e.goodsData.list = l.default.get(n, "data.data", []) || [];
                      case 4:
                      case "end":
                        return t.stop();
                    }
                  }, t);
                }))();
              },
              getTask: function getTask() {
                var e = this;
                return (0, i.default)(r.default.mark(function t() {
                  var n;
                  return r.default.wrap(function(t) {
                    while (1) switch (t.prev = t.next) {
                      case 0:
                        return t.next = 2, o.default.indexTask();
                      case 2:
                        n = t.sent, e.taskData.banner = l.default.get(n, "data.data.banner", []) || [], e.taskData.list = l.default.get(n, "data.data.list", []) || [];
                      case 5:
                      case "end":
                        return t.stop();
                    }
                  }, t);
                }))();
              },
              getAd: function getAd() {
                var e = this;
                return (0, i.default)(r.default.mark(function t() {
                  var n;
                  return r.default.wrap(function(t) {
                    while (1) switch (t.prev = t.next) {
                      case 0:
                        return t.next = 2, o.default.banner({
                          position: "bnindexmid"
                        });
                      case 2:
                        n = t.sent, e.adData.list = l.default.get(n, "data.data", []) || [];
                      case 4:
                      case "end":
                        return t.stop();
                    }
                  }, t);
                }))();
              },
              getCourse: function getCourse() {
                return (0, i.default)(r.default.mark(function e() {
                  return r.default.wrap(function(e) {
                    while (1) switch (e.prev = e.next) {
                      case 0:
                      case "end":
                        return e.stop();
                    }
                  }, e);
                }))();
              },
              getGuide: function getGuide() {
                var e = this;
                return (0, i.default)(r.default.mark(function t() {
                  var n;
                  return r.default.wrap(function(t) {
                    while (1) switch (t.prev = t.next) {
                      case 0:
                        return t.next = 2, o.default.banner({
                          position: "bnrectop"
                        });
                      case 2:
                        n = t.sent, e.guideData.list = l.default.get(n, "data.data", []) || [];
                      case 4:
                      case "end":
                        return t.stop();
                    }
                  }, t);
                }))();
              },
              clickPointItem: function clickPointItem(e) {
                1 == e ? f.default.toWebviewByTokenUrl({
                  tokenUrl: this.storeTokenUrl
                }) : 2 == e ? f.default.to({
                  page: "taskCenter",
                  query: {}
                }) : f.default.toWebviewByTokenUrl({
                  tokenUrl: m.welfareCenter
                });
              },
              clickNav: function clickNav(t) {
                return (0, i.default)(r.default.mark(function n() {
                  return r.default.wrap(function(n) {
                    while (1) switch (n.prev = n.next) {
                      case 0:
                        if ("hksc" !== t.id) {
                          n.next = 3;
                          break;
                        }
                        return e.switchTab({
                          url: "/pages/seckill/flashsale"
                        }), n.abrupt("return");
                      case 3:
                        if ("jxhw" !== t.id) {
                          n.next = 6;
                          break;
                        }
                        return f.default.toMp({
                          name: "goods"
                        }), n.abrupt("return");
                      case 6:
                        if ("lgjz" === t.id && f.default.to({
                            fullPath: "/pages-picture/picture/all-category"
                          }), "yjhx" === t.id && (t.url = "/pages-activity/jingdong/show?activityId=5"), "tsjy" === t.id && (t.url = "/pages-help/complaintAndSuggest/complaintAndSuggest"), "cxcs" === t.id && e.setStorageSync("productList", "[]"), "dzsms" !== t.id) {
                          n.next = 13;
                          break;
                        }
                        return f.default.to({
                          page: "webviewH5",
                          query: {
                            url: m.instructionBookUrl
                          }
                        }), n.abrupt("return");
                      case 13:
                        if ("xtsms" !== t.id) {
                          n.next = 16;
                          break;
                        }
                        return f.default.to({
                          page: "webviewH5",
                          query: {
                            url: m.instructionBookUrl
                          }
                        }), n.abrupt("return");
                      case 16:
                        "fwdj" === t.id && f.default.toMp({
                          name: "serviceHome"
                        });
                      case 17:
                      case "end":
                        return n.stop();
                    }
                  }, n);
                }))();
              },
              authClickService: function authClickService(e) {
                f.default.to({
                  page: "webviewH5",
                  query: {
                    url: encodeURIComponent(m.imUrl)
                  }
                });
              },
              packetsClose: function packetsClose() {
                this.assign({
                  isPackets: !1
                });
              },
              packetsOpenClickMain: function packetsOpenClickMain(e) {
                this.packets.userInfo = e.detail, this.packets.type = "reward";
              }
            })
          };
        t.default = v;
      }).call(this, n("df3c")["default"]);
    },
    c0f6: function c0f6(e, t, n) {


      (function(e, t) {
        var a = n("47a9");
        n("5c38");
        a(n("3240"));
        var r = a(n("e441"));
        e.__webpack_require_UNI_MP_PLUGIN__ = n, t(r.default);
      }).call(this, n("3223")["default"], n("df3c")["createPage"]);
    },
    e441: function e441(e, t, n) {


      n.r(t);
      var a = n("2410"),
        r = n("5cc0");
      for (var i in r)["default"].indexOf(i) < 0 && function(e) {
        n.d(t, e, function() {
          return r[e];
        });
      }(i);
      var u = n("828b"),
        c = Object(u["a"])(r["default"], a["b"], a["c"], !1, null, null, null, !1, a["a"], void 0);
      t["default"] = c.exports;
    }
  },
  [
    ["c0f6", "common/runtime", "common/vendor"]
  ]
]);