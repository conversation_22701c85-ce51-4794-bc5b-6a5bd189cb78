(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["components/comp-packets-result-wrap/comp-packets-result-wrap"], {
    "3d56": function d56(t, n, e) {


      var u = e("4449"),
        c = e.n(u);
      c.a;
    },
    4449: function _(t, n, e) {},
    "6e9c": function e9c(t, n, e) {


      e.r(n);
      var u = e("8922"),
        c = e("87fd");
      for (var o in c)["default"].indexOf(o) < 0 && function(t) {
        e.d(n, t, function() {
          return c[t];
        });
      }(o);
      e("3d56");
      var r = e("828b"),
        i = Object(r["a"])(c["default"], u["b"], u["c"], !1, null, "036706cd", null, !1, u["a"], void 0);
      n["default"] = i.exports;
    },
    "87fd": function fd(t, n, e) {


      e.r(n);
      var u = e("c5d9"),
        c = e.n(u);
      for (var o in u)["default"].indexOf(o) < 0 && function(t) {
        e.d(n, t, function() {
          return u[t];
        });
      }(o);
      n["default"] = c.a;
    },
    8922: function _(t, n, e) {


      e.d(n, "b", function() {
        return c;
      }), e.d(n, "c", function() {
        return o;
      }), e.d(n, "a", function() {
        return u;
      });
      var u = {
          compButton: function compButton() {
            return Promise.all([e.e("common/vendor"), e.e("components/comp-button/comp-button")]).then(e.bind(null, "ca5a"));
          }
        },
        c = function c() {
          var t = this.$createElement;
          this._self._c;
        },
        o = [];
    },
    c5d9: function c5d9(t, n, e) {


      Object.defineProperty(n, "__esModule", {
        value: !0
      }), n.default = void 0;
      var u = {
        props: {
          btnOpenType: {
            type: String,
            default: ""
          },
          btn: {
            type: Object,
            default: function _default() {
              return null;
            }
          }
        },
        data: function data() {
          return {};
        },
        methods: {
          clickBtn: function clickBtn() {
            this.$emit("clickBtn", this.btn);
          }
        }
      };
      n.default = u;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['components/comp-packets-result-wrap/comp-packets-result-wrap-create-component', {
    'components/comp-packets-result-wrap/comp-packets-result-wrap-create-component': function componentsCompPacketsResultWrapCompPacketsResultWrapCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("6e9c"));
    }
  },
  [
    ['components/comp-packets-result-wrap/comp-packets-result-wrap-create-component']
  ]
]);