GET /api/article/getChildArticleColumnListByLabel/policy_fees HTTP/1.1
Host: uc-admin.skyallhere.com
Connection: keep-alive
App-Path: /pages/service/service
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
Content-Type: application/json
App-System: Windows 10 x64
xweb_xhr: 1
App-Model: microsoft
App-Sdkversion: 3.8.12
App-Version: 3.9.12
Accept: */*
Sec-Fetch-Site: cross-site
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
Referer: https://servicewechat.com/wxff438d3c60c63fb6/342/page-frame.html
Accept-Encoding: gzip, deflate, br
Accept-Language: zh-CN,zh;q=0.9



HTTP/1.1 200
Date: Fri, 01 Aug 2025 14:56:25 GMT
Content-Type: application/json
Transfer-Encoding: chunked
Connection: keep-alive
Access-Control-Allow-Origin: *
Access-Control-Allow-Credentials: true

{"code":"200","data":[{"id":71,"columnName":"服务政策","columnIcon":"https://yonghuyunying-obs.skyallhere.com/member-center/icon/ee0f966f-7402-4344-934f-47935240c0ff.png","columnLabel":"Service_Policy","isShow":1,"backgroundColor":null,"articleType":null,"productCategoryId":5,"pcolumnName":null},{"id":72,"columnName":"安装收费标准","columnIcon":"https://yonghuyunying-obs.skyallhere.com/member-center/icon/218c31e2-e5cd-418c-90d7-f469278f3821.png","columnLabel":"Install_Fee_Standard","isShow":1,"backgroundColor":null,"articleType":null,"productCategoryId":5,"pcolumnName":null},{"id":73,"columnName":"维修收费标准","columnIcon":"https://yonghuyunying-obs.skyallhere.com/member-center/icon/31746468-d86e-41be-a9a3-5fd3abdfd42e.png","columnLabel":"Maintenance_Fee_Standard","isShow":1,"backgroundColor":null,"articleType":null,"productCategoryId":5,"pcolumnName":null}],"msg":"ok","success":true}