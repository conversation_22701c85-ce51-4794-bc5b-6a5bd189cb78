(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/service/service"], {
    "0363": function _(e, t, n) {


      n.d(t, "b", function() {
        return c;
      }), n.d(t, "c", function() {
        return o;
      }), n.d(t, "a", function() {
        return r;
      });
      var r = {
          compPage: function compPage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-page/comp-page")]).then(n.bind(null, "fccb"));
          }
        },
        c = function c() {
          var e = this.$createElement;
          this._self._c;
        },
        o = [];
    },
    5976: function _(e, t, n) {},
    "6bd1": function bd1(e, t, n) {


      (function(e, t) {
        var r = n("47a9");
        n("5c38");
        r(n("3240"));
        var c = r(n("d51e"));
        e.__webpack_require_UNI_MP_PLUGIN__ = n, t(c.default);
      }).call(this, n("3223")["default"], n("df3c")["createPage"]);
    },
    8894: function _(e, t, n) {


      n.r(t);
      var r = n("ba6d"),
        c = n.n(r);
      for (var o in r)["default"].indexOf(o) < 0 && function(e) {
        n.d(t, e, function() {
          return r[e];
        });
      }(o);
      t["default"] = c.a;
    },
    "951c7": function c7(e, t, n) {


      var r = n("5976"),
        c = n.n(r);
      c.a;
    },
    ba6d: function ba6d(e, t, n) {


      var r = n("47a9");
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.default = void 0;
      var c = r(n("7eb4")),
        o = r(n("ee10")),
        a = (r(n("a23b")), r(n("9f94")), r(n("c148"))),
        s = {
          data: function data() {
            return {
              prearrangeBtnList: [{
                title: "一键报装",
                icon: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/server_installed.png",
                url: "/pages-product/appoint/install?redirect=worksheet"
              }, {
                title: "一键报修",
                icon: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/server_repair.png",
                url: "/pages-product/appoint/repair?redirect=worksheet"
              }, {
                title: "服务进度",
                icon: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/server_progress.png",
                url: "/pages-product/worksheet/worksheet?check=1"
              }],
              policyBtnList: [],
              serverBtnList: [{
                title: "经销商报单",
                icon: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/server_customerOrderSubmission.png",
                url: "/pages-product/appoint/repair?redirect=worksheet&groupType=1"
              }, {
                title: "上门勘测",
                icon: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/server_onSiteSurvey.png",
                url: "/pages-product/appoint/survey?redirect=worksheet"
              }, {
                title: "服务百宝箱",
                icon: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/server_treasureBox.png",
                url: "/pages-product/serviceBox/serviceBox"
              }, {
                title: "投诉建议",
                icon: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/server_complaintsAndSuggestions.png",
                url: "/pages-help/complaintAndSuggest/complaintAndSuggest"
              }, {
                title: "在线客服",
                icon: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/server_customerService.png",
                url: "/pages/webview/webviewKF"
              }, {
                title: "联系我们",
                icon: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/server_contactUs.png",
                type: "contactUs"
              }]
            };
          },
          components: {
            PageNav: function PageNav() {
              n.e("pages/service/page-nav").then(function() {
                return resolve(n("da5f"));
              }.bind(null, n)).catch(n.oe);
            },
            Card: function Card() {
              n.e("pages/service/components/Card").then(function() {
                return resolve(n("0a8e"));
              }.bind(null, n)).catch(n.oe);
            },
            CardButtons: function CardButtons() {
              n.e("pages/service/components/CardButtons").then(function() {
                return resolve(n("da80"));
              }.bind(null, n)).catch(n.oe);
            }
          },
          methods: {
            fetch: function fetch() {
              return Promise.all([this.getPolicyFeesStandars()]);
            },
            getPolicyFeesStandars: function getPolicyFeesStandars() {
              var e = this;
              return (0, o.default)(c.default.mark(function t() {
                var n;
                return c.default.wrap(function(t) {
                  while (1) switch (t.prev = t.next) {
                    case 0:
                      return t.prev = 0, t.next = 3, a.default.getPolicyFeesCategories("policy_fees");
                    case 3:
                      n = t.sent, n.data.data && n.data.data.length > 0 && (e.policyBtnList = n.data.data.map(function(e) {
                        var t = "";
                        switch (e.columnLabel) {
                          case "Install_Fee_Standard":
                            t = "/pages-product/policyFees/installFees";
                            break;
                          case "Maintenance_Fee_Standard":
                            t = "/pages-product/policyFees/repairFees";
                            break;
                          default:
                            t = "/pages-product/policyFees/servicePolicy";
                            break;
                        }
                        return {
                          id: e.id,
                          icon: e.columnIcon,
                          title: e.columnName,
                          url: "".concat(t, "?navName=").concat(e.columnName, "&navId=").concat(e.id)
                        };
                      })), t.next = 10;
                      break;
                    case 7:
                      t.prev = 7, t.t0 = t["catch"](0), console.error("获取收费标准失败：", t.t0);
                    case 10:
                    case "end":
                      return t.stop();
                  }
                }, t, null, [
                  [0, 7]
                ]);
              }))();
            }
          }
        };
      t.default = s;
    },
    d51e: function d51e(e, t, n) {


      n.r(t);
      var r = n("0363"),
        c = n("8894");
      for (var o in c)["default"].indexOf(o) < 0 && function(e) {
        n.d(t, e, function() {
          return c[e];
        });
      }(o);
      n("951c7");
      var a = n("828b"),
        s = Object(a["a"])(c["default"], r["b"], r["c"], !1, null, "5f095cf5", null, !1, r["a"], void 0);
      t["default"] = s.exports;
    }
  },
  [
    ["6bd1", "common/runtime", "common/vendor"]
  ]
]);