(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["components/comp-info-flow-card/comp-info-flow-card"], {
    3383: function _(t, e, n) {


      Object.defineProperty(e, "__esModule", {
        value: !0
      }), e.default = void 0;
      var o = {
        props: {
          type: {
            type: Number,
            default: 10
          },
          isVideo: {
            type: Boolean,
            default: !1
          },
          imgs: {
            type: Array,
            default: function _default() {
              return [];
            }
          },
          name: {
            type: String,
            default: ""
          },
          isCommentCount: {
            type: Boolean,
            default: !0
          },
          commentCount: {
            type: Number,
            default: 0
          },
          isLikedCount: {
            type: Boolean,
            default: !0
          },
          likedCount: {
            type: Number,
            default: 0
          },
          viewCount: {
            type: Number,
            default: 0
          },
          time: {
            type: String,
            default: ""
          },
          videoTime: {
            type: String,
            default: ""
          }
        },
        methods: {
          clickCard: function clickCard() {
            this.$emit("clickCard");
          }
        }
      };
      e.default = o;
    },
    6535: function _(t, e, n) {


      n.r(e);
      var o = n("3383"),
        u = n.n(o);
      for (var i in o)["default"].indexOf(i) < 0 && function(t) {
        n.d(e, t, function() {
          return o[t];
        });
      }(i);
      e["default"] = u.a;
    },
    "8b01": function b01(t, e, n) {},
    aaed: function aaed(t, e, n) {


      n.r(e);
      var o = n("d9f0"),
        u = n("6535");
      for (var i in u)["default"].indexOf(i) < 0 && function(t) {
        n.d(e, t, function() {
          return u[t];
        });
      }(i);
      n("fd49");
      var a = n("828b"),
        r = Object(a["a"])(u["default"], o["b"], o["c"], !1, null, "bfa596a0", null, !1, o["a"], void 0);
      e["default"] = r.exports;
    },
    d9f0: function d9f0(t, e, n) {


      n.d(e, "b", function() {
        return u;
      }), n.d(e, "c", function() {
        return i;
      }), n.d(e, "a", function() {
        return o;
      });
      var o = {
          compImage: function compImage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-image/comp-image")]).then(n.bind(null, "31bf"));
          }
        },
        u = function u() {
          var t = this.$createElement,
            e = (this._self._c, 10 === this.type ? this.imgs.length : null),
            n = 10 === this.type ? this.imgs.length : null;
          this.$mp.data = Object.assign({}, {
            $root: {
              g0: e,
              g1: n
            }
          });
        },
        i = [];
    },
    fd49: function fd49(t, e, n) {


      var o = n("8b01"),
        u = n.n(o);
      u.a;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['components/comp-info-flow-card/comp-info-flow-card-create-component', {
    'components/comp-info-flow-card/comp-info-flow-card-create-component': function componentsCompInfoFlowCardCompInfoFlowCardCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("aaed"));
    }
  },
  [
    ['components/comp-info-flow-card/comp-info-flow-card-create-component']
  ]
]);