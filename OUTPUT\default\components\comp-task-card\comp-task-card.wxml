<view class="comp-task-card flex-row data-v-5724aeea">
    <view class="hd data-v-5724aeea">
        <comp-image bind:__l="__l" class="data-v-5724aeea" height="80rpx" isServer="{{true}}" name="{{img}}" radius="50%" vueId="6d81bcfc-1" width="80rpx"></comp-image>
    </view>
    <view class="bd flex-one data-v-5724aeea">
        <view class="name nowrap data-v-5724aeea">{{name}}</view>
        <view class="subname nowrap data-v-5724aeea">{{subname}}</view>
    </view>
    <view class="ft flex-col col-center row-center data-v-5724aeea">
        <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-5724aeea" data-event-opts="{{[ [ '^getUserInfo',[ ['clickBtn'] ] ] ]}}" openType="getUserInfo" vueId="6d81bcfc-2" vueSlots="{{['default']}}">
            <view class="{{['btn','flex-col','row-center','col-center','data-v-5724aeea','is-status-'+status]}}">{{''+statusName+''}}</view>
        </comp-button>
    </view>
</view>
