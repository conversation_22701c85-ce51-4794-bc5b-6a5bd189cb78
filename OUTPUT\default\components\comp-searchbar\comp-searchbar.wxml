<view bindtap="__e" class="{{['comp-searchbar','flex-row','data-v-2b4d5ba8',isShadow?'is-shadow':'',isFocus?'is-focus':'']}}" data-event-opts="{{[ [ 'tap',[ [ 'clickWrap',['$event'] ] ] ] ]}}" style="{{'height:'+height+';'+'border-radius:'+borderRadius+';'+'background:'+background+';'+'border:'+border+';'}}">
    <view class="hd flex-col row-center col-center data-v-2b4d5ba8">
        <comp-icon bind:__l="__l" class="data-v-2b4d5ba8" color="#ccc" icon="iconsearch_o" size="44rpx" vueId="0b97cde2-1"></comp-icon>
    </view>
    <view class="bd flex-one flex-row data-v-2b4d5ba8">
        <input bindblur="__e" bindconfirm="__e" bindfocus="__e" bindinput="__e" class="input flex-one data-v-2b4d5ba8" data-event-opts="{{[ [ 'input',[ [ 'input',['$event'] ] ] ],[ 'focus',[ [ 'focus',['$event'] ] ] ],[ 'blur',[ [ 'blur',['$event'] ] ] ],[ 'confirm',[ [ 'confirm',['$event'] ] ] ] ]}}" disabled="{{disabled}}" placeholder="{{placeholder}}" placeholderClass="placeholder comp-searchbar__placeholder" type="search" value="{{keyword}}"></input>
        <view catchtap="__e" class="close flex-row col-center data-v-2b4d5ba8" data-event-opts="{{[ [ 'tap',[ [ 'clear',['$event'] ] ] ] ]}}" wx:if="{{$root.g0}}">
            <comp-icon bind:__l="__l" class="data-v-2b4d5ba8" color="#ccc" icon="iconclose" size="36rpx" vueId="0b97cde2-2"></comp-icon>
        </view>
    </view>
    <view class="ft data-v-2b4d5ba8"></view>
</view>
