(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/service/components/Card"], {
    "0a8e": function a8e(t, n, e) {


      e.r(n);
      var r = e("ccbd"),
        u = e("bc17");
      for (var c in u)["default"].indexOf(c) < 0 && function(t) {
        e.d(n, t, function() {
          return u[t];
        });
      }(c);
      e("16df");
      var a = e("828b"),
        i = Object(a["a"])(u["default"], r["b"], r["c"], !1, null, "3162dd58", null, !1, r["a"], void 0);
      n["default"] = i.exports;
    },
    1315: function _(t, n, e) {


      Object.defineProperty(n, "__esModule", {
        value: !0
      }), n.default = void 0;
      var r = {
        name: "Card",
        props: {
          title: {
            type: String,
            required: !0
          },
          marginTop: {
            type: String,
            default: "0"
          }
        }
      };
      n.default = r;
    },
    "16df": function df(t, n, e) {


      var r = e("701b"),
        u = e.n(r);
      u.a;
    },
    "701b": function b(t, n, e) {},
    bc17: function bc17(t, n, e) {


      e.r(n);
      var r = e("1315"),
        u = e.n(r);
      for (var c in r)["default"].indexOf(c) < 0 && function(t) {
        e.d(n, t, function() {
          return r[t];
        });
      }(c);
      n["default"] = u.a;
    },
    ccbd: function ccbd(t, n, e) {


      e.d(n, "b", function() {
        return r;
      }), e.d(n, "c", function() {
        return u;
      }), e.d(n, "a", function() {});
      var r = function r() {
          var t = this.$createElement;
          this._self._c;
        },
        u = [];
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/service/components/Card-create-component', {
    'pages/service/components/Card-create-component': function pagesServiceComponentsCardCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("0a8e"));
    }
  },
  [
    ['pages/service/components/Card-create-component']
  ]
]);