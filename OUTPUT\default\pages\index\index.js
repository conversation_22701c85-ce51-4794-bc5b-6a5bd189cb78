(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/index/index"], {
    "237c": function c(e, t, n) {


      (function(e) {
        var r = n("47a9");
        Object.defineProperty(t, "__esModule", {
          value: !0
        }), t.default = void 0;
        var o = r(n("7eb4")),
          a = r(n("ee10")),
          c = r(n("7ca3")),
          i = n("8f59"),
          u = r(n("5e82")),
          l = r(n("bdc5")),
          s = r(n("8b9c")),
          f = r(n("c148")),
          d = r(n("a23b"));

        function m(e, t) {
          var n = Object.keys(e);
          if (Object.getOwnPropertySymbols) {
            var r = Object.getOwnPropertySymbols(e);
            t && (r = r.filter(function(t) {
              return Object.getOwnPropertyDescriptor(e, t).enumerable;
            })), n.push.apply(n, r);
          }
          return n;
        }
        var p = {
          name: "<PERSON><PERSON><PERSON>",
          components: {
            compRecommand: function compRecommand() {
              n.e("pages/user/comp-recommand").then(function() {
                return resolve(n("def8"));
              }.bind(null, n)).catch(n.oe);
            },
            CompSkeleton: function CompSkeleton() {
              n.e("components/comp-page/comp-skeleton").then(function() {
                return resolve(n("4dba"));
              }.bind(null, n)).catch(n.oe);
            }
          },
          data: function data() {
            return {
              topGap: 44,
              currentLevel: 1,
              isFirst: !0,
              recommandList: [],
              storeTokenUrl: "",
              adData: [],
              levelMap: {
                1: "基础会员",
                2: "白银会员",
                3: "黄金会员",
                4: "钻石会员",
                5: "黑金会员"
              },
              levelList: [{
                id: 1,
                name: "基础会员"
              }, {
                id: 2,
                name: "白银会员"
              }, {
                id: 3,
                name: "黄金会员"
              }, {
                id: 4,
                name: "钻石会员"
              }, {
                id: 5,
                name: "黑金会员"
              }],
              benefits: [{
                icon: "hywd",
                text: "会员维豆",
                level: 1
              }, {
                icon: "gjswd",
                text: "产品维豆",
                level: 1
              }, {
                icon: "cpfkyl",
                text: "产品反馈有礼",
                level: 1
              }, {
                icon: "lyb",
                text: "0元领延保",
                level: 1
              }, {
                icon: "smcl",
                text: "上门设计",
                level: 2
              }, {
                icon: "qwkf",
                text: "企微客服",
                level: 2
              }, {
                icon: "dydjj",
                text: "电影代金券",
                level: 3
              }, {
                icon: "srhl",
                text: "生日好礼",
                level: 3
              }, {
                icon: "ykq",
                text: "免费遥控器",
                level: 4
              }, {
                icon: "mkf",
                text: "免费麦克风",
                level: 4
              }, {
                icon: "jdqx",
                text: "家电清洗",
                level: 5
              }, {
                icon: "yshyk",
                text: "影视会员年卡",
                level: 5
              }]
            };
          },
          computed: function(e) {
            for (var t = 1; t < arguments.length; t++) {
              var n = null != arguments[t] ? arguments[t] : {};
              t % 2 ? m(Object(n), !0).forEach(function(t) {
                (0, c.default)(e, t, n[t]);
              }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : m(Object(n)).forEach(function(t) {
                Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
              });
            }
            return e;
          }({}, (0, i.mapState)(["profile"])),
          onShow: function onShow() {
            var e = this;
            return (0, a.default)(o.default.mark(function t() {
              return o.default.wrap(function(t) {
                while (1) switch (t.prev = t.next) {
                  case 0:
                    if (e.isFirst) {
                      t.next = 3;
                      break;
                    }
                    return t.next = 3, l.default.getProfile();
                  case 3:
                  case "end":
                    return t.stop();
                }
              }, t);
            }))();
          },
          mounted: function mounted() {
            var t = this,
              n = 20;
            e.getSystemInfo({
              success: function success(r) {
                n = r.statusBarHeight;
                var o = e.getMenuButtonBoundingClientRect ? e.getMenuButtonBoundingClientRect() : null;
                t.topGap = o ? o.bottom + 8 : n + 44;
              }
            });
          },
          methods: {
            getAd: function getAd() {
              var e = this;
              return (0, a.default)(o.default.mark(function t() {
                var n;
                return o.default.wrap(function(t) {
                  while (1) switch (t.prev = t.next) {
                    case 0:
                      return t.next = 2, s.default.banner({
                        position: "bnindexmid"
                      });
                    case 2:
                      n = t.sent, e.adData = d.default.get(n, "data.data", []) || [];
                    case 4:
                    case "end":
                      return t.stop();
                  }
                }, t);
              }))();
            },
            toGetGrowth: function toGetGrowth() {
              e.navigateTo({
                url: "/pages-member/member/member"
              });
            },
            toMore: function toMore() {
              e.navigateTo({
                url: "/pages-user/more-auth/more-auth?level=" + this.currentLevel
              });
            },
            toAuthDetail: function toAuthDetail(t) {
              e.navigateTo({
                url: "/pages-user/user-level/rights?index=" + (t + 1)
              });
            },
            getWd: function getWd() {
              e.navigateTo({
                url: "/pages-user/get-wd/get-wd"
              });
            },
            getCzz: function getCzz() {
              e.navigateTo({
                url: "/pages-member/member/member"
              });
            },
            previousFetch: function previousFetch() {
              var e = this;
              return (0, a.default)(o.default.mark(function t() {
                return o.default.wrap(function(t) {
                  while (1) switch (t.prev = t.next) {
                    case 0:
                      if (!e.profile) {
                        t.next = 3;
                        break;
                      }
                      return t.next = 3, l.default.getProfile();
                    case 3:
                    case "end":
                      return t.stop();
                  }
                }, t);
              }))();
            },
            fetch: function fetch() {
              return this.profile && (this.currentLevel = this.profile.grade), Promise.all([this.getRecommandList(), this.getIndexNav(), this.getAd()]);
            },
            afterFetch: function afterFetch() {
              this.isFirst = !1;
            },
            getRecommandList: function getRecommandList() {
              var e = this;
              return (0, a.default)(o.default.mark(function t() {
                var n, r;
                return o.default.wrap(function(t) {
                  while (1) switch (t.prev = t.next) {
                    case 0:
                      if (e.profile) {
                        t.next = 2;
                        break;
                      }
                      return t.abrupt("return");
                    case 2:
                      return console.log(e.profile), t.next = 5, f.default.queryRecommandList({
                        pageNum: 1,
                        pageSize: 20,
                        bannerPosLabel: "bnrecommendedforyou"
                      });
                    case 5:
                      n = t.sent, r = d.default.get(n, "data.data.records", []), e.recommandList = r;
                    case 8:
                    case "end":
                      return t.stop();
                  }
                }, t);
              }))();
            },
            getIndexNav: function getIndexNav() {
              var e = this;
              return (0, a.default)(o.default.mark(function t() {
                var n, r;
                return o.default.wrap(function(t) {
                  while (1) switch (t.prev = t.next) {
                    case 0:
                      return t.next = 2, s.default.indexNav();
                    case 2:
                      n = t.sent, r = d.default.get(n, "data.data.storeTokenUrl", ""), e.storeTokenUrl = r;
                    case 5:
                    case "end":
                      return t.stop();
                  }
                }, t);
              }))();
            },
            jumpMore: function jumpMore() {
              u.default.toWebviewByTokenUrl({
                tokenUrl: this.storeTokenUrl
              });
            },
            changeCard: function changeCard(e) {
              var t = e.detail.current + 1;
              this.currentLevel = t;
            }
          }
        };
        t.default = p;
      }).call(this, n("df3c")["default"]);
    },
    "8fcc": function fcc(e, t, n) {},
    "96bc": function bc(e, t, n) {


      n.d(t, "b", function() {
        return o;
      }), n.d(t, "c", function() {
        return a;
      }), n.d(t, "a", function() {
        return r;
      });
      var r = {
          compPage: function compPage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-page/comp-page")]).then(n.bind(null, "fccb"));
          },
          compButton: function compButton() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-button/comp-button")]).then(n.bind(null, "ca5a"));
          },
          compIcon: function compIcon() {
            return n.e("components/comp-icon/comp-icon").then(n.bind(null, "84bb2"));
          }
        },
        o = function o() {
          var e = this.$createElement,
            t = (this._self._c, {
              color: 5 == this.currentLevel ? "rgba(255, 255, 255, 0.3)" : "rgba(0, 0, 0, 0.3)"
            }),
            n = this.recommandList.length;
          this.$mp.data = Object.assign({}, {
            $root: {
              a0: t,
              g0: n
            }
          });
        },
        a = [];
    },
    b0b8: function b0b8(e, t, n) {},
    d58d: function d58d(e, t, n) {


      (function(e, t) {
        var r = n("47a9");
        n("5c38");
        r(n("3240"));
        var o = r(n("e0c4"));
        e.__webpack_require_UNI_MP_PLUGIN__ = n, t(o.default);
      }).call(this, n("3223")["default"], n("df3c")["createPage"]);
    },
    de12: function de12(e, t, n) {


      n.r(t);
      var r = n("237c"),
        o = n.n(r);
      for (var a in r)["default"].indexOf(a) < 0 && function(e) {
        n.d(t, e, function() {
          return r[e];
        });
      }(a);
      t["default"] = o.a;
    },
    def2: function def2(e, t, n) {


      var r = n("8fcc"),
        o = n.n(r);
      o.a;
    },
    e0c4: function e0c4(e, t, n) {


      n.r(t);
      var r = n("96bc"),
        o = n("de12");
      for (var a in o)["default"].indexOf(a) < 0 && function(e) {
        n.d(t, e, function() {
          return o[e];
        });
      }(a);
      n("f6f3"), n("def2");
      var c = n("828b"),
        i = Object(c["a"])(o["default"], r["b"], r["c"], !1, null, "4fd79b5a", null, !1, r["a"], void 0);
      t["default"] = i.exports;
    },
    f6f3: function f6f3(e, t, n) {


      var r = n("b0b8"),
        o = n.n(r);
      o.a;
    }
  },
  [
    ["d58d", "common/runtime", "common/vendor"]
  ]
]);