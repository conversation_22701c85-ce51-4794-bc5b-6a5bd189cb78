.comp-skeleton.data-v-9a3e6efa {
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    z-index: 0
}

.animation.data-v-9a3e6efa {
    -webkit-animation: backpos-data-v-9a3e6efa .9s ease-in-out 0s infinite;
    animation: backpos-data-v-9a3e6efa .9s ease-in-out 0s infinite;
    background: linear-gradient(90deg,hsla(0,0%,100%,0),hsla(0,0%,100%,.5) 50%,hsla(0,0%,100%,0) 80%);
    background-repeat: no-repeat;
    background-size: 30% 100%;
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 1
}

.row.data-v-9a3e6efa {
    margin: 24rpx 40rpx
}

.line.data-v-9a3e6efa,.square.data-v-9a3e6efa {
    background: #ebebeb;
    margin: 24rpx 0
}

.line.data-v-9a3e6efa {
    height: 48rpx
}

.line--90.data-v-9a3e6efa {
    width: 90%
}

.line--80.data-v-9a3e6efa {
    width: 80%
}

.line--70.data-v-9a3e6efa {
    width: 70%
}

.line--50.data-v-9a3e6efa {
    width: 50%
}

.square.data-v-9a3e6efa {
    height: 242rpx;
    margin-right: 32rpx;
    width: 242rpx
}

.no-margin-top.data-v-9a3e6efa {
    margin-top: 0
}

.no-margin-bottom.data-v-9a3e6efa {
    margin-bottom: 0
}

@-webkit-keyframes backpos-data-v-9a3e6efa {
    from {
        background-position-x: -200px
    }

    to {
        background-position-x: calc(200px + 100%)
    }
}

@keyframes backpos-data-v-9a3e6efa {
    from {
        background-position-x: -200px
    }

    to {
        background-position-x: calc(200px + 100%)
    }
}