<view class="data-v-db973c74" slot="bd">
    <view class="page-header__bar flex-row col-center space-between data-v-db973c74">
        <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-db973c74" data-event-opts="{{[ [ '^getUserInfo',[ ['clickScore'] ] ] ]}}" openType="getUserInfo" vueId="05218cf6-1" vueSlots="{{['default']}}">
            <view class="page-header__score flex-row col-center data-v-db973c74">
                <view class="score__name data-v-db973c74">我的维豆</view>
                <view class="score__subname data-v-db973c74">{{score}}</view>
                <comp-icon bind:__l="__l" class="data-v-db973c74" color="#3088fe" icon="iconarrow_right_o" size="24rpx" vueId="{{'05218cf6-2'+','+'05218cf6-1'}}"></comp-icon>
            </view>
        </comp-button>
        <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-db973c74" data-event-opts="{{[ [ '^getUserInfo',[ ['clickSign'] ] ] ]}}" openType="getUserInfo" vueId="05218cf6-3" vueSlots="{{['default']}}">
            <view class="page-header__sign flex-row col-center row-center data-v-db973c74">
                <comp-image bind:__l="__l" class="data-v-db973c74" height="33rpx" name="home-coin" vueId="{{'05218cf6-4'+','+'05218cf6-3'}}" width="33rpx"></comp-image>
                <view class="sign__name data-v-db973c74">签到</view>
            </view>
        </comp-button>
    </view>
    <view class="banner flex-row space-between col-center data-v-db973c74">
        <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-db973c74" data-event-opts="{{[ [ '^getUserInfo',[ [ 'clickItem',[1] ] ] ] ]}}" openType="getUserInfo" vueId="05218cf6-5" vueSlots="{{['default']}}">
            <view class="advertise_left flex-col data-v-db973c74">
                <view class="title data-v-db973c74">维豆商城</view>
                <view class="sub-title data-v-db973c74">维豆兑换好礼</view>
            </view>
        </comp-button>
        <view class="advertise_right data-v-db973c74">
            <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-db973c74" data-event-opts="{{[ [ '^getUserInfo',[ [ 'clickItem',[2] ] ] ] ]}}" openType="getUserInfo" vueId="05218cf6-6" vueSlots="{{['default']}}">
                <view class="sub flex-col row-center right-top data-v-db973c74">
                    <view class="title data-v-db973c74">任务中心</view>
                    <view class="sub-title data-v-db973c74">完成任务领维豆</view>
                </view>
            </comp-button>
            <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-db973c74" data-event-opts="{{[ [ '^getUserInfo',[ [ 'clickItem',[3] ] ] ] ]}}" openType="getUserInfo" vueId="05218cf6-7" vueSlots="{{['default']}}">
                <view class="sub flex-col row-center right-bottom data-v-db973c74">
                    <view class="title data-v-db973c74">福利中心</view>
                    <view class="sub-title data-v-db973c74">超值兑换</view>
                </view>
            </comp-button>
        </view>
    </view>
</view>
