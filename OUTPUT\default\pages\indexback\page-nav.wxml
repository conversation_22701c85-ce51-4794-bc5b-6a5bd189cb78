<view class="page-nav flex-row flex-wrap data-v-df45385c">
    <comp-button bind:__l="__l" bind:getUserInfo="__e" bind:onClick="__e" class="page-nav__item data-v-df45385c" data-event-opts="{{[ [ '^getUserInfo',[ [ 'clickNav',['$0'],[ [ ['navs','',index] ] ] ] ] ],[ '^onClick',[ [ 'clickNav',['$0'],[ [ ['navs','',index] ] ] ] ] ] ]}}" openType="{{nav[$orig].openType}}" vueId="{{'9a70315c-1-'+index}}" vueSlots="{{['default']}}" wx:if="{{nav.m0}}" wx:for="{{$root.l0}}" wx:for-item="nav" wx:key="index">
        <view class="flex-col row-center col-center data-v-df45385c">
            <comp-image bind:__l="__l" class="data-v-df45385c" height="100rpx" name="{{nav[$orig].img}}" vueId="{{'9a70315c-2-'+index+','+'9a70315c-1-'+index}}" width="100rpx"></comp-image>
            <view class="page-nav__name data-v-df45385c">{{nav[$orig].name}}</view>
        </view>
    </comp-button>
</view>
