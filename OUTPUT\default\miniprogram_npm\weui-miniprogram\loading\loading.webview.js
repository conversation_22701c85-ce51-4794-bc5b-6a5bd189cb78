$gwx_XC_14=function(_,_v,_n,_p,_s,_wp,_wl,$gwn,$gwl,$gwh,wh,$gstack,$gwrt,gra,grb,TestTest,wfor,_ca,_da,_r,_rz,_o,_oz,_1,_1z,_2,_2z,_m,_mz,nv_getDate,nv_getRegExp,nv_console,nv_parseInt,nv_parseFloat,nv_isNaN,nv_isFinite,nv_decodeURI,nv_decodeURIComponent,nv_encodeURI,nv_encodeURIComponent,$gdc,nv_JSON,_af,_gv,_ai,_grp,_gd,_gapi,$ixc,_ic,_w,_ev,_tsd){return function(path,global){
if(typeof global==='undefined'){if (typeof __GWX_GLOBAL__==='undefined')global={};else global=__GWX_GLOBAL__;}var __WXML_GLOBAL__={};{
}__WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
var e_={}
if(typeof(global.entrys)==='undefined')global.entrys={};e_=global.entrys;
var d_={}
if(typeof(global.defines)==='undefined')global.defines={};d_=global.defines;
var f_={}
if(typeof(global.modules)==='undefined')global.modules={};f_=global.modules || {};
var p_={}
__WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
__WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
__WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
var z=__WXML_GLOBAL__.ops_set.$gwx_XC_14 || [];
__WXML_GLOBAL__.debuginfo_set = __WXML_GLOBAL__.debuginfo_set || {};
var debugInfo=__WXML_GLOBAL__.debuginfo_set.$gwx_XC_14 || [];
function gz$gwx_XC_14_1(){
if( __WXML_GLOBAL__.ops_cached.$gwx_XC_14_1)return __WXML_GLOBAL__.ops_cached.$gwx_XC_14_1
__WXML_GLOBAL__.ops_cached.$gwx_XC_14_1=[];
(function(z){var a=11;function Z(ops,debugLine){z.push(['11182016',ops,debugLine])}
Z([a,[3,'wx_loading_view '],[[2,'?:'],[[7],[3,'animated']],[1,'wx_loading_view__animated'],[1,'']],[3,' '],[[2,'?:'],[[2,'!'],[[7],[3,'show']]],[1,'wx_loading_view__hide'],[1,'']],[3,' '],[[7],[3,'extClass']]],['./miniprogram_npm/weui-miniprogram/loading/loading.wxml',1,12])
Z([3,'wx_loading_view'],['./miniprogram_npm/weui-miniprogram/loading/loading.wxml',1,209])
Z([[2,'?:'],[[7],[3,'animated']],[[2,'+'],[[2,'+'],[1,'transition: height '],[[7],[3,'duration']]],[1,'ms ease;']],[1,'']],['./miniprogram_npm/weui-miniprogram/loading/loading.wxml',1,138])
Z([[2,'==='],[[7],[3,'type']],[1,'dot-white']],['./miniprogram_npm/weui-miniprogram/loading/loading.wxml',1,239])
Z([3,'loading wx_dot_loading wx_dot_loading_white'],['./miniprogram_npm/weui-miniprogram/loading/loading.wxml',1,270])
Z([[2,'==='],[[7],[3,'type']],[1,'dot-gray']],['./miniprogram_npm/weui-miniprogram/loading/loading.wxml',1,337])
Z([3,'loading wx_dot_loading'],['./miniprogram_npm/weui-miniprogram/loading/loading.wxml',1,367])
Z([[2,'==='],[[7],[3,'type']],[1,'circle']],['./miniprogram_npm/weui-miniprogram/loading/loading.wxml',1,407])
Z([3,'weui-loadmore'],['./miniprogram_npm/weui-miniprogram/loading/loading.wxml',1,435])
Z([3,'weui-loading'],['./miniprogram_npm/weui-miniprogram/loading/loading.wxml',1,463])
Z([3,'weui-loadmore__tips'],['./miniprogram_npm/weui-miniprogram/loading/loading.wxml',1,491])
Z([a,[[7],[3,'tips']]],['./miniprogram_npm/weui-miniprogram/loading/loading.wxml',1,513])
})(__WXML_GLOBAL__.ops_cached.$gwx_XC_14_1);return __WXML_GLOBAL__.ops_cached.$gwx_XC_14_1
}
__WXML_GLOBAL__.ops_set.$gwx_XC_14=z;
__WXML_GLOBAL__.ops_init.$gwx_XC_14=true;
__WXML_GLOBAL__.debuginfo_set.$gwx_XC_14=debugInfo;
var x=['./miniprogram_npm/weui-miniprogram/loading/loading.wxml'];d_[x[0]]={}
var m0=function(e,s,r,gg){
var z=gz$gwx_XC_14_1()
var hAG=_mz(z,'view',['class',0,'id',1,'style',1],[],e,s,gg)
var oBG=_v()
_(hAG,oBG)
if(_oz(z,3,e,s,gg)){oBG.wxVkey=1
var cCG=_n('view')
_rz(z,cCG,'class',4,e,s,gg)
_(oBG,cCG)
}
else if(_oz(z,5,e,s,gg)){oBG.wxVkey=2
var oDG=_n('view')
_rz(z,oDG,'class',6,e,s,gg)
_(oBG,oDG)
}
else if(_oz(z,7,e,s,gg)){oBG.wxVkey=3
var lEG=_n('view')
_rz(z,lEG,'class',8,e,s,gg)
var aFG=_n('view')
_rz(z,aFG,'class',9,e,s,gg)
_(lEG,aFG)
var tGG=_n('view')
_rz(z,tGG,'class',10,e,s,gg)
var eHG=_oz(z,11,e,s,gg)
_(tGG,eHG)
_(lEG,tGG)
_(oBG,lEG)
}
oBG.wxXCkey=1
_(r,hAG)
return r
}
e_[x[0]]=e_[x[0]]||{f:m0,j:[],i:[],ti:[],ic:[]}
if(path&&e_[path]){
outerGlobal.__wxml_comp_version__=0.02
return function(env,dd,global){$gwxc=0;var root={"tag":"wx-page"};root.children=[]
;g="$gwx_XC_14";var main=e_[path].f
if (typeof global==="undefined")global={};global.f=$gdc(f_[path],"",1);
if(typeof(outerGlobal.__webview_engine_version__)!='undefined'&&outerGlobal.__webview_engine_version__+1e-6>=0.02+1e-6&&outerGlobal.__mergeData__)
{
env=outerGlobal.__mergeData__(env,dd);
}
try{
main(env,{},root,global);
_tsd(root)
if(typeof(outerGlobal.__webview_engine_version__)=='undefined'|| outerGlobal.__webview_engine_version__+1e-6<0.01+1e-6){return _ev(root);}
}catch(err){
console.log(err)
}
;g="";
return root;
}
}
}
}(__g.a,__g.b,__g.c,__g.d,__g.e,__g.f,__g.g,__g.h,__g.i,__g.j,__g.k,__g.l,__g.m,__g.n,__g.o,__g.p,__g.q,__g.r,__g.s,__g.t,__g.u,__g.v,__g.w,__g.x,__g.y,__g.z,__g.A,__g.B,__g.C,__g.D,__g.E,__g.F,__g.G,__g.H,__g.I,__g.J,__g.K,__g.L,__g.M,__g.N,__g.O,__g.P,__g.Q,__g.R,__g.S,__g.T,__g.U,__g.V,__g.W,__g.X,__g.Y,__g.Z,__g.aa);if(__vd_version_info__.delayedGwx||false)$gwx_XC_14();	if (__vd_version_info__.delayedGwx) __wxAppCode__['miniprogram_npm/weui-miniprogram/loading/loading.wxml'] = [$gwx_XC_14, './miniprogram_npm/weui-miniprogram/loading/loading.wxml'];else __wxAppCode__['miniprogram_npm/weui-miniprogram/loading/loading.wxml'] = $gwx_XC_14( './miniprogram_npm/weui-miniprogram/loading/loading.wxml' );
	
var noCss=typeof __vd_version_info__!=='undefined'&&__vd_version_info__.noCss===true;if(!noCss){__wxAppCode__['miniprogram_npm/weui-miniprogram/loading/loading.wxss'] = setCssToHead_wxfa43a4a7041a84de([".",[1],"wx_loading_view{display:-webkit-flex;display:flex;-webkit-justify-content:center;justify-content:center;-webkit-align-items:center;align-items:center;overflow:hidden}\n.",[1],"wx_loading_view__hide{height:0!important;display:none}\n.",[1],"wx_loading_view__animated.",[1],"wx_loading_view__hide{display:-webkit-flex;display:flex}\n.",[1],"loading{color:hsla(0,0%,100%,.9);font-size:17px;text-align:center}\n.",[1],"loading_view_translation{transition:height .2s ease .3s}\n",],undefined,{path:"./miniprogram_npm/weui-miniprogram/loading/loading.wxss"});
}