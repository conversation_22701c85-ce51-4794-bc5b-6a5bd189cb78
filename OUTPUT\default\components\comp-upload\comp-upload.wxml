<view class="comp-upload flex-row flex-wrap data-v-62ad5e56">
    <view class="data-v-62ad5e56" style="display:flex;flex-wrap:wrap;">
        <view bindtap="__e" class="btn img flex-row data-v-62ad5e56" data-event-opts="{{[ [ 'tap',[ [ 'preview',['$0'],[ [ ['imgs','',index] ] ] ] ] ] ]}}" style="{{'margin-right:'+marginRight+';'+'margin-bottom:'+marginBottom+';'+'width:'+width+';'+'border:'+border+';'+'border-radius:'+borderRadius+';'}}" wx:for="{{imgs}}" wx:key="index">
            <comp-image bind:__l="__l" class="data-v-62ad5e56" height="{{height}}" isServer="{{true}}" name="{{item}}" vueId="{{'54fce0fc-1-'+index}}" width="{{width}}"></comp-image>
            <view catchtap="__e" class="del data-v-62ad5e56" data-event-opts="{{[ [ 'tap',[ [ 'del',['$0'],[ [ ['imgs','',index] ] ] ] ] ] ]}}" wx:if="{{!disabled}}">
                <view class="del__main data-v-62ad5e56">
                    <comp-icon bind:__l="__l" class="data-v-62ad5e56" color="#ffffff" icon="iconclose_o" size="32rpx" vueId="{{'54fce0fc-2-'+index}}"></comp-icon>
                </view>
            </view>
        </view>
        <view bindtap="__e" class="btn add flex-col row-center col-center data-v-62ad5e56" data-event-opts="{{[ [ 'tap',[ [ 'upload',[false] ] ] ] ]}}" style="{{'margin-right:'+marginRight+';'+'margin-bottom:'+marginBottom+';'+'padding:'+addPadding+';'+'width:'+width+';'+'height:'+height+';'}}" wx:if="{{$root.g0}}">
            <comp-icon bind:__l="__l" class="data-v-62ad5e56" color="#ccc" icon="iconpicture_o" size="76rpx" vueId="54fce0fc-3"></comp-icon>
            <view class="add__name data-v-62ad5e56" wx:if="{{addName}}">{{addName}}</view>
            <view class="add__subname data-v-62ad5e56" wx:if="{{addSubname}}">{{addSubname}}</view>
        </view>
    </view>
    <comp-dialog activeSubname="{{activeSubname}}" bind:__l="__l" bind:authorization="__e" bind:clickActive="__e" bind:clickBtn="__e" class="data-v-62ad5e56 vue-ref" data-event-opts="{{[ [ '^clickBtn',[ ['handleClose'] ] ],[ '^authorization',[ ['handleAuthorization'] ] ],[ '^clickActive',[ ['handleActive'] ] ] ]}}" data-ref="refDialog" extBtnName="同意" extBtnOpenType="agreePrivacyAuthorization" name="用户隐私保护提示" subname="{{subname}}" subnameBottom="{{subnameBottom}}" vueId="54fce0fc-4"></comp-dialog>
</view>
