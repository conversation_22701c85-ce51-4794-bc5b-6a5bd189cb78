<page-panel bind:__l="__l" bind:clickMore="__e" class="page-pic-share data-v-b47df2ce" data-event-opts="{{[ [ '^clickMore',[ ['clickMore'] ] ] ]}}" isMore="{{true}}" title="家装分享" vueId="40d67f0e-1" vueSlots="{{['bd']}}">
    <view class="data-v-b47df2ce" slot="bd">
        <view class="pic-row data-v-b47df2ce">
            <swiper circular="{{true}}" class="data-v-b47df2ce" nextMargin="20rpx" previousMargin="20rpx" style="height:200px;">
                <swiper-item class="data-v-b47df2ce" wx:for="{{list}}" wx:key="index">
                    <comp-button bind:__l="__l" bind:onClick="__e" class="data-v-b47df2ce" data-event-opts="{{[ [ '^onClick',[ [ 'clickPic',['$0'],[ [ ['list','',index] ] ] ] ] ] ]}}" vueId="{{'40d67f0e-2-'+index+','+'40d67f0e-1'}}" vueSlots="{{['default']}}">
                        <view class="card data-v-b47df2ce" style="{{'background-image:'+'url('+item.tvWallImg+')'+';'+'background-size:'+'100%'+';'+'background-position:'+'center center'+';'}}">
                            <view catchtap="__e" class="more data-v-b47df2ce" data-event-opts="{{[ [ 'tap',[ [ 'clickContact',['$0'],[ [ ['list','',index] ] ] ] ] ] ]}}">
                                <view class="text _div data-v-b47df2ce">咨询客服购买同款<comp-icon bind:__l="__l" class="data-v-b47df2ce" color="#ffffff" icon="iconarrow_right_o" size="24rpx" vueId="{{'40d67f0e-3-'+index+','+'40d67f0e-2-'+index}}"></comp-icon>
                                </view>
                            </view>
                        </view>
                    </comp-button>
                </swiper-item>
            </swiper>
        </view>
    </view>
</page-panel>
