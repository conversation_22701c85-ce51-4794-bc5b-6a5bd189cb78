(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/user/comp-recommand"], {
    "1dd7": function dd7(t, e, n) {


      n.r(e);
      var o = n("88f6"),
        c = n.n(o);
      for (var r in o)["default"].indexOf(r) < 0 && function(t) {
        n.d(e, t, function() {
          return o[t];
        });
      }(r);
      e["default"] = c.a;
    },
    6432: function _(t, e, n) {


      n.d(e, "b", function() {
        return c;
      }), n.d(e, "c", function() {
        return r;
      }), n.d(e, "a", function() {
        return o;
      });
      var o = {
          compIcon: function compIcon() {
            return n.e("components/comp-icon/comp-icon").then(n.bind(null, "84bb2"));
          },
          compEmpty: function compEmpty() {
            return n.e("components/comp-empty/comp-empty").then(n.bind(null, "ddc4"));
          },
          compImage: function compImage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-image/comp-image")]).then(n.bind(null, "31bf"));
          }
        },
        c = function c() {
          var t = this.$createElement,
            e = (this._self._c, this.list.length),
            n = this.list.length;
          this.$mp.data = Object.assign({}, {
            $root: {
              g0: e,
              g1: n
            }
          });
        },
        r = [];
    },
    "88f6": function f6(t, e, n) {


      (function(t) {
        var o = n("47a9");
        Object.defineProperty(e, "__esModule", {
          value: !0
        }), e.default = void 0;
        var c = o(n("7ca3")),
          r = n("8f59"),
          i = (o(n("8b9c")), o(n("a23b"))),
          u = o(n("5e82")),
          a = n("b3c5");

        function l(t, e) {
          var n = Object.keys(t);
          if (Object.getOwnPropertySymbols) {
            var o = Object.getOwnPropertySymbols(t);
            e && (o = o.filter(function(e) {
              return Object.getOwnPropertyDescriptor(t, e).enumerable;
            })), n.push.apply(n, o);
          }
          return n;
        }
        (0, a.getConfig)();
        var f = {
          props: {
            list: {
              type: Array,
              default: function _default() {
                return [];
              }
            }
          },
          computed: function(t) {
            for (var e = 1; e < arguments.length; e++) {
              var n = null != arguments[e] ? arguments[e] : {};
              e % 2 ? l(Object(n), !0).forEach(function(e) {
                (0, c.default)(t, e, n[e]);
              }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(n)) : l(Object(n)).forEach(function(e) {
                Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(n, e));
              });
            }
            return t;
          }({}, (0, r.mapState)(["profile"])),
          methods: {
            clickMore: function clickMore() {
              this.$emit("clickMore");
            },
            clickProduct: function clickProduct() {
              u.default.to({
                page: "commonSimpleList",
                query: {
                  type: "reward"
                }
              });
            },
            clickAct: function clickAct(e) {
              4 == e.linkType ? e.appId ? t.navigateToMiniProgram({
                appId: e.appId,
                path: e.linkValue,
                success: function success() {
                  i.default.log("Navigate: toMp success");
                },
                fail: function fail(t) {
                  i.default.log("Navigate: toMp fail", t);
                }
              }) : u.default.to({
                fullPath: e.linkValue
              }) : 1 == e.linkType && u.default.toWebviewByTokenUrl({
                tokenUrl: e.linkValue
              });
            }
          }
        };
        e.default = f;
      }).call(this, n("df3c")["default"]);
    },
    d37c: function d37c(t, e, n) {


      var o = n("f338"),
        c = n.n(o);
      c.a;
    },
    def8: function def8(t, e, n) {


      n.r(e);
      var o = n("6432"),
        c = n("1dd7");
      for (var r in c)["default"].indexOf(r) < 0 && function(t) {
        n.d(e, t, function() {
          return c[t];
        });
      }(r);
      n("d37c");
      var i = n("828b"),
        u = Object(i["a"])(c["default"], o["b"], o["c"], !1, null, "e456d562", null, !1, o["a"], void 0);
      e["default"] = u.exports;
    },
    f338: function f338(t, e, n) {}
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/user/comp-recommand-create-component', {
    'pages/user/comp-recommand-create-component': function pagesUserCompRecommandCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("def8"));
    }
  },
  [
    ['pages/user/comp-recommand-create-component']
  ]
]);