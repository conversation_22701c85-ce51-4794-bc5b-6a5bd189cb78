<view class="comp-error flex-col col-center data-v-22dacf18" style="{{'top:'+top+'px'+';'}}">
    <comp-image bind:__l="__l" class="data-v-22dacf18" height="400rpx" name="{{errorImg}}" vueId="10cf099f-1" width="398rpx"></comp-image>
    <view class="name data-v-22dacf18">{{errorName}}</view>
    <view class="subname data-v-22dacf18" wx:if="{{errorSubname}}">{{errorSubname}}</view>
    <comp-button bind:__l="__l" bind:onClick="__e" class="data-v-22dacf18" data-event-opts="{{[ [ '^onClick',[ ['clickRefresh'] ] ] ]}}" vueId="10cf099f-2" vueSlots="{{['default']}}" wx:if="{{errorIsRefresh}}">
        <view class="btn flex-row col-center row-center data-v-22dacf18">
            <slot name="btn" wx:if="{{$slots.btn}}"></slot>
            <block wx:else>
                <comp-icon bind:__l="__l" class="{{['data-v-22dacf18',rotate?'rotate':'']}}" color="#AFAFAF" icon="iconrefresh-o" size="44rpx" vueId="{{'10cf099f-3'+','+'10cf099f-2'}}"></comp-icon>
                <view class="data-v-22dacf18">刷新</view>
            </block>
        </view>
    </comp-button>
</view>
