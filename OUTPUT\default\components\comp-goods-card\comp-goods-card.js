(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["components/comp-goods-card/comp-goods-card"], {
    "1c13": function c13(e, t, n) {


      n.r(t);
      var a = n("cece"),
        r = n.n(a);
      for (var c in a)["default"].indexOf(c) < 0 && function(e) {
        n.d(t, e, function() {
          return a[e];
        });
      }(c);
      t["default"] = r.a;
    },
    "4a1b0": function a1b0(e, t, n) {


      var a = n("9c61"),
        r = n.n(a);
      r.a;
    },
    "555a": function a(e, t, n) {


      n.d(t, "b", function() {
        return r;
      }), n.d(t, "c", function() {
        return c;
      }), n.d(t, "a", function() {
        return a;
      });
      var a = {
          compImage: function compImage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-image/comp-image")]).then(n.bind(null, "31bf"));
          }
        },
        r = function r() {
          var e = this.$createElement;
          this._self._c;
        },
        c = [];
    },
    "9c61": function c61(e, t, n) {},
    bc6b: function bc6b(e, t, n) {


      n.r(t);
      var a = n("555a"),
        r = n("1c13");
      for (var c in r)["default"].indexOf(c) < 0 && function(e) {
        n.d(t, e, function() {
          return r[e];
        });
      }(c);
      n("4a1b0");
      var o = n("828b"),
        u = Object(o["a"])(r["default"], a["b"], a["c"], !1, null, "14f3d638", null, !1, a["a"], void 0);
      t["default"] = u.exports;
    },
    cece: function cece(e, t, n) {


      var a = n("47a9");
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.default = void 0;
      var r = a(n("7eb4")),
        c = a(n("ee10")),
        o = (a(n("8b9c")), a(n("5e82"))),
        u = (a(n("a23b")), {
          props: {
            img: {
              type: String,
              default: ""
            },
            name: {
              type: String,
              default: ""
            },
            subname: {
              type: String,
              default: ""
            },
            score: {
              type: Number,
              default: 0
            },
            tokenUrl: {
              type: String,
              default: ""
            }
          },
          computed: {
            showName: function showName() {
              var e = this.name,
                t = this.subname ? '<span style="color: #F04033">'.concat(this.subname, "</span>") : "";
              return e + t;
            }
          },
          methods: {
            clickGoods: function clickGoods() {
              var e = this;
              return (0, c.default)(r.default.mark(function t() {
                return r.default.wrap(function(t) {
                  while (1) switch (t.prev = t.next) {
                    case 0:
                      o.default.toWebviewByTokenUrl({
                        tokenUrl: e.tokenUrl
                      });
                    case 1:
                    case "end":
                      return t.stop();
                  }
                }, t);
              }))();
            }
          }
        });
      t.default = u;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['components/comp-goods-card/comp-goods-card-create-component', {
    'components/comp-goods-card/comp-goods-card-create-component': function componentsCompGoodsCardCompGoodsCardCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("bc6b"));
    }
  },
  [
    ['components/comp-goods-card/comp-goods-card-create-component']
  ]
]);