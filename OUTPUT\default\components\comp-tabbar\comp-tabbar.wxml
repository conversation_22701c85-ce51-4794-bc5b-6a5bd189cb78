<view class="comp-tabbar flex-row space-around data-v-7310f83d">
    <view bindtap="__e" class="{{['tab','flex-col','row-center','col-center','flex-one','data-v-7310f83d',item.id===active?'is-active':'']}}" data-event-opts="{{[ [ 'tap',[ [ 'switchTab',['$0'],[ [ ['tabData','',index] ] ] ] ] ] ]}}" wx:for="{{tabData}}" wx:key="index">
        <image class="icon data-v-7310f83d" src="{{item.id===active?item.iconActive:item.icon}}"></image>
        <view class="name data-v-7310f83d">{{item.name}}</view>
    </view>
</view>
