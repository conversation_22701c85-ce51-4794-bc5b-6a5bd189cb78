.pic-row.data-v-b47df2ce {
    margin: 10rpx auto 0;
    overflow-x: scroll
}

.pic-row .card.data-v-b47df2ce {
    background-size: cover;
    border-radius: 24rpx;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    height: 360rpx;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
    margin: 0 10rpx
}

.pic-row .card .more.data-v-b47df2ce {
    -webkit-backdrop-filter: blur(3px);
    backdrop-filter: blur(3px);
    background: rgba(0,0,0,.6);
    border-radius: 0rpx 0rpx 24rpx 24rpx;
    height: 72rpx;
    line-height: 72rpx;
    width: 692rpx
}

.pic-row .card .more .text.data-v-b47df2ce {
    -webkit-align-items: center;
    align-items: center;
    color: #fff;
    display: -webkit-flex;
    display: flex;
    font-family: PingFang-SC-Medium,PingFang-SC;
    font-size: 28rpx;
    font-weight: 500;
    height: 72rpx;
    line-height: 72rpx;
    padding-left: 24rpx
}

.pic-row .name.data-v-b47df2ce {
    color: rgba(0,0,0,.85);
    font-family: PingFang-SC-Bold,PingFang-SC;
    font-size: 28rpx;
    font-weight: 700;
    height: 40rpx;
    line-height: 40rpx;
    margin: 16rpx 0 4rpx;
    padding-left: 24rpx
}

.pic-row .location.data-v-b47df2ce {
    color: rgba(0,0,0,.35);
    font-family: PingFang-SC-Medium,PingFang-SC;
    font-size: 26rpx;
    font-weight: 500;
    height: 37rpx;
    line-height: 37rpx;
    padding-left: 24rpx
}
