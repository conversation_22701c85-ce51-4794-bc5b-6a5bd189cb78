var t = function(t) {
    return (t = t.toString())[1] ? t : "0" + t
  },
  n = function(t) {
    wx.login({
      success: function(n) {
        t(n)
      }
    })
  },
  e = function() {
    return new Promise(function(t, n) {
      wx.getUserInfo({
        lang: "zh_CN",
        success: function(n) {
          t(n)
        },
        fail: function(t) {
          n(t)
        }
      })
    })
  };
module.exports = {
  formatDate: function(n) {
    var e = n.getFullYear(),
      o = n.getMonth() + 1,
      i = n.getDate(),
      u = n.getHours(),
      r = n.getMinutes(),
      a = n.getSeconds();
    return [e, o, i].map(t).join("-") + "T" + [u, r, a].map(t).join(":")
  },
  formatDateNoT: function(n) {
    var e = n.getFullYear(),
      o = n.getMonth() + 1,
      i = n.getDate(),
      u = n.getHours(),
      r = n.getMinutes(),
      a = n.getSeconds();
    return [e, o, i].map(t).join("-") + " " + [u, r, a].map(t).join(":")
  },
  formatTime: function(n) {
    var e = n.getFullYear(),
      o = n.getMonth() + 1,
      i = n.getDate(),
      u = n.getHours(),
      r = n.getMinutes(),
      a = n.getSeconds();
    return [e, o, i].map(t).join("/") + " " + [u, r, a].map(t).join(":")
  },
  chekWxLogin: function() {
    return new Promise(function(t, o) {
      wx.getSetting({
        success: function(i) {
          if (!i.authSetting["scope.userInfo"]) return o({
            authSetting: !1
          });
          wx.getStorage({
            key: "cache_key",
            success: function(n) {
              if (checkLogin()) return t({
                userinfo: getApp().globalData.userInfo,
                isLogin: !0
              });
              e().then(function(e) {
                return e.cache_key = n.data, t({
                  userInfo: e,
                  isLogin: !1
                })
              }).catch(function(t) {
                return o(t)
              })
            },
            fail: function() {
              n(function(n) {
                e().then(function(e) {
                  return e.code = n, t({
                    userInfo: e,
                    isLogin: !1
                  })
                }).catch(function(t) {
                  return o(t)
                })
              })
            }
          })
        },
        fail: function(t) {
          return o(t)
        }
      })
    })
  },
  getCodeLogin: n,
  wxgetUserInfo: e,
  checkUpdateVersion: function() {
    if (wx.canIUse("getUpdateManager")) {
      var t = wx.getUpdateManager();
      t.onCheckForUpdate(function(n) {
        n.hasUpdate && (t.onUpdateReady(function() {
          t.applyUpdate()
        }), t.onUpdateFailed(function() {
          wx.showModal({
            title: "已经有新版本喽~",
            content: "请您删除当前小程序，到微信 “发现-小程序” 页，重新搜索打开哦~"
          })
        }))
      })
    } else wx.showModal({
      title: "溫馨提示",
      content: "当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。"
    })
  },
  uuid: function() {
    for (var t = [], n = 0; n < 32; n++) t[n] = "0123456789abcdef".substr(Math.floor(16 * Math.random()), 1);
    return t.join("")
  }
};