{"usingComponents": {"comp-button": "components\\comp-packets-reward\\components\\comp-button\\comp-button", "comp-checkbox": "components\\comp-packets-reward\\components\\comp-checkbox\\comp-checkbox", "comp-agreement": "components\\comp-packets-reward\\components\\comp-agreement\\comp-agreement", "duiba-pay": "components\\comp-packets-reward\\wxcomponents\\duiba-miniprogram-pay\\index", "painter": "components\\comp-packets-reward\\wxcomponents\\painter\\painter", "mp-action-sheet": "components\\comp-packets-reward\\miniprogram_npm\\weui-miniprogram\\actionsheet\\actionsheet", "mp-cell": "components\\comp-packets-reward\\miniprogram_npm\\weui-miniprogram\\cell\\cell", "mp-cells": "components\\comp-packets-reward\\miniprogram_npm\\weui-miniprogram\\cells\\cells", "mp-emoji": "components\\comp-packets-reward\\wxcomponents\\emoji\\index", "mp-half-screen-dialog": "components\\comp-packets-reward\\miniprogram_npm\\weui-miniprogram\\half-screen-dialog\\half-screen-dialog", "mp-icon": "components\\comp-packets-reward\\miniprogram_npm\\weui-miniprogram\\icon\\icon", "mp-loading": "components\\comp-packets-reward\\miniprogram_npm\\weui-miniprogram\\loading\\loading", "mp-navigation-bar": "components\\comp-packets-reward\\miniprogram_npm\\weui-miniprogram\\navigation-bar\\navigation-bar", "mp-slideview": "components\\comp-packets-reward\\miniprogram_npm\\weui-miniprogram\\slideview\\slideview", "mi-lin-video": ".\\components\\comp-packets-reward\\plugin:\\wx8d04ad0492dab24d\\miLin-video"}, "component": true}