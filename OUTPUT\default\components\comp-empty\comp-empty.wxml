<view class="comp-empty flex-col row-center col-center data-v-9229a4a2">
    <comp-image bind:__l="__l" class="data-v-9229a4a2" height="400rpx" name="{{img}}" vueId="65e74dbc-1" width="398rpx"></comp-image>
    <view class="name data-v-9229a4a2" wx:if="{{name!=='true'}}">{{name}}</view>
    <view class="subname data-v-9229a4a2" wx:if="{{subname}}">{{subname}}</view>
    <comp-button bind:__l="__l" bind:onClick="__e" class="data-v-9229a4a2" data-event-opts="{{[ [ '^onClick',[ ['clickBtn'] ] ] ]}}" vueId="65e74dbc-2" vueSlots="{{['default']}}" wx:if="{{isBtn}}">
        <view class="btn flex-row col-center row-center data-v-9229a4a2">
            <slot name="btn" wx:if="{{$slots.btn}}"></slot>
            <block wx:else>
                <comp-icon bind:__l="__l" class="data-v-9229a4a2" color="#AFAFAF" icon="iconrefresh-o" size="44rpx" vueId="{{'65e74dbc-3'+','+'65e74dbc-2'}}"></comp-icon>
                <view class="data-v-9229a4a2">刷新</view>
            </block>
        </view>
    </comp-button>
</view>
