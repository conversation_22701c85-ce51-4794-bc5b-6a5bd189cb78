(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/user/page-nav"], {
    "0e18": function e18(e, t, n) {


      n.r(t);
      var r = n("a4e1"),
        o = n.n(r);
      for (var c in r)["default"].indexOf(c) < 0 && function(e) {
        n.d(t, e, function() {
          return r[e];
        });
      }(c);
      t["default"] = o.a;
    },
    "5afa": function afa(e, t, n) {


      n.r(t);
      var r = n("df85"),
        o = n("0e18");
      for (var c in o)["default"].indexOf(c) < 0 && function(e) {
        n.d(t, e, function() {
          return o[e];
        });
      }(c);
      n("c001");
      var u = n("828b"),
        a = Object(u["a"])(o["default"], r["b"], r["c"], !1, null, "38a402f0", null, !1, r["a"], void 0);
      t["default"] = a.exports;
    },
    6414: function _(e, t, n) {},
    a4e1: function a4e1(e, t, n) {


      var r = n("47a9");
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.default = void 0;
      var o = r(n("7eb4")),
        c = r(n("ee10")),
        u = r(n("7ca3")),
        a = n("8f59"),
        i = r(n("5e82")),
        f = n("b3c5"),
        l = r(n("c148")),
        s = r(n("a23b"));

      function p(e, t) {
        var n = Object.keys(e);
        if (Object.getOwnPropertySymbols) {
          var r = Object.getOwnPropertySymbols(e);
          t && (r = r.filter(function(t) {
            return Object.getOwnPropertyDescriptor(e, t).enumerable;
          })), n.push.apply(n, r);
        }
        return n;
      }
      (0, f.getConfig)();
      var d = {
        computed: function(e) {
          for (var t = 1; t < arguments.length; t++) {
            var n = null != arguments[t] ? arguments[t] : {};
            t % 2 ? p(Object(n), !0).forEach(function(t) {
              (0, u.default)(e, t, n[t]);
            }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : p(Object(n)).forEach(function(t) {
              Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
            });
          }
          return e;
        }({}, (0, a.mapState)(["profile"])),
        data: function data() {
          return {
            couponNum: ""
          };
        },
        mounted: function mounted() {
          this.getMyCoupons();
        },
        methods: {
          getMyCoupons: function getMyCoupons() {
            var e = this;
            return (0, c.default)(o.default.mark(function t() {
              var n, r;
              return o.default.wrap(function(t) {
                while (1) switch (t.prev = t.next) {
                  case 0:
                    return t.next = 2, l.default.getUserCoupon({});
                  case 2:
                    n = t.sent, r = s.default.get(n, "data.data"), e.couponNum = r;
                  case 5:
                  case "end":
                    return t.stop();
                }
              }, t);
            }))();
          },
          clickMyScore: function clickMyScore(e) {
            i.default.to({
              page: "myCoupon"
            });
          },
          clickScoreExchange: function clickScoreExchange(e) {
            i.default.toWebviewByTokenUrl({
              tokenUrl: this.profile.scoreExchangeTokenUrl
            });
          },
          clickMemberTask: function clickMemberTask() {
            i.default.to({
              fullPath: "/pages-picture/picture/page-add-pic?activeTab=2"
            });
          }
        }
      };
      t.default = d;
    },
    c001: function c001(e, t, n) {


      var r = n("6414"),
        o = n.n(r);
      o.a;
    },
    df85: function df85(e, t, n) {


      n.d(t, "b", function() {
        return o;
      }), n.d(t, "c", function() {
        return c;
      }), n.d(t, "a", function() {
        return r;
      });
      var r = {
          compButton: function compButton() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-button/comp-button")]).then(n.bind(null, "ca5a"));
          },
          compImage: function compImage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-image/comp-image")]).then(n.bind(null, "31bf"));
          }
        },
        o = function o() {
          var e = this.$createElement;
          this._self._c;
        },
        c = [];
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/user/page-nav-create-component', {
    'pages/user/page-nav-create-component': function pagesUserPageNavCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("5afa"));
    }
  },
  [
    ['pages/user/page-nav-create-component']
  ]
]);