<view class="{{['page-nav','data-v-0e8688be',bbnone?'bbnone':'']}}">
    <view class="card-box data-v-0e8688be">
        <view class="h3 data-v-0e8688be">{{boxTitle}}</view>
        <view class="flex flex-wrap flex-row data-v-0e8688be">
            <view class="nav flex-row row-center page-nav__item data-v-0e8688be" wx:for="{{navs}}" wx:key="index">
                <comp-button bind:__l="__l" bind:getUserInfo="__e" bind:onClick="__e" class="data-v-0e8688be" data-event-opts="{{[ [ '^getUserInfo',[ [ 'authClickNav',['$0'],[ [ ['navs','',index] ] ] ] ] ],[ '^onClick',[ [ 'clickNav',['$0'],[ [ ['navs','',index] ] ] ] ] ] ]}}" openType="{{item.openType}}" vueId="{{'0df5a40e-1-'+index}}" vueSlots="{{['default']}}">
                    <view class="flex-col col-center row-center data-v-0e8688be">
                        <view class="icon data-v-0e8688be">
                            <comp-image bind:__l="__l" class="data-v-0e8688be" height="72rpx" isCustomSrc="{{item.imgUrl}}" name="{{item.img}}" url="{{item.imgUrl}}" vueId="{{'0df5a40e-2-'+index+','+'0df5a40e-1-'+index}}" width="72rpx"></comp-image>
                        </view>
                        <view class="{{['name','data-v-0e8688be',!item.subname?'one_row':'']}}">{{item.name}}</view>
                        <view class="subname data-v-0e8688be">{{item.subname}}</view>
                    </view>
                </comp-button>
            </view>
        </view>
    </view>
</view>
