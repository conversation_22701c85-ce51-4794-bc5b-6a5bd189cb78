(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/user/page-nav-list"], {
    "0229": function _(e, n, t) {},
    "14e4": function e4(e, n, t) {


      t.d(n, "b", function() {
        return c;
      }), t.d(n, "c", function() {
        return o;
      }), t.d(n, "a", function() {
        return a;
      });
      var a = {
          compButton: function compButton() {
            return Promise.all([t.e("common/vendor"), t.e("components/comp-button/comp-button")]).then(t.bind(null, "ca5a"));
          }
        },
        c = function c() {
          var e = this.$createElement;
          this._self._c;
        },
        o = [];
    },
    5579: function _(e, n, t) {


      t.r(n);
      var a = t("de05"),
        c = t.n(a);
      for (var o in a)["default"].indexOf(o) < 0 && function(e) {
        t.d(n, e, function() {
          return a[e];
        });
      }(o);
      n["default"] = c.a;
    },
    "859e": function e(_e, n, t) {


      t.r(n);
      var a = t("14e4"),
        c = t("5579");
      for (var o in c)["default"].indexOf(o) < 0 && function(e) {
        t.d(n, e, function() {
          return c[e];
        });
      }(o);
      t("f581");
      var i = t("828b"),
        r = Object(i["a"])(c["default"], a["b"], a["c"], !1, null, "21b573b7", null, !1, a["a"], void 0);
      n["default"] = r.exports;
    },
    de05: function de05(e, n, t) {


      (function(e) {
        var a = t("47a9");
        Object.defineProperty(n, "__esModule", {
          value: !0
        }), n.default = void 0;
        var c = a(t("7ca3")),
          o = t("8f59"),
          i = a(t("5e82"));

        function r(e, n) {
          var t = Object.keys(e);
          if (Object.getOwnPropertySymbols) {
            var a = Object.getOwnPropertySymbols(e);
            n && (a = a.filter(function(n) {
              return Object.getOwnPropertyDescriptor(e, n).enumerable;
            })), t.push.apply(t, a);
          }
          return t;
        }
        var u = {
          data: function data() {
            return {
              baseUrl: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/",
              navList: [{
                icon: "icon_nav_1",
                name: "我的保修卡",
                click: this.handleWarrantyCard
              }, {
                icon: "icon_nav_3",
                name: "我的建议",
                click: this.handleSuggestion
              }, {
                icon: "icon_nav_4",
                name: "我的投诉",
                click: this.handleComplaint
              }, {
                icon: "icon_nav_5",
                name: "我的图册",
                click: this.handleMemberTask
              }, {
                icon: "icon_nav_6",
                name: "维豆兑换",
                click: this.handleScoreExchange
              }, {
                icon: "icon_nav_7",
                name: "我的优惠券",
                click: this.handleMyScore
              }, {
                icon: "icon_nav_8",
                name: "在线客服",
                click: this.handleCustomer
              }]
            };
          },
          computed: function(e) {
            for (var n = 1; n < arguments.length; n++) {
              var t = null != arguments[n] ? arguments[n] : {};
              n % 2 ? r(Object(t), !0).forEach(function(n) {
                (0, c.default)(e, n, t[n]);
              }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : r(Object(t)).forEach(function(n) {
                Object.defineProperty(e, n, Object.getOwnPropertyDescriptor(t, n));
              });
            }
            return e;
          }({}, (0, o.mapState)(["profile"])),
          methods: {
            handleClick: function handleClick(e) {
              e.click();
            },
            handleWarrantyCard: function handleWarrantyCard() {
              e.navigateTo({
                url: "/pages-guarantee/device/device"
              });
            },
            handleActivity: function handleActivity() {
              e.navigateTo({
                url: "/pages-product/myActivities/myActivities"
              });
            },
            handleSuggestion: function handleSuggestion() {
              e.navigateTo({
                url: "/pages-product/mySuggestion/mySuggestion"
              });
            },
            handleComplaint: function handleComplaint() {
              e.navigateTo({
                url: "/pages-product/myComplaint/myComplaint"
              });
            },
            handleMemberTask: function handleMemberTask() {
              i.default.to({
                fullPath: "/pages-picture/picture/page-add-pic?activeTab=2"
              });
            },
            handleScoreExchange: function handleScoreExchange() {
              i.default.toWebviewByTokenUrl({
                tokenUrl: this.profile.scoreExchangeTokenUrl
              });
            },
            handleMyScore: function handleMyScore() {
              i.default.to({
                page: "myCoupon"
              });
            },
            handleCustomer: function handleCustomer() {
              e.navigateTo({
                url: "/pages/webview/webviewKF"
              });
            }
          }
        };
        n.default = u;
      }).call(this, t("df3c")["default"]);
    },
    f581: function f581(e, n, t) {


      var a = t("0229"),
        c = t.n(a);
      c.a;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/user/page-nav-list-create-component', {
    'pages/user/page-nav-list-create-component': function pagesUserPageNavListCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("859e"));
    }
  },
  [
    ['pages/user/page-nav-list-create-component']
  ]
]);