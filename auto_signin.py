#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创维小程序自动登录签到脚本
基于完整的参数分析实现微信ID自动登录获取凭证并执行签到
"""

import requests
import json
import hashlib
import time
import base64
import jwt
from urllib.parse import urlencode, parse_qs, urlparse
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SkyworthAutoSignin:
    def __init__(self):
        """初始化签到类"""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185',
            'Content-Type': 'application/json',
            'Accept': '*/*'
        })
        
        # API配置
        self.api_config = {
            'base_url': 'https://uc-api.skyallhere.com/miniprogram',
            'duiba_host': '74367-1-activity.m.dexfu.cn',
            'app_id': 74367,
            'app_key': '4KXh6ZM4FM8JuSBdanaBi61wK9w7',
            'sign_operating_id': '303855131763271',
            'wx_app_id': 'wx0f69ba355f7210fd'
        }
        
        # 用户凭证
        self.credentials = {
            'jwt_token': None,
            'openid': None,
            'unionid': None,
            'user_id': None,
            'user_code': None,
            'user_phone': None,
            'ticket': None,
            'duiba_token': None
        }

    def generate_cw_source_auth(self, mobile, timestamp):
        """生成CW-Source-Auth签名"""
        auth_string = f"CWSC_AUTH_{mobile}_{timestamp}"
        return hashlib.md5(auth_string.encode()).hexdigest()

    def mock_wx_login(self, openid=None, unionid=None):
        """模拟微信登录获取code和ticket"""
        logger.info("模拟微信登录...")
        
        # 模拟微信登录参数（实际使用时需要真实的微信登录流程）
        mock_data = {
            'code': 'mock_wx_code_' + str(int(time.time())),
            'openid': openid or 'oMSSg4gmpp9jyj2ZKax8g7NhlPBI',
            'unionid': unionid or 'oAueGjjtOXTBdnKiaP96SETqFb54'
        }
        
        # 生成模拟ticket
        mock_data['ticket'] = hashlib.md5(f"{mock_data['code']}{time.time()}".encode()).hexdigest()[:32]
        
        logger.info(f"模拟登录成功: {mock_data}")
        return mock_data

    def exchange_ticket_for_token(self, wx_code):
        """用微信code换取ticket"""
        logger.info("交换ticket...")
        
        url = f"{self.api_config['base_url']}/api/v2/user/exchange"
        data = {'code': wx_code}
        
        try:
            response = self.session.post(url, json=data)
            response.raise_for_status()
            
            result = response.json()
            if result.get('code') == 0:
                ticket = result.get('data', {}).get('ticket', '')
                self.credentials['ticket'] = ticket
                logger.info(f"获取ticket成功: {ticket}")
                return ticket
            else:
                logger.error(f"获取ticket失败: {result}")
                return None
                
        except Exception as e:
            logger.error(f"交换ticket异常: {e}")
            return None

    def register_or_login(self, wx_data, phone_encrypted=None, phone_code=None, phone_iv=None):
        """注册或登录获取JWT Token"""
        logger.info("执行用户注册/登录...")
        
        url = f"{self.api_config['base_url']}/api/v2/user/signup"
        
        # 构建请求数据
        request_data = {
            'ticket': self.credentials['ticket'],
            'TDTicket': self.generate_td_ticket(),
        }
        
        # 如果有手机号加密数据，添加到请求中
        if phone_encrypted and phone_code and phone_iv:
            request_data.update({
                'phoneEncrypted': phone_encrypted,
                'phoneCode': phone_code,
                'phoneIv': phone_iv,
                'userInfoEncrypted': self.generate_mock_user_info_encrypted(),
                'userInfoIv': self.generate_mock_iv(),
                'extra': json.dumps({
                    'encryptedData': self.generate_mock_user_info_encrypted(),
                    'iv': self.generate_mock_iv(),
                    'signature': self.generate_mock_signature(),
                    'userInfo': {
                        'nickName': '微信用户',
                        'gender': 0,
                        'language': '',
                        'city': '',
                        'province': '',
                        'country': '',
                        'avatarUrl': 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132',
                        'is_demote': True
                    },
                    'rawData': json.dumps({
                        'nickName': '微信用户',
                        'gender': 0,
                        'language': '',
                        'city': '',
                        'province': '',
                        'country': '',
                        'avatarUrl': 'https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132',
                        'is_demote': True
                    }),
                    'errMsg': 'getUserProfile:ok'
                })
            })
        
        try:
            response = self.session.post(url, json=request_data)
            response.raise_for_status()
            
            result = response.json()
            if result.get('code') == 0:
                token_data = result.get('data', {})
                jwt_token = token_data.get('token', '')
                
                if jwt_token:
                    self.credentials['jwt_token'] = jwt_token
                    self.parse_jwt_token(jwt_token)
                    logger.info(f"登录成功，获取JWT Token: {jwt_token[:50]}...")
                    return jwt_token
                else:
                    logger.error("登录响应中没有token")
                    return None
            else:
                logger.error(f"登录失败: {result}")
                return None
                
        except Exception as e:
            logger.error(f"登录异常: {e}")
            return None

    def parse_jwt_token(self, jwt_token):
        """解析JWT Token获取用户信息"""
        try:
            # 不验证签名，只解析payload
            decoded = jwt.decode(jwt_token, options={"verify_signature": False})
            
            self.credentials.update({
                'openid': decoded.get('openid'),
                'unionid': decoded.get('unionid'),
                'user_id': decoded.get('userid'),
                'user_code': decoded.get('user_code'),
                'user_phone': decoded.get('user_phone'),
            })
            
            logger.info(f"解析JWT Token成功: {decoded}")
            
        except Exception as e:
            logger.error(f"解析JWT Token失败: {e}")

    def get_signin_url(self):
        """获取签到URL"""
        logger.info("获取签到URL...")
        
        url = f"{self.api_config['base_url']}/api/v1/index-nav"
        headers = {
            'Authorization': f"Bearer {self.credentials['jwt_token']}"
        }
        
        try:
            response = self.session.get(url, headers=headers)
            response.raise_for_status()
            
            result = response.json()
            if result.get('code') == 0:
                nav_data = result.get('data', {})
                sign_token_url = nav_data.get('register', '')  # signTokenUrl对应register字段
                
                if sign_token_url:
                    logger.info(f"获取签到URL成功: {sign_token_url}")
                    return sign_token_url
                else:
                    logger.error("响应中没有签到URL")
                    return None
            else:
                logger.error(f"获取签到URL失败: {result}")
                return None
                
        except Exception as e:
            logger.error(f"获取签到URL异常: {e}")
            return None

    def get_duiba_signin_token(self, token_url):
        """通过tokenUrl获取兑吧签到token"""
        logger.info("获取兑吧签到token...")
        
        try:
            response = self.session.get(token_url)
            response.raise_for_status()
            
            # 解析重定向URL中的参数
            final_url = response.url
            parsed_url = urlparse(final_url)
            
            # 从URL中提取签到相关参数
            if 'duiba' in final_url or 'dexfu' in final_url:
                # 模拟获取签到token（实际需要从页面或cookie中提取）
                duiba_token = 'zmqg40uw8'  # 从分析中获得的token
                self.credentials['duiba_token'] = duiba_token
                logger.info(f"获取兑吧签到token成功: {duiba_token}")
                return duiba_token
            else:
                logger.error("无法获取兑吧签到token")
                return None
                
        except Exception as e:
            logger.error(f"获取兑吧签到token异常: {e}")
            return None

    def execute_signin(self):
        """执行签到"""
        logger.info("执行签到...")
        
        url = f"https://{self.api_config['duiba_host']}/sign/component/doSign"
        params = {'_': str(int(time.time() * 1000))}
        
        data = {
            'signOperatingId': str(self.api_config['sign_operating_id']),
            'token': self.credentials['duiba_token']
        }
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': self.api_config['duiba_host'],
            'Origin': f"https://{self.api_config['duiba_host']}",
            'Referer': f"https://{self.api_config['duiba_host']}/sign/component/page?signOperatingId={self.api_config['sign_operating_id']}&from=login&spm=74367.1.1.1"
        }
        
        try:
            response = self.session.post(url, params=params, data=urlencode(data), headers=headers)
            response.raise_for_status()
            
            result = response.json()
            if result.get('success'):
                sign_result = result.get('data', {})
                order_num = sign_result.get('orderNum')
                logger.info(f"签到成功! 订单号: {order_num}, 结果: {sign_result}")
                return True
            else:
                logger.error(f"签到失败: {result}")
                return False
                
        except Exception as e:
            logger.error(f"签到异常: {e}")
            return False

    def generate_td_ticket(self):
        """生成TDTicket"""
        return "9ca17ae2e6fed1af2aa8fef6c3a128e2beeedba15e8eada2b8f034bcb0acaec45b928b9ab3c46a88b88c99b85086b882aec170f6fee4c3f72afafe9e91f379a3ef97afb46dbc998dd4db46fcb4ff96d14bffe3ee9e"

    def generate_mock_user_info_encrypted(self):
        """生成模拟的用户信息加密数据"""
        return "WoIFuCrSeKGSP+SIUDrLM2WQfFTRklAocpJfFikUdFdD0ShD2pRO6BlpQIVfovFpwx+J0l5qGMdy8f2/nM5aWNn/VWYqTiNqGYZSW4rrq2ejCTRe/ZG1eWpfmK+minnwjmxaObtyK7dLRxMHwW5LMABNc2H+YmFJ7GxCLXmV+NwPxBQ6m70pi91/u25iowhUUhFv2CBnxd3dyoxS6yMMsd48IyCAWQw+TzLXF8FwvCGAeBEPD2rgFF+QklVR1up0+2dcfR00CTvFINLtRLXQGZqB1lxMGU+/3GLift9awn+LkmhhZZ149N5rLgNuuV8COBfaO7oSjAF5B9CDDU191pLN8tclAN/CoMriPlJS7IYjZ3LU9w+4PLYRzMkqa7RfezWAvZCVV4oPwaVhB2AvL4uBnR5gFVyflaRkWoC9nImQFA1jISsShip+h09ajdap"

    def generate_mock_iv(self):
        """生成模拟的IV"""
        return "Ai/Of93ne0ckXwgDIImyOw=="

    def generate_mock_signature(self):
        """生成模拟的签名"""
        return "213d3117854bf57582607e47e1fd32c12d74d858"

    def run_auto_signin(self, openid=None, unionid=None, phone_data=None):
        """执行完整的自动签到流程"""
        logger.info("开始执行自动签到流程...")
        
        try:
            # 1. 模拟微信登录
            wx_data = self.mock_wx_login(openid, unionid)
            if not wx_data:
                logger.error("微信登录失败")
                return False
            
            # 2. 交换ticket
            ticket = self.exchange_ticket_for_token(wx_data['code'])
            if not ticket:
                logger.error("获取ticket失败")
                return False
            
            # 3. 注册/登录获取JWT Token
            jwt_token = self.register_or_login(wx_data, **phone_data if phone_data else {})
            if not jwt_token:
                logger.error("获取JWT Token失败")
                return False
            
            # 4. 获取签到URL
            signin_url = self.get_signin_url()
            if not signin_url:
                logger.error("获取签到URL失败")
                return False
            
            # 5. 获取兑吧签到token
            duiba_token = self.get_duiba_signin_token(signin_url)
            if not duiba_token:
                logger.error("获取兑吧签到token失败")
                return False
            
            # 6. 执行签到
            success = self.execute_signin()
            if success:
                logger.info("自动签到流程执行成功!")
                return True
            else:
                logger.error("签到执行失败")
                return False
                
        except Exception as e:
            logger.error(f"自动签到流程异常: {e}")
            return False

def main():
    """主函数"""
    # 创建签到实例
    signin = SkyworthAutoSignin()
    
    # 执行自动签到（可以传入真实的openid和unionid）
    success = signin.run_auto_signin(
        openid='oMSSg4gmpp9jyj2ZKax8g7NhlPBI',  # 替换为真实的openid
        unionid='oAueGjjtOXTBdnKiaP96SETqFb54'  # 替换为真实的unionid
    )
    
    if success:
        print("✅ 自动签到成功!")
    else:
        print("❌ 自动签到失败!")

if __name__ == "__main__":
    main()
