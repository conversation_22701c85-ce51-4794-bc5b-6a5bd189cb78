{"component": true, "usingComponents": {"comp-button": "components\\comp-packets-result-wrap\\components\\comp-button\\comp-button", "duiba-pay": "components\\comp-packets-result-wrap\\wxcomponents\\duiba-miniprogram-pay\\index", "painter": "components\\comp-packets-result-wrap\\wxcomponents\\painter\\painter", "mp-action-sheet": "components\\comp-packets-result-wrap\\miniprogram_npm\\weui-miniprogram\\actionsheet\\actionsheet", "mp-cell": "components\\comp-packets-result-wrap\\miniprogram_npm\\weui-miniprogram\\cell\\cell", "mp-cells": "components\\comp-packets-result-wrap\\miniprogram_npm\\weui-miniprogram\\cells\\cells", "mp-emoji": "components\\comp-packets-result-wrap\\wxcomponents\\emoji\\index", "mp-half-screen-dialog": "components\\comp-packets-result-wrap\\miniprogram_npm\\weui-miniprogram\\half-screen-dialog\\half-screen-dialog", "mp-icon": "components\\comp-packets-result-wrap\\miniprogram_npm\\weui-miniprogram\\icon\\icon", "mp-loading": "components\\comp-packets-result-wrap\\miniprogram_npm\\weui-miniprogram\\loading\\loading", "mp-navigation-bar": "components\\comp-packets-result-wrap\\miniprogram_npm\\weui-miniprogram\\navigation-bar\\navigation-bar", "mp-slideview": "components\\comp-packets-result-wrap\\miniprogram_npm\\weui-miniprogram\\slideview\\slideview", "mi-lin-video": ".\\components\\comp-packets-result-wrap\\plugin:\\wx8d04ad0492dab24d\\miLin-video"}}