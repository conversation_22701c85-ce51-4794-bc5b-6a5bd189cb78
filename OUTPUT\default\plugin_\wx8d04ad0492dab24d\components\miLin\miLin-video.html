	<style> </style> 	<page></page> 	<script> 	var __setCssStartTime__ = Date.now();			__wxAppCode__['plugin-private://wx8d04ad0492dab24d/components/miLin/miLin-video.wxss']();	
	var __setCssEndTime__ = Date.now();	(function() { 		var gf = $gwx_wx8d04ad0492dab24d( './components/miLin/miLin-video.wxml' ); 		if (window.__wxAppCodeReadyCallback__) { 			window.__wxAppCodeReadyCallback__(gf); 		} else { 			document.dispatchEvent(new CustomEvent( "generateFuncReady", { 				detail: { 					generateFunc: gf 			}})); 		} 	})(); 	</script> 	 