(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["components/comp-button/comp-button"], {
    "2ef4": function ef4(e, t, n) {},
    "60c4": function c4(e, t, n) {


      (function(e, r) {
        var o = n("47a9");
        Object.defineProperty(t, "__esModule", {
          value: !0
        }), t.default = void 0;
        var f = o(n("7eb4")),
          s = o(n("34cf")),
          a = o(n("ee10")),
          u = o(n("7ca3")),
          i = n("8f59"),
          l = o(n("8b9c")),
          c = o(n("a23b")),
          p = o(n("5e82"));

        function d(e, t) {
          var n = Object.keys(e);
          if (Object.getOwnPropertySymbols) {
            var r = Object.getOwnPropertySymbols(e);
            t && (r = r.filter(function(t) {
              return Object.getOwnPropertyDescriptor(e, t).enumerable;
            })), n.push.apply(n, r);
          }
          return n;
        }

        function I(e) {
          for (var t = 1; t < arguments.length; t++) {
            var n = null != arguments[t] ? arguments[t] : {};
            t % 2 ? d(Object(n), !0).forEach(function(t) {
              (0, u.default)(e, t, n[t]);
            }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : d(Object(n)).forEach(function(t) {
              Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
            });
          }
          return e;
        }
        var b = {
          props: {
            openType: {
              type: String,
              default: ""
            },
            userInfoIsSkip: {
              type: Boolean,
              default: !1
            },
            userInfoIsPreventDefault: {
              type: Boolean,
              default: !1
            },
            userInfoIsAuth: {
              type: Boolean,
              default: !0
            },
            userInfoIsRegister: {
              type: Boolean,
              default: !1
            },
            userInfoIsRefresh: {
              type: Boolean,
              default: !1
            },
            userInfoIsForceRefresh: {
              type: Boolean,
              default: !1
            },
            userInfoIsProfile: {
              type: Boolean,
              default: !0
            }
          },
          computed: I(I({}, (0, i.mapState)(["token", "profile"])), {}, {
            btnOpenType: function btnOpenType() {
              return "getUserInfo" === this.openType && e.getUserProfile ? "_getUserProfile" : this.openType;
            },
            btnUserInfoIsSkip: function btnUserInfoIsSkip() {
              return !!this.userInfoIsSkip || !(!this.userInfoIsProfile || !this.profile);
            },
            btnUserInfoIsPreventDefault: function btnUserInfoIsPreventDefault() {
              return this.userInfoIsPreventDefault;
            },
            btnUserInfoIsAuth: function btnUserInfoIsAuth() {
              return this.userInfoIsAuth;
            },
            btnUserInfoIsRegister: function btnUserInfoIsRegister() {
              return this.userInfoIsRegister;
            },
            btnUserInfoIsRefresh: function btnUserInfoIsRefresh() {
              return this.userInfoIsRefresh;
            },
            btnUserInfoIsForceRefresh: function btnUserInfoIsForceRefresh() {
              return this.userInfoIsForceRefresh;
            }
          }),
          methods: I(I({}, (0, i.mapMutations)(["assign"])), {}, {
            btnGetPhoneNumber: function btnGetPhoneNumber(e) {
              e.detail.encryptedData ? (c.default.log("CompButton btnGetPhoneNumber 获取手机号", e), this.assign({
                cacheAuthPhoneNumberData: e.detail
              }), this.$emit("getPhoneNumber", e)) : c.default.log("CompButton btnGetPhoneNumber 获取手机号异常", e);
            },
            btnClick: function btnClick(e) {
              "_getUserProfile" !== this.btnOpenType ? this.btnOpenType || (c.default.log("CompButton btnClick 正常点击", e), this.$emit("onClick", e)) : this.btnGetUserProfile();
            },
            handleAgreeprivacyauthorization: function handleAgreeprivacyauthorization() {
              this.$emit("onAgreeprivacyauthorization");
            },
            refreshUserInfo: function refreshUserInfo(e) {
              var t = this;
              return (0, a.default)(f.default.mark(function n() {
                var r, o;
                return f.default.wrap(function(n) {
                  while (1) switch (n.prev = n.next) {
                    case 0:
                      try {
                        r = JSON.parse(e.detail.rawData), o = c.default.cloneDeep(t.profile), (t.btnUserInfoIsForceRefresh || o.nickName !== r.nickName || o.avatar !== r.avatarUrl) && (c.default.log("CompButton: refreshUserInfo 更新用户信息", e), o.nickName = r.nickName, o.avatar = r.avatarUrl, t.assign({
                          profile: o
                        }), l.default.refresh({
                          userInfoEncrypted: e.detail.encryptedData,
                          phoneCode: e.detail.code,
                          userInfoIv: e.detail.iv
                        }));
                      } catch (f) {
                        c.default.log("CompButton refreshUserInfo 更新用户信息异常", f);
                      }
                    case 1:
                    case "end":
                      return n.stop();
                  }
                }, n);
              }))();
            },
            afterGetUserInfo: function afterGetUserInfo(e) {
              return this.btnUserInfoIsRefresh && (c.default.log("CompButton afterGetUserInfo 刷新用户信息"), this.refreshUserInfo(e)), this.btnUserInfoIsPreventDefault ? (c.default.log("CompButton afterGetUserInfo 阻止默认行为"), void this.$emit("getUserInfo", e)) : this.btnUserInfoIsRegister && (c.default.log("CompButton afterGetUserInfo 检测到是否跳转注册页"), !this.token) ? (c.default.log("CompButton afterGetUserInfo 当前无 token，不进行跳转"), this.assign({
                cacheAuthUserInfoData: e.detail
              }), void p.default.to({
                page: "login"
              })) : (c.default.log("CompButton afterGetUserInfo 触发 getUserInfo 回调"), void this.$emit("getUserInfo", e));
            },
            btnGetUserInfo: function btnGetUserInfo(e) {
              if (e.detail.encryptedData) {
                if (c.default.log("CompButton btnGetUserInfo 获取用户信息", e), this.btnUserInfoIsAuth && (c.default.log("CompButton btnGetUserInfo 检测到跳转到授权页"), !this.token)) return c.default.log("CompButton btnGetUserInfo 当前无 token，进行跳转"), void p.default.to({
                  page: "auth"
                });
                this.afterGetUserInfo(e);
              } else c.default.log("CompButton btnGetUserInfo 获取用户信息异常", e);
            },
            btnGetUserProfile: function btnGetUserProfile() {
              var e = this;
              return (0, a.default)(f.default.mark(function t() {
                var n, o, a, u;
                return f.default.wrap(function(t) {
                  while (1) switch (t.prev = t.next) {
                    case 0:
                      if (!e.btnUserInfoIsSkip) {
                        t.next = 4;
                        break;
                      }
                      return c.default.log("CompButton btnGetUserProfile 跳过授权直接回调"), e.$emit("getUserInfo", {}), t.abrupt("return");
                    case 4:
                      if (!e.btnUserInfoIsAuth) {
                        t.next = 10;
                        break;
                      }
                      if (c.default.log("CompButton btnGetUserProfile 检测到跳转到授权页"), e.token) {
                        t.next = 10;
                        break;
                      }
                      return c.default.log("CompButton btnGetUserInfo 当前无 token，进行跳转"), p.default.to({
                        page: "auth"
                      }), t.abrupt("return");
                    case 10:
                      return t.next = 12, r.getUserProfile({
                        desc: "用于完善用户资料"
                      });
                    case 12:
                      if (n = t.sent, o = (0, s.default)(n, 2), a = o[0], u = o[1], !a) {
                        t.next = 19;
                        break;
                      }
                      return c.default.log("CompButton btnGetUserProfile 获取用户信息异常", a), t.abrupt("return");
                    case 19:
                      c.default.log("CompButton btnGetUserProfile 获取用户信息", u), e.afterGetUserInfo({
                        detail: u
                      });
                    case 21:
                    case "end":
                      return t.stop();
                  }
                }, t);
              }))();
            }
          })
        };
        t.default = b;
      }).call(this, n("3223")["default"], n("df3c")["default"]);
    },
    "7b79": function b79(e, t, n) {


      var r = n("2ef4"),
        o = n.n(r);
      o.a;
    },
    ae9d: function ae9d(e, t, n) {


      n.d(t, "b", function() {
        return r;
      }), n.d(t, "c", function() {
        return o;
      }), n.d(t, "a", function() {});
      var r = function r() {
          var e = this.$createElement;
          this._self._c;
        },
        o = [];
    },
    ca5a: function ca5a(e, t, n) {


      n.r(t);
      var r = n("ae9d"),
        o = n("f0b1");
      for (var f in o)["default"].indexOf(f) < 0 && function(e) {
        n.d(t, e, function() {
          return o[e];
        });
      }(f);
      n("7b79");
      var s = n("828b"),
        a = Object(s["a"])(o["default"], r["b"], r["c"], !1, null, "7c23a10c", null, !1, r["a"], void 0);
      t["default"] = a.exports;
    },
    f0b1: function f0b1(e, t, n) {


      n.r(t);
      var r = n("60c4"),
        o = n.n(r);
      for (var f in r)["default"].indexOf(f) < 0 && function(e) {
        n.d(t, e, function() {
          return r[e];
        });
      }(f);
      t["default"] = o.a;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['components/comp-button/comp-button-create-component', {
    'components/comp-button/comp-button-create-component': function componentsCompButtonCompButtonCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("ca5a"));
    }
  },
  [
    ['components/comp-button/comp-button-create-component']
  ]
]);