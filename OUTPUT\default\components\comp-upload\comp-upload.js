require("../../@babel/runtime/helpers/Arrayincludes");
(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["components/comp-upload/comp-upload"], {
    "1a21": function a21(e, t, n) {


      var a = n("8064"),
        r = n.n(a);
      r.a;
    },
    6821: function _(e, t, n) {


      n.d(t, "b", function() {
        return r;
      }), n.d(t, "c", function() {
        return i;
      }), n.d(t, "a", function() {
        return a;
      });
      var a = {
          compImage: function compImage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-image/comp-image")]).then(n.bind(null, "31bf"));
          },
          compIcon: function compIcon() {
            return n.e("components/comp-icon/comp-icon").then(n.bind(null, "84bb2"));
          },
          compDialog: function compDialog() {
            return n.e("components/comp-dialog/comp-dialog").then(n.bind(null, "c5eb"));
          }
        },
        r = function r() {
          var e = this.$createElement,
            t = (this._self._c, !this.disabled && this.imgs.length < this.count);
          this.$mp.data = Object.assign({}, {
            $root: {
              g0: t
            }
          });
        },
        i = [];
    },
    8064: function _(e, t, n) {},
    bb56: function bb56(e, t, n) {


      n.r(t);
      var a = n("cf38"),
        r = n.n(a);
      for (var i in a)["default"].indexOf(i) < 0 && function(e) {
        n.d(t, e, function() {
          return a[e];
        });
      }(i);
      t["default"] = r.a;
    },
    c51f: function c51f(e, t, n) {


      n.r(t);
      var a = n("6821"),
        r = n("bb56");
      for (var i in r)["default"].indexOf(i) < 0 && function(e) {
        n.d(t, e, function() {
          return r[e];
        });
      }(i);
      n("1a21");
      var o = n("828b"),
        u = Object(o["a"])(r["default"], a["b"], a["c"], !1, null, "62ad5e56", null, !1, a["a"], void 0);
      t["default"] = u.exports;
    },
    cf38: function cf38(e, t, n) {


      (function(e, a) {
        var r = n("47a9");
        Object.defineProperty(t, "__esModule", {
          value: !0
        }), t.default = void 0;
        var i = r(n("7eb4")),
          o = r(n("ee10")),
          u = r(n("8b9c")),
          c = r(n("a23b")),
          s = r(n("c148")),
          l = n("e55c"),
          d = {
            props: {
              disabled: {
                type: Boolean,
                default: !1
              },
              isAuto: {
                type: Boolean,
                default: !0
              },
              tartgetDirectory: {
                type: String,
                default: ""
              },
              addName: {
                type: String,
                default: "添加图片"
              },
              addSubname: {
                type: String,
                default: ""
              },
              addPadding: {
                type: String,
                default: "0rpx"
              },
              type: {
                type: String,
                default: "default"
              },
              count: {
                type: Number,
                default: 1
              },
              marginRight: {
                type: String,
                default: "20rpx"
              },
              marginBottom: {
                type: String,
                default: "20rpx"
              },
              height: {
                type: String,
                default: "200rpx"
              },
              width: {
                type: String,
                default: "200rpx"
              },
              border: {
                type: String,
                default: ""
              },
              borderRadius: {
                type: String,
                default: "4rpx"
              },
              needMd5: {
                type: Boolean,
                default: !1
              },
              limitSize: {
                type: Number,
                default: 5242880
              },
              limitType: {
                type: String,
                default: ""
              },
              sizeType: {
                type: String,
                default: "compressed"
              },
              md5CheckOne: {
                type: Boolean,
                default: !1
              }
            },
            data: function data() {
              return {
                subname: "在获取手机相册服务之前，请仔细阅读",
                activeSubname: "《会员中心小程序隐私保护指引》",
                subnameBottom: "如果你同意该指引，请点击“同意”才可以使用相册功能",
                imgs: [],
                md5s: []
              };
            },
            computed: {
              emitChange: function emitChange() {
                var e = this.imgs,
                  t = this.md5s;
                this.$emit("change", {
                  imgs: e,
                  md5s: t
                });
              }
            },
            methods: {
              reset: function reset() {
                this.imgs = [], this.md5s = [];
              },
              preview: function preview(t) {
                e.previewImage({
                  urls: this.imgs,
                  current: t
                });
              },
              handleClose: function handleClose() {
                this.$refs.refDialog.close();
              },
              handleActive: function handleActive() {
                a.openPrivacyContract({
                  success: function success() {},
                  fail: function fail() {},
                  complete: function complete() {}
                });
              },
              handleAuthorization: function handleAuthorization() {
                this.$refs.refDialog.close(), this.upload();
              },
              upload: function upload() {
                var t = arguments,
                  n = this;
                return (0, o.default)(i.default.mark(function r() {
                  var o, l, d, f, p, m, g, h, b, y, x, v, w, S, k, M;
                  return i.default.wrap(function(r) {
                    while (1) switch (r.prev = r.next) {
                      case 0:
                        if (o = t.length > 0 && void 0 !== t[0] && t[0], n.isAuto || o) {
                          r.next = 4;
                          break;
                        }
                        return n.$emit("upload"), r.abrupt("return");
                      case 4:
                        return l = !1, r.next = 7, a.getPrivacySetting({
                          success: function success(e) {
                            l = e.needAuthorization;
                          }
                        });
                      case 7:
                        if (!l) {
                          r.next = 10;
                          break;
                        }
                        return n.$refs.refDialog.open(), r.abrupt("return");
                      case 10:
                        return r.next = 12, a.chooseImage({
                          count: 1,
                          sizeType: ["compressed"],
                          sourceType: ["album", "camera"]
                        });
                      case 12:
                        if (d = r.sent, d && d.tempFilePaths && d.tempFilePaths.length) {
                          r.next = 15;
                          break;
                        }
                        return r.abrupt("return");
                      case 15:
                        if (f = !1, !n.limitType) {
                          r.next = 22;
                          break;
                        }
                        if (p = d.tempFilePaths[0], m = p.substr(p.indexOf(".") + 1), g = n.limitType.split(",") || [], g.includes(m)) {
                          r.next = 22;
                          break;
                        }
                        return r.abrupt("return", e.showModal({
                          title: "提示",
                          content: "图片格式不符合要求，请更换图片",
                          showCancel: !1
                        }));
                      case 22:
                        h = d.tempFiles || [], b = 0;
                      case 24:
                        if (!(b < h.length)) {
                          r.next = 54;
                          break;
                        }
                        if (y = h[b], !(y.size > n.limitSize)) {
                          r.next = 29;
                          break;
                        }
                        return f = !0, r.abrupt("continue", 51);
                      case 29:
                        if (x = "", !n.needMd5) {
                          r.next = 41;
                          break;
                        }
                        return r.next = 33, n.getFileMd5(d.tempFilePaths[b]);
                      case 33:
                        if (x = r.sent, !n.md5CheckOne) {
                          r.next = 40;
                          break;
                        }
                        return r.next = 37, s.default.checkMdOne(x);
                      case 37:
                        if (v = r.sent, "200" === v.data.code) {
                          r.next = 40;
                          break;
                        }
                        return r.abrupt("return", e.showModal({
                          title: "提示",
                          content: "".concat(v.data.msg, " "),
                          confirmColor: "#306dff",
                          confirmText: "确认",
                          showCancel: !1
                        }));
                      case 40:
                        console.log("获取到md5", x);
                      case 41:
                        return r.next = 43, u.default.uploadFile(y.path, n.type, n.tartgetDirectory);
                      case 43:
                        if (w = r.sent, S = c.default.get(w, "code", 0), k = c.default.get(w, "data", ""), 0 === S && k) {
                          r.next = 49;
                          break;
                        }
                        return e.showModal({
                          title: "提示",
                          content: "".concat(w.msg, " ").concat(S),
                          showCancel: !1
                        }), r.abrupt("break", 54);
                      case 49:
                        n.imgs.push(k), n.md5s.push(x);
                      case 51:
                        b++, r.next = 24;
                        break;
                      case 54:
                        f && (M = n.limitSize / 1024 / 1024, e.showModal({
                          title: "提示",
                          content: "图片最大尺寸为 ".concat(M, "M"),
                          showCancel: !1
                        }));
                      case 55:
                      case "end":
                        return r.stop();
                    }
                  }, r);
                }))();
              },
              getFileMd5: function getFileMd5(e) {
                return (0, o.default)(i.default.mark(function t() {
                  return i.default.wrap(function(t) {
                    while (1) switch (t.prev = t.next) {
                      case 0:
                        return t.abrupt("return", new Promise(function(t, n) {
                          a.getFileSystemManager().readFile({
                            filePath: e,
                            success: function success(e) {
                              console.log("wx.getFileSystemManager().readFile success", e);
                              var n = new l.ArrayBuffer();
                              n.append(e.data);
                              var a = n.end(!1);
                              console.log(a), t(a);
                            },
                            fail: function fail(e) {
                              console.error(e), n(e);
                            }
                          });
                        }));
                      case 1:
                      case "end":
                        return t.stop();
                    }
                  }, t);
                }))();
              },
              del: function del(e) {
                var t = this;
                this.imgs = this.imgs.filter(function(n, a) {
                  return n === e && t.md5s.splice(a, 1), n !== e;
                });
              }
            }
          };
        t.default = d;
      }).call(this, n("df3c")["default"], n("3223")["default"]);
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['components/comp-upload/comp-upload-create-component', {
    'components/comp-upload/comp-upload-create-component': function componentsCompUploadCompUploadCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("c51f"));
    }
  },
  [
    ['components/comp-upload/comp-upload-create-component']
  ]
]);