(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["components/comp-tab/comp-tab"], {
    "428f": function f(t, e, n) {


      var a = n("47a9");
      Object.defineProperty(e, "__esModule", {
        value: !0
      }), e.default = void 0;
      var i = a(n("7eb4")),
        u = a(n("ee10")),
        r = a(n("a23b")),
        c = {
          props: {
            tabs: {
              type: Array,
              default: []
            }
          },
          data: function data() {
            return {
              active: 0,
              view: 0,
              btnWidth: 0
            };
          },
          mounted: function mounted() {
            var t = this;
            return (0, u.default)(i.default.mark(function e() {
              var n;
              return i.default.wrap(function(e) {
                while (1) switch (e.prev = e.next) {
                  case 0:
                    return e.next = 2, r.default.getElementInfo(".btn", t);
                  case 2:
                    n = e.sent, t.btnWidth = n.width - 10;
                  case 4:
                  case "end":
                    return e.stop();
                }
              }, e);
            }))();
          },
          methods: {
            select: function select(t, e) {
              this.active = e;
              var n = this.tabs.length,
                a = e - 1;
              a < 0 && (a = 0), a > n - 1 && (a = n - 1), this.view = a, this.$emit("select", {
                data: t,
                active: this.active
              });
            }
          }
        };
      e.default = c;
    },
    "7f36": function f36(t, e, n) {


      n.r(e);
      var a = n("428f"),
        i = n.n(a);
      for (var u in a)["default"].indexOf(u) < 0 && function(t) {
        n.d(e, t, function() {
          return a[t];
        });
      }(u);
      e["default"] = i.a;
    },
    a0d1: function a0d1(t, e, n) {


      n.d(e, "b", function() {
        return a;
      }), n.d(e, "c", function() {
        return i;
      }), n.d(e, "a", function() {});
      var a = function a() {
          var t = this.$createElement;
          this._self._c;
        },
        i = [];
    },
    c2e8: function c2e8(t, e, n) {


      var a = n("e654"),
        i = n.n(a);
      i.a;
    },
    cda6: function cda6(t, e, n) {


      n.r(e);
      var a = n("a0d1"),
        i = n("7f36");
      for (var u in i)["default"].indexOf(u) < 0 && function(t) {
        n.d(e, t, function() {
          return i[t];
        });
      }(u);
      n("c2e8");
      var r = n("828b"),
        c = Object(r["a"])(i["default"], a["b"], a["c"], !1, null, "e8ab23a0", null, !1, a["a"], void 0);
      e["default"] = c.exports;
    },
    e654: function e654(t, e, n) {}
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['components/comp-tab/comp-tab-create-component', {
    'components/comp-tab/comp-tab-create-component': function componentsCompTabCompTabCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("cda6"));
    }
  },
  [
    ['components/comp-tab/comp-tab-create-component']
  ]
]);