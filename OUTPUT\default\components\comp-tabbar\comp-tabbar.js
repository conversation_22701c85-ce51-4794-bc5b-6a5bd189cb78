(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["components/comp-tabbar/comp-tabbar"], {
    "2b52": function b52(t, e, i) {


      var n = i("d386"),
        c = i.n(n);
      c.a;
    },
    "4e23": function e23(t, e, i) {


      i.r(e);
      var n = i("6fce"),
        c = i.n(n);
      for (var a in n)["default"].indexOf(a) < 0 && function(t) {
        i.d(e, t, function() {
          return n[t];
        });
      }(a);
      e["default"] = c.a;
    },
    "6a9d": function a9d(t, e, i) {


      i.d(e, "b", function() {
        return n;
      }), i.d(e, "c", function() {
        return c;
      }), i.d(e, "a", function() {});
      var n = function n() {
          var t = this.$createElement;
          this._self._c;
        },
        c = [];
    },
    "6d43": function d43(t, e, i) {


      i.r(e);
      var n = i("6a9d"),
        c = i("4e23");
      for (var a in c)["default"].indexOf(a) < 0 && function(t) {
        i.d(e, t, function() {
          return c[t];
        });
      }(a);
      i("2b52");
      var o = i("828b"),
        r = Object(o["a"])(c["default"], n["b"], n["c"], !1, null, "7310f83d", null, !1, n["a"], void 0);
      e["default"] = r.exports;
    },
    "6fce": function fce(t, e, i) {


      var n = i("47a9");
      Object.defineProperty(e, "__esModule", {
        value: !0
      }), e.default = void 0;
      var c = n(i("a23b")),
        a = n(i("5e82")),
        o = {
          props: {
            type: {
              type: String,
              default: "course"
            }
          },
          data: function data() {
            return {
              active: ""
            };
          },
          computed: {
            tabData: function tabData() {
              return "course" === this.type ? [{
                id: 1,
                path: "/pages-course/course/course",
                isRedirect: !0,
                name: "视频",
                icon: "/static/icon-video.png",
                iconActive: "/static/icon-video-active.png"
              }, {
                id: 2,
                path: "/pages/discover/proxy?from=course",
                isRedirect: !0,
                name: "发现",
                icon: "/static/icon-discover.png",
                iconActive: "/static/icon-discover-active.png"
              }, {
                id: 3,
                path: "/pages/service/proxy?from=course",
                isRedirect: !0,
                name: "服务",
                icon: "/static/icon-service.png",
                iconActive: "/static/icon-service-active.png"
              }, {
                id: 4,
                path: "/pages/index/index",
                name: "更多",
                icon: "/static/icon-home.png",
                iconActive: "/static/icon-home-active.png"
              }] : [];
            }
          },
          mounted: function mounted() {
            var t = c.default.getRoute(),
              e = this.tabData.find(function(e) {
                return e.path.indexOf(t.route) > -1;
              }) || {};
            this.active = e.id || "";
          },
          methods: {
            switchTab: function switchTab(t) {
              this.active = t.id, a.default.redirectTo({
                fullPath: t.path
              });
            }
          }
        };
      e.default = o;
    },
    d386: function d386(t, e, i) {}
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['components/comp-tabbar/comp-tabbar-create-component', {
    'components/comp-tabbar/comp-tabbar-create-component': function componentsCompTabbarCompTabbarCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("6d43"));
    }
  },
  [
    ['components/comp-tabbar/comp-tabbar-create-component']
  ]
]);