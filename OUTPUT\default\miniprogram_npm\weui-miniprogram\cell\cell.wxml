<view ariaRole="{{ariaRole}}" bindtap="navigateTo" class="weui-cell {{link?'weui-cell_access':''}} {{extClass}} {{outerClass}} {{inForm?' weui-cell-inform':''}}{{inline?'':'weui-cell_label-block'}}" hoverClass="{{hover?'weui-cell_active weui-active':extHoverClass}}">
    <view class="weui-cell__hd {{iconClass}}" wx:if="{{hasHeader}}">
        <image class="weui-cell__icon" mode="aspectFit" src="{{icon}}" wx:if="{{icon}}"></image>
        <slot name="icon" wx:else></slot>
        <block wx:if="{{inForm}}">
            <view class="weui-label" wx:if="{{title}}">{{title}}</view>
            <slot name="title" wx:else></slot>
        </block>
        <block wx:else>
            <block wx:if="{{title}}">{{title}}</block>
            <slot name="title" wx:else></slot>
        </block>
    </view>
    <view class="weui-cell__bd" wx:if="{{hasBody}}">
        <block wx:if="{{value}}">{{value}}</block>
        <slot wx:else></slot>
    </view>
    <view class="weui-cell__ft weui-cell__ft_in-access {{footerClass}}" wx:if="{{hasFooter}}">
        <block wx:if="{{footer}}">{{footer}}</block>
        <slot name="footer" wx:else></slot>
        <icon color="#E64340" size="23" type="warn" wx:if="{{showError&&error}}"></icon>
    </view>
</view>
