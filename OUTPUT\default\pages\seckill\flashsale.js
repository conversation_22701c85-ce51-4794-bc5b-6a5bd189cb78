(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/seckill/flashsale"], {
    "2c11": function c11(t, e, n) {


      (function(t, r) {
        var a = n("47a9");
        Object.defineProperty(e, "__esModule", {
          value: !0
        }), e.default = void 0;
        var o = a(n("7eb4")),
          i = a(n("af34")),
          c = a(n("ee10")),
          u = a(n("7ca3")),
          s = n("8f59"),
          d = a(n("a13b")),
          l = a(n("c148")),
          h = a(n("5e82")),
          f = n("b3c5");

        function p(t, e) {
          var n = Object.keys(t);
          if (Object.getOwnPropertySymbols) {
            var r = Object.getOwnPropertySymbols(t);
            e && (r = r.filter(function(e) {
              return Object.getOwnPropertyDescriptor(t, e).enumerable;
            })), n.push.apply(n, r);
          }
          return n;
        }

        function g(t) {
          for (var e = 1; e < arguments.length; e++) {
            var n = null != arguments[e] ? arguments[e] : {};
            e % 2 ? p(Object(n), !0).forEach(function(e) {
              (0, u.default)(t, e, n[e]);
            }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(n)) : p(Object(n)).forEach(function(e) {
              Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(n, e));
            });
          }
          return t;
        }
        var v = (0, f.getConfig)(),
          w = v.subMsgTemplateId,
          b = {
            data: function data() {
              return {
                pageTitle: "",
                showFixedHeader: !1,
                tabsOffsetTop: 0,
                pageScrollTop: 0,
                scrollTimer: null,
                lastScrollTop: 0,
                page: 1,
                pageSize: 10,
                hasMore: !0,
                isLoading: !1,
                currentTabIndex: 0,
                currentTabDate: "",
                promotionType: "2",
                countdownTimer: null,
                countdownLabel: "距开始",
                countdownDays: "00",
                countdownHours: "00",
                countdownMinutes: "00",
                countdownSeconds: "00",
                countdownStatus: 0,
                dateList: [],
                productList: [],
                isStart: !1,
                beginTime: "",
                endTime: "",
                reservedProducts: [],
                czz: "",
                wd: "",
                tabStatusCache: {},
                tabProductCache: {}
              };
            },
            computed: g(g({}, (0, s.mapState)(["token"])), {}, {
              currentDate: function currentDate() {
                return this.dateList[this.currentTabIndex] || "";
              },
              getStatusText: function getStatusText() {
                var t = this;
                return function(e, n) {
                  if (t.tabStatusCache[e]) return t.tabStatusCache[e];
                  var r = "";
                  if (t.isToday(e)) {
                    var a = t.getCurrentTabProducts(e);
                    r = a.length > 0 && a.every(function(t) {
                      return t.productPercent >= 1;
                    }) ? "已售罄" : "抢购中";
                  } else {
                    var o = t.dateList.filter(function(e) {
                      return !t.isToday(e);
                    });
                    if (o.length > 0) {
                      var i = o[0];
                      r = e === i ? "即将开始" : "未开始";
                    } else r = "即将开始";
                  }
                  return t.tabStatusCache[e] = r, r;
                };
              }
            }),
            onPageScroll: function onPageScroll(t) {
              t.scrollTop > 20 ? this.pageTitle = "回馈商城" : this.pageTitle = "";
            },
            onLoad: function onLoad() {
              this.getSeckillAndGroupData(), this.loadReservedProductsFromCache();
            },
            onShow: function onShow() {
              t.getStorageSync("wd") && (this.wd = t.getStorageSync("wd"), t.removeStorageSync("wd"), t.showToast({
                icon: "none",
                title: 3 == this.wd ? "前往创维商场购买商品可获得维豆" : "点击任意商品详情页，浏览十秒以上可获得维豆",
                duration: 3e3
              }));
              t.getStorageSync("czz") && (this.czz = t.getStorageSync("czz"), t.removeStorageSync("czz"), t.showToast({
                icon: "none",
                title: {
                  1: "点击任意商品详情页，分享商品后可赚取成长值",
                  2: "点击任意商品详情页，浏览十秒以上可赚取成长值",
                  3: "前往创维商场购买商品可赚取成长值"
                } [this.czz],
                duration: 3e3
              }));
            },
            onReady: function onReady() {},
            onReachBottom: function onReachBottom() {
              console.log("触底了，加载更多数据"), this.loadMoreData();
            },
            onPullDownRefresh: function onPullDownRefresh() {
              console.log("下拉刷新");
            },
            onUnload: function onUnload() {
              clearInterval(this.countdownTimer);
            },
            methods: {
              getCurrentTabProducts: function getCurrentTabProducts(t) {
                return t === this.currentTabDate ? this.productList : this.tabProductCache[t] || [];
              },
              getActivityTitle: function getActivityTitle(t) {
                if (!t) return "疯狂星期二";
                var e = new Date(t),
                  n = e.getDay();
                return 2 === n ? "疯狂星期二" : 5 === n ? "黑色星期五" : "疯狂星期二";
              },
              goToProductDetail: function goToProductDetail(e) {
                if (e.activityUuid && e.productNo) {
                  var n = "/pages-cwshop/goods/detail/detail?skuNo=" + e.productNo + "&promotionUuid=" + e.activityUuid;
                  1 == this.czz || 2 == this.czz ? n = "/pages-cwshop/goods/detail/detail?skuNo=" + e.productNo + "&promotionUuid=" + e.activityUuid + "&czz=" + this.czz : 2 == this.wd && (n = "/pages-cwshop/goods/detail/detail?skuNo=" + e.productNo + "&promotionUuid=" + e.activityUuid + "&wd=" + this.wd), t.navigateTo({
                    url: n
                  });
                } else t.showToast({
                  title: "商品信息不完整",
                  icon: "none"
                });
              },
              getSeckillAndGroupData: function getSeckillAndGroupData() {
                var t = this;
                return (0, c.default)(o.default.mark(function e() {
                  var n, r, a, i, c, u;
                  return o.default.wrap(function(e) {
                    while (1) switch (e.prev = e.next) {
                      case 0:
                        if (n = new Date(), r = n.getDay(), 2 === r ? (a = new Date(n), i = new Date(n), i.setDate(n.getDate() + 3)) : 5 === r ? (i = new Date(n), a = new Date(n), a.setDate(n.getDate() + 4)) : r < 2 ? (a = new Date(n), a.setDate(n.getDate() + (2 - r)), i = new Date(n), i.setDate(n.getDate() + (5 - r))) : 3 === r || 4 === r ? (i = new Date(n), i.setDate(n.getDate() + (5 - r)), a = new Date(n), a.setDate(n.getDate() + (9 - r))) : (a = new Date(n), a.setDate(n.getDate() + 3), i = new Date(n), i.setDate(n.getDate() + 6)), c = function c(t) {
                            var e = t.getFullYear(),
                              n = String(t.getMonth() + 1).padStart(2, "0"),
                              r = String(t.getDate()).padStart(2, "0");
                            return "".concat(e, "-").concat(n, "-").concat(r);
                          }, u = [{
                            date: a,
                            string: c(a)
                          }, {
                            date: i,
                            string: c(i)
                          }].sort(function(t, e) {
                            return t.date - e.date;
                          }), t.dateList = u.map(function(t) {
                            return t.string;
                          }), !(t.dateList.length > 0)) {
                          e.next = 10;
                          break;
                        }
                        return t.currentTabDate = t.dateList[0], e.next = 10, t.getSeckillAndGroupBuyPageData();
                      case 10:
                      case "end":
                        return e.stop();
                    }
                  }, e);
                }))();
              },
              loadMoreData: function loadMoreData() {
                this.hasMore && !this.isLoading && this.getSeckillAndGroupBuyPageData();
              },
              getSeckillAndGroupBuyPageData: function getSeckillAndGroupBuyPageData() {
                var t = this;
                return (0, c.default)(o.default.mark(function e() {
                  var n, r, a;
                  return o.default.wrap(function(e) {
                    while (1) switch (e.prev = e.next) {
                      case 0:
                        if (!t.isLoading) {
                          e.next = 2;
                          break;
                        }
                        return e.abrupt("return");
                      case 2:
                        return console.log("当前日期：", t.currentTabDate), n = t.isToday(t.currentTabDate), r = {
                          nowPage: t.page.toString(),
                          pageShow: t.pageSize.toString(),
                          nowDate: t.currentTabDate,
                          promotionType: t.promotionType,
                          type: n ? "0" : "1"
                        }, t.isLoading = !0, e.prev = 6, e.next = 9, d.default.getSeckillAndGroupBuyPageAPI(r);
                      case 9:
                        if (a = e.sent, !n || a.productList.length) {
                          e.next = 15;
                          break;
                        }
                        return r.type = "1", e.next = 14, d.default.getSeckillAndGroupBuyPageAPI(r);
                      case 14:
                        a = e.sent;
                      case 15:
                        t.productList = a.productList, t.productList.length > 0 && (t.tabProductCache[t.currentTabDate] = (0, i.default)(t.productList), t.updateTabStatusCache(t.currentTabDate, t.productList)), a.productList.length > 0 ? (t.beginTime = a.beginTime, t.endTime = a.endTime) : (t.beginTime = t.currentTabDate + " 00:00:00", t.endTime = ""), t.startCountdown(), t.isLoading = !1, t.page < a.totalPage ? t.page++ : t.hasMore = !1, console.log("秒杀商品列表 ：", a), e.next = 28;
                        break;
                      case 24:
                        e.prev = 24, e.t0 = e["catch"](6), t.isLoading = !1, console.error("获取秒杀商品列表失败：", e.t0);
                      case 28:
                      case "end":
                        return e.stop();
                    }
                  }, e, null, [
                    [6, 24]
                  ]);
                }))();
              },
              startCountdown: function startCountdown() {
                var t = this;
                this.countdownTimer && clearInterval(this.countdownTimer), this.countdownTimer = setInterval(function() {
                  t.updateCountdown();
                }, 1e3), this.updateCountdown();
              },
              updateCountdown: function updateCountdown() {
                var t = new Date().getTime(),
                  e = 0,
                  n = 0;
                if (this.beginTime) {
                  var r = this.beginTime.replace(" ", "T");
                  e = new Date(r).getTime();
                }
                if (this.endTime) {
                  var a = this.endTime.replace(" ", "T");
                  n = new Date(a).getTime();
                }
                var o = 0;
                if (e - t >= 0 ? (o = e - t, this.countdownLabel = "距开始", this.countdownStatus = 0) : n - t >= 0 ? (o = n - t, this.countdownLabel = "距结束", this.countdownStatus = 1) : (this.countdownLabel = "已结束", this.countdownStatus = 2, clearInterval(this.countdownTimer), o = 0), o > 0) {
                  var i = Math.floor(o / 864e5),
                    c = Math.floor(o % 864e5 / 36e5),
                    u = Math.floor(o % 36e5 / 6e4),
                    s = Math.floor(o % 6e4 / 1e3);
                  this.countdownDays = i < 10 ? "0" + i : "" + i, this.countdownHours = c < 10 ? "0" + c : "" + c, this.countdownMinutes = u < 10 ? "0" + u : "" + u, this.countdownSeconds = s < 10 ? "0" + s : "" + s;
                } else this.countdownDays = "00", this.countdownHours = "00", this.countdownMinutes = "00", this.countdownSeconds = "00";
              },
              updateTabStatusCache: function updateTabStatusCache(t, e) {
                this.isToday(t) && (e.length > 0 && e.every(function(t) {
                  return t.productPercent >= 1;
                }) ? this.tabStatusCache[t] = "已售罄" : this.tabStatusCache[t] = "抢购中");
              },
              changeTab: function changeTab(t) {
                this.currentTabDate && this.productList.length > 0 && (this.tabProductCache[this.currentTabDate] = (0, i.default)(this.productList), this.updateTabStatusCache(this.currentTabDate, this.productList)), this.currentTabIndex = t, this.currentTabDate = this.dateList[t];
                var e = this.tabProductCache[this.currentTabDate];
                e && e.length > 0 ? (this.productList = (0, i.default)(e), this.page = Math.ceil(e.length / this.pageSize) + 1, this.hasMore = !0) : (this.page = 1, this.hasMore = !0, this.productList = []), this.getSeckillAndGroupBuyPageData();
              },
              formatDate: function formatDate(t) {
                if (!t) return "";
                var e = t.split("-");
                return e.length >= 3 ? "".concat(e[1], "-").concat(e[2]) : t;
              },
              isToday: function isToday(t) {
                if (!t) return !0;
                var e = new Date(),
                  n = e.getFullYear(),
                  r = String(e.getMonth() + 1).padStart(2, "0"),
                  a = String(e.getDate()).padStart(2, "0"),
                  o = "".concat(n, "-").concat(r, "-").concat(a);
                return t === o;
              },
              handleReservation: function handleReservation(e) {
                var n = this;
                return (0, c.default)(o.default.mark(function r() {
                  var a;
                  return o.default.wrap(function(r) {
                    while (1) switch (r.prev = r.next) {
                      case 0:
                        if (r.prev = 0, n.token) {
                          r.next = 4;
                          break;
                        }
                        return t.showModal({
                          title: "提示",
                          content: "当前用户未进行授权，是否跳转授权登录页面",
                          success: function success(t) {
                            t.confirm ? h.default.to({
                              page: "auth",
                              query: {
                                back: !0
                              }
                            }) : t.cancel && console.log("用户点击取消");
                          }
                        }), r.abrupt("return");
                      case 4:
                        return a = [w], r.next = 7, n.requestSubscription(a, e);
                      case 7:
                        r.next = 12;
                        break;
                      case 9:
                        r.prev = 9, r.t0 = r["catch"](0), console.error("预约处理失败:", r.t0);
                      case 12:
                      case "end":
                        return r.stop();
                    }
                  }, r, null, [
                    [0, 9]
                  ]);
                }))();
              },
              requestSubscription: function requestSubscription(e, n) {
                var a = this;
                return (0, c.default)(o.default.mark(function i() {
                  return o.default.wrap(function(i) {
                    while (1) switch (i.prev = i.next) {
                      case 0:
                        r.requestSubscribeMessage({
                          tmplIds: e,
                          success: function() {
                            var r = (0, c.default)(o.default.mark(function r(i) {
                              return o.default.wrap(function(r) {
                                while (1) switch (r.prev = r.next) {
                                  case 0:
                                    if ("accept" !== i[e[0]]) {
                                      r.next = 5;
                                      break;
                                    }
                                    return r.next = 3, a.performReservation(n);
                                  case 3:
                                    r.next = 6;
                                    break;
                                  case 5:
                                    "reject" === i[e[0]] ? a.guideToSettings() : "ban" === i[e[0]] && t.showToast({
                                      title: "订阅消息已被禁用",
                                      icon: "none"
                                    });
                                  case 6:
                                  case "end":
                                    return r.stop();
                                }
                              }, r);
                            }));
                            return function(t) {
                              return r.apply(this, arguments);
                            };
                          }(),
                          fail: function() {
                            var t = (0, c.default)(o.default.mark(function t(e) {
                              return o.default.wrap(function(t) {
                                while (1) switch (t.prev = t.next) {
                                  case 0:
                                    console.error("订阅消息请求失败:", e), 20004 === e.errCode && a.guideToSettings();
                                  case 2:
                                  case "end":
                                    return t.stop();
                                }
                              }, t);
                            }));
                            return function(e) {
                              return t.apply(this, arguments);
                            };
                          }()
                        });
                      case 1:
                      case "end":
                        return i.stop();
                    }
                  }, i);
                }))();
              },
              guideToSettings: function guideToSettings() {
                t.showModal({
                  title: "订阅消息",
                  content: "您需要开启订阅消息权限才能收到秒杀提醒，是否前往设置？",
                  success: function success(e) {
                    e.confirm && t.openSetting({
                      success: function() {
                        var e = (0, c.default)(o.default.mark(function e(n) {
                          return o.default.wrap(function(e) {
                            while (1) switch (e.prev = e.next) {
                              case 0:
                                n.subscriptionsSetting && n.subscriptionsSetting.mainSwitch && t.showToast({
                                  title: "订阅权限已开启",
                                  icon: "success"
                                });
                              case 1:
                              case "end":
                                return e.stop();
                            }
                          }, e);
                        }));
                        return function(t) {
                          return e.apply(this, arguments);
                        };
                      }()
                    });
                  }
                });
              },
              performReservation: function performReservation(e) {
                var n = this;
                return (0, c.default)(o.default.mark(function r() {
                  var a, i, c, u;
                  return o.default.wrap(function(r) {
                    while (1) switch (r.prev = r.next) {
                      case 0:
                        return a = t.getStorageSync("phone") || "2", i = {
                          idempotentKey: "".concat(a, "_").concat(e.productNo, "_").concat(e.activityUuid),
                          originalPrice: e.basePrice,
                          pagePath: "/pages-cwshop/goods/detail/detail?skuNo=".concat(e.productNo, "&promotionUuid=").concat(e.activityUuid),
                          productImageUrl: e.productImg,
                          productName: e.productName,
                          seckillEndTime: n.endTime,
                          seckillPrice: e.activityPrice,
                          seckillStartTime: n.beginTime,
                          source: "cwshop"
                        }, r.next = 4, n.showConfirmModal({
                          title: "预约确认",
                          content: "确定要预约「".concat(e.productName, "」吗？")
                        });
                      case 4:
                        if (c = r.sent, c) {
                          r.next = 7;
                          break;
                        }
                        return r.abrupt("return");
                      case 7:
                        return r.prev = 7, r.next = 10, l.default.seckillAppointment(i);
                      case 10:
                        u = r.sent, "200" !== u.code && "400" !== u.code || n.addToReservedList(e), r.next = 17;
                        break;
                      case 14:
                        r.prev = 14, r.t0 = r["catch"](7), console.error("预约请求失败:", r.t0);
                      case 17:
                      case "end":
                        return r.stop();
                    }
                  }, r, null, [
                    [7, 14]
                  ]);
                }))();
              },
              showConfirmModal: function showConfirmModal(e) {
                var n = e.title,
                  r = e.content,
                  a = e.confirmText,
                  o = void 0 === a ? "确定" : a,
                  i = e.cancelText,
                  c = void 0 === i ? "取消" : i;
                return new Promise(function(e) {
                  t.showModal({
                    title: n,
                    content: r,
                    confirmText: o,
                    cancelText: c,
                    success: function success(t) {
                      return e(t.confirm);
                    },
                    fail: function fail() {
                      return e(!1);
                    }
                  });
                });
              },
              addToReservedList: function addToReservedList(t) {
                this.reservedProducts.push({
                  productNo: t.productNo,
                  activityUuid: t.activityUuid,
                  reserveTime: Date.now(),
                  expireTime: this.beginTime
                }), this.saveReservedProductsToCache();
              },
              loadReservedProductsFromCache: function loadReservedProductsFromCache() {
                try {
                  var e = t.getStorageSync("reservedProducts");
                  if (e) {
                    var n = JSON.parse(e),
                      r = new Date().getTime();
                    n = n.filter(function(t) {
                      var e = t.expireTime.replace(" ", "T"),
                        n = new Date(e).getTime();
                      return n > r;
                    }), this.reservedProducts = n, this.saveReservedProductsToCache();
                  }
                } catch (a) {
                  console.error("加载预约商品缓存失败:", a);
                }
              },
              saveReservedProductsToCache: function saveReservedProductsToCache() {
                try {
                  t.setStorageSync("reservedProducts", JSON.stringify(this.reservedProducts));
                } catch (e) {
                  console.error("保存预约商品缓存失败:", e);
                }
              },
              isReserved: function isReserved(t) {
                return this.reservedProducts.some(function(e) {
                  return e.productNo === t.productNo && e.activityUuid === t.activityUuid;
                });
              },
              delay: function delay(t) {
                return new Promise(function(e) {
                  return setTimeout(e, t);
                });
              }
            },
            beforeDestroy: function beforeDestroy() {
              this.countdownTimer && clearInterval(this.countdownTimer), this.saveReservedProductsToCache();
            }
          };
        e.default = b;
      }).call(this, n("df3c")["default"], n("3223")["default"]);
    },
    "2fac": function fac(t, e, n) {


      n.d(e, "b", function() {
        return a;
      }), n.d(e, "c", function() {
        return o;
      }), n.d(e, "a", function() {
        return r;
      });
      var r = {
          compPage: function compPage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-page/comp-page")]).then(n.bind(null, "fccb"));
          }
        },
        a = function a() {
          var t = this,
            e = t.$createElement,
            n = (t._self._c, t.dateList.length),
            r = t.dateList.length,
            a = t.__map(t.dateList, function(e, n) {
              var r = t.__get_orig(e),
                a = 2 === t.dateList.length && 0 === t.currentTabIndex && 0 === n,
                o = 2 === t.dateList.length && 0 === t.currentTabIndex && 1 === n,
                i = 2 === t.dateList.length && 1 === t.currentTabIndex && 0 === n,
                c = 2 === t.dateList.length && 1 === t.currentTabIndex && 1 === n,
                u = 2 === t.dateList.length && 0 === t.currentTabIndex && 0 === n,
                s = 2 === t.dateList.length && 0 === t.currentTabIndex && 1 === n,
                d = 2 === t.dateList.length && 1 === t.currentTabIndex && 0 === n,
                l = 2 === t.dateList.length && 1 === t.currentTabIndex && 1 === n,
                h = t.getActivityTitle(e),
                f = t.formatDate(e),
                p = t.getStatusText(e, n);
              return {
                $orig: r,
                g1: a,
                g2: o,
                g3: i,
                g4: c,
                g6: u,
                g7: s,
                g8: d,
                g9: l,
                m0: h,
                m1: f,
                m2: p
              };
            }),
            o = t.productList.length,
            i = o > 0 ? t.__map(t.productList, function(e, n) {
              var r = t.__get_orig(e),
                a = e.begin ? null : t.isReserved(e),
                o = !e.begin || e.productPercent >= 1 ? null : Math.floor(100 * e.productPercent);
              return {
                $orig: r,
                m3: a,
                g11: o
              };
            }) : null,
            c = t.productList.length;
          t._isMounted || (t.e0 = function(e, n) {
            var r = arguments[arguments.length - 1].currentTarget.dataset,
              a = r.eventParams || r["event-params"];
            n = a.item;
            e.stopPropagation(), !t.isReserved(n) && t.handleReservation(n);
          }), t.$mp.data = Object.assign({}, {
            $root: {
              g0: n,
              g5: r,
              l0: a,
              g10: o,
              l1: i,
              g12: c
            }
          });
        },
        o = [];
    },
    "36b6": function b6(t, e, n) {


      n.r(e);
      var r = n("2c11"),
        a = n.n(r);
      for (var o in r)["default"].indexOf(o) < 0 && function(t) {
        n.d(e, t, function() {
          return r[t];
        });
      }(o);
      e["default"] = a.a;
    },
    "65c7": function c7(t, e, n) {


      (function(t, e) {
        var r = n("47a9");
        n("5c38");
        r(n("3240"));
        var a = r(n("c648"));
        t.__webpack_require_UNI_MP_PLUGIN__ = n, e(a.default);
      }).call(this, n("3223")["default"], n("df3c")["createPage"]);
    },
    c648: function c648(t, e, n) {


      n.r(e);
      var r = n("2fac"),
        a = n("36b6");
      for (var o in a)["default"].indexOf(o) < 0 && function(t) {
        n.d(e, t, function() {
          return a[t];
        });
      }(o);
      n("f5c6");
      var i = n("828b"),
        c = Object(i["a"])(a["default"], r["b"], r["c"], !1, null, null, null, !1, r["a"], void 0);
      e["default"] = c.exports;
    },
    d735: function d735(t, e, n) {},
    f5c6: function f5c6(t, e, n) {


      var r = n("d735"),
        a = n.n(r);
      a.a;
    }
  },
  [
    ["65c7", "common/runtime", "common/vendor"]
  ]
]);