<view class="comp-info-flow-card data-v-bfa596a0">
    <view bindtap="__e" class="is-card--10 data-v-bfa596a0" data-event-opts="{{[ [ 'tap',[ [ 'clickCard',['$event'] ] ] ] ]}}" wx:if="{{type===10}}">
        <view class="card__hd flex-row data-v-bfa596a0">
            <slot name="hd"></slot>
            <view class="card__name data-v-bfa596a0">
                <view class="multi-nowrap data-v-bfa596a0">{{name}}</view>
            </view>
        </view>
        <view class="card__bd flex-row data-v-bfa596a0" wx:if="{{$root.g0===1}}">
            <comp-image bind:__l="__l" class="data-v-bfa596a0" height="167rpx" isServer="{{true}}" isVideoCover="{{isVideo}}" name="{{imgs[0].img}}" radius="8rpx" vueId="b82e2438-1" width="224rpx"></comp-image>
            <view class="card__video-time flex-row col-center data-v-bfa596a0" wx:if="{{videoTime}}">{{videoTime}}</view>
        </view>
        <view class="card__bd flex-row data-v-bfa596a0" wx:if="{{$root.g1>1}}">
            <comp-image bind:__l="__l" class="img-margin data-v-bfa596a0" height="157rpx" isServer="{{true}}" isVideoCover="{{isVideo}}" name="{{item.img}}" radius="8rpx" vueId="{{'b82e2438-2-'+i}}" width="212rpx" wx:for="{{imgs}}" wx:for-index="i" wx:key="i"></comp-image>
        </view>
        <view class="card__ft flex-row space-between data-v-bfa596a0" wx:if="{{isCommentCount||isLikedCount||time}}">
            <view class="flex-row flex-one data-v-bfa596a0">
                <text class="data-v-bfa596a0">
                    <text class="data-v-bfa596a0">{{viewCount||0}}</text>
                    <text class="spacing data-v-bfa596a0">阅读量</text>
                    <text class="data-v-bfa596a0">{{likedCount||0}}</text>
                    <text class="spacing data-v-bfa596a0">赞</text>
                </text>
            </view>
            <view class="data-v-bfa596a0" wx:if="{{time}}">{{time}}</view>
        </view>
    </view>
    <view bindtap="__e" class="is-card--11 data-v-bfa596a0" data-event-opts="{{[ [ 'tap',[ [ 'clickCard',['$event'] ] ] ] ]}}" wx:if="{{type===11}}">
        <view class="card__hd flex-row data-v-bfa596a0">
            <view class="card__img data-v-bfa596a0">
                <comp-image bind:__l="__l" class="data-v-bfa596a0" height="105rpx" isServer="{{true}}" isVideoCover="{{isVideo}}" name="{{imgs[0].img}}" radius="8rpx" vueId="b82e2438-3" width="140rpx"></comp-image>
                <view class="card__video-time flex-row col-center data-v-bfa596a0" wx:if="{{videoTime}}">{{videoTime}}</view>
            </view>
            <view class="flex-one card__name data-v-bfa596a0">
                <view class="multi-nowrap data-v-bfa596a0">
                    <rich-text nodes="{{name}}"></rich-text>
                </view>
            </view>
        </view>
    </view>
</view>
