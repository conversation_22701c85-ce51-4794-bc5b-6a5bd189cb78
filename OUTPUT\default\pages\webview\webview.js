(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/webview/webview"], {
    "397f": function f(e, t, n) {


      n.d(t, "b", function() {
        return o;
      }), n.d(t, "c", function() {
        return i;
      }), n.d(t, "a", function() {
        return r;
      });
      var r = {
          compPage: function compPage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-page/comp-page")]).then(n.bind(null, "fccb"));
          }
        },
        o = function o() {
          var e = this.$createElement;
          this._self._c;
        },
        i = [];
    },
    "50db": function db(e, t, n) {


      n.r(t);
      var r = n("397f"),
        o = n("a877");
      for (var i in o)["default"].indexOf(i) < 0 && function(e) {
        n.d(t, e, function() {
          return o[e];
        });
      }(i);
      var a = n("828b"),
        c = Object(a["a"])(o["default"], r["b"], r["c"], !1, null, null, null, !1, r["a"], void 0);
      t["default"] = c.exports;
    },
    a877: function a877(e, t, n) {


      n.r(t);
      var r = n("ed61"),
        o = n.n(r);
      for (var i in r)["default"].indexOf(i) < 0 && function(e) {
        n.d(t, e, function() {
          return r[e];
        });
      }(i);
      t["default"] = o.a;
    },
    e9eb: function e9eb(e, t, n) {


      (function(e, t) {
        var r = n("47a9");
        n("5c38");
        r(n("3240"));
        var o = r(n("50db"));
        e.__webpack_require_UNI_MP_PLUGIN__ = n, t(o.default);
      }).call(this, n("3223")["default"], n("df3c")["createPage"]);
    },
    ed61: function ed61(e, t, n) {


      (function(e) {
        var r = n("47a9");
        Object.defineProperty(t, "__esModule", {
          value: !0
        }), t.default = void 0;
        var o = r(n("7eb4")),
          i = r(n("ee10")),
          a = r(n("7ca3")),
          c = n("8f59"),
          u = r(n("bdc5")),
          f = r(n("a23b")),
          l = r(n("8b9c")),
          s = r(n("5e82")),
          d = n("b3c5");

        function b(e, t) {
          var n = Object.keys(e);
          if (Object.getOwnPropertySymbols) {
            var r = Object.getOwnPropertySymbols(e);
            t && (r = r.filter(function(t) {
              return Object.getOwnPropertyDescriptor(e, t).enumerable;
            })), n.push.apply(n, r);
          }
          return n;
        }
        var p = (0, d.getConfig)(),
          g = {
            data: function data() {
              return {
                isFirst: !0,
                url: "",
                backgroundColor: "#ffffff",
                frontColor: "#000000",
                tag: ""
              };
            },
            computed: function(e) {
              for (var t = 1; t < arguments.length; t++) {
                var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? b(Object(n), !0).forEach(function(t) {
                  (0, a.default)(e, t, n[t]);
                }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : b(Object(n)).forEach(function(t) {
                  Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
                });
              }
              return e;
            }({}, (0, c.mapState)(["profile"])),
            onShareAppMessage: function onShareAppMessage() {
              var e = "/pages/webview/webview?url=".concat(encodeURIComponent(this.url), "&backgroundColor=").concat(this.backgroundColor, "&fontColor=").concat(this.frontColor);
              return this.tag && (e = "/pages/webview/webview?tag=".concat(this.tag)), f.default.generateShareInfo({
                path: e,
                imageUrl: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/share_logo.jpg"
              });
            },
            onShow: function onShow() {
              f.default.log("Webview onShow", "当前 URL ".concat(this.url)), this.isFirst || this.refresh();
            },
            onHide: function onHide() {
              this.url = "", f.default.log("Webview onHide", "当前 URL ".concat(this.url));
            },
            methods: {
              fetch: function fetch() {
                var e = f.default.getRoute().options || {};
                f.default.log("Webview fetch 当前 query 为", "".concat(e)), e.tag ? this.initWithTag(e) : this.init(e);
              },
              afterFetch: function afterFetch() {
                this.isFirst = !1;
              },
              init: function init() {
                var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                  n = decodeURIComponent(t.url || ""),
                  r = t.backgroundColor || "#ffffff",
                  o = t.frontColor || "#000000";
                this.url = n, this.backgroundColor = r, this.frontColor = o, e.setNavigationBarColor({
                  frontColor: o,
                  backgroundColor: r,
                  fail: function fail(e) {
                    f.default.log("Webview: setNavigationBarColor fail", e);
                  }
                }), u.default.completeTask({
                  isDelay: !0,
                  type: "webview"
                });
              },
              initWithTag: function initWithTag() {
                var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                  t = e.tag;
                "questionnaire" !== t || this.initTagQuestionnaire(e);
              },
              initTagQuestionnaire: function initTagQuestionnaire() {
                var t = arguments,
                  n = this;
                return (0, i.default)(o.default.mark(function r() {
                  var i, a, c, d, b, g;
                  return o.default.wrap(function(r) {
                    while (1) switch (r.prev = r.next) {
                      case 0:
                        if (i = t.length > 0 && void 0 !== t[0] ? t[0] : {}, n.profile) {
                          r.next = 3;
                          break;
                        }
                        return r.abrupt("return");
                      case 3:
                        return e.showLoading(), r.next = 6, l.default.banner({
                          position: "bnindextop"
                        });
                      case 6:
                        if (a = r.sent, c = f.default.get(a, "data.data", []) || [], d = encodeURIComponent("/hdtool/index?id=134324798357656"), b = c.find(function(e) {
                            return e.tokenUrl && e.tokenUrl.indexOf(d) > -1;
                          }), b) {
                          r.next = 12;
                          break;
                        }
                        return r.abrupt("return");
                      case 12:
                        if (g = !!Number(i.task), !g) {
                          r.next = 16;
                          break;
                        }
                        return r.next = 16, u.default.completeTask({
                          taskCodeDefault: "TS00037",
                          isDelay: !0,
                          type: "webview"
                        });
                      case 16:
                        return "prod" !== p.env && (b.tokenUrl = "https://test-uc-api.skyallhere.com/miniprogram/tp/duiba-nologin?dbredirect=%2F%2F74367.activity-1.m.duiba.com.cn%2Fchw%2Fvisual-editor%2Fskins%3Fid%3D131445"), r.next = 19, s.default.getUrlByTokenUrl({
                          tokenUrl: b.tokenUrl
                        });
                      case 19:
                        n.url = r.sent, e.hideLoading();
                      case 21:
                      case "end":
                        return r.stop();
                    }
                  }, r);
                }))();
              }
            }
          };
        t.default = g;
      }).call(this, n("df3c")["default"]);
    }
  },
  [
    ["e9eb", "common/runtime", "common/vendor"]
  ]
]);