.is-card--10.data-v-bfa596a0 {
    padding: 30rpx 40rpx
}

.is-card--10 .card__name.data-v-bfa596a0 {
    font-size: var(--font-size-34);
    font-weight: var(--font-weight-bold);
    line-height: 48rpx
}

.is-card--10 .card__name .multi-nowrap.data-v-bfa596a0 {
    -webkit-line-clamp: 3
}

.is-card--10 .card__hd .card__img.data-v-bfa596a0 {
    margin-left: 30rpx;
    position: relative
}

.is-card--10 .card__hd .card__video-time.data-v-bfa596a0 {
    background: rgba(0,0,0,.3);
    border-radius: 20rpx;
    bottom: 8rpx;
    color: var(--color-fill-grey-inverse);
    font-size: var(--font-size-22);
    height: 40rpx;
    line-height: 30rpx;
    padding: 0 15rpx;
    position: absolute;
    right: 0
}

.is-card--10 .card__bd.data-v-bfa596a0 {
    padding-top: 20rpx
}

.img-margin.data-v-bfa596a0 {
    margin-right: 17rpx
}

.card__bd.data-v-bfa596a0:last-child {
    margin-right: 0
}

.is-card--10 .card__ft.data-v-bfa596a0 {
    color: var(--color-text-weak);
    font-size: var(--font-size-22);
    line-height: 30rpx;
    padding: 10rpx 0 0
}

.is-card--10 .card__ft .spacing.data-v-bfa596a0 {
    padding: 0 6rpx
}

.is-card--11.data-v-bfa596a0 {
    padding: 30rpx 40rpx
}

.is-card--11 .card__hd .card__img.data-v-bfa596a0 {
    margin-right: 24rpx;
    position: relative
}

.is-card--11 .card__hd .card__name.data-v-bfa596a0 {
    color: var(--color-text-subtitle);
    font-size: 28rpx
}

.is-card--11 .card__hd .card__video-time.data-v-bfa596a0 {
    background: rgba(0,0,0,.3);
    border-radius: 20rpx;
    bottom: 8rpx;
    color: var(--color-fill-grey-inverse);
    font-size: var(--font-size-22);
    height: 40rpx;
    line-height: 30rpx;
    padding: 0 15rpx;
    position: absolute;
    right: 0
}
