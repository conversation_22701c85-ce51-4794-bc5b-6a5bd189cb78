<view class="page-product data-v-07af3e95">
    <view class="hd flex-row space-between data-v-07af3e95">
        <view bindlongpress="__e" class="title data-v-07af3e95" data-event-opts="{{[ [ 'longpress',[ [ 'openDebug',['$event'] ] ] ] ]}}">我的产品</view>
        <view bindtap="__e" class="more flex-row col-center data-v-07af3e95" data-event-opts="{{[ [ 'tap',[ [ 'clickMore',['$event'] ] ] ] ]}}" wx:if="{{$root.g0}}">
            <view class="data-v-07af3e95">更多</view>
            <comp-icon bind:__l="__l" class="data-v-07af3e95" color="rgba(0, 0, 0, 0.30)" icon="iconarrow_right_o" size="24rpx" vueId="b10b868c-1"></comp-icon>
        </view>
    </view>
    <view class="bd data-v-07af3e95">
        <view class="empty__wrap data-v-07af3e95" wx:if="{{!$root.g1}}">
            <view class="empty flex-col row-center col-center data-v-07af3e95">
                <view class="empty__name data-v-07af3e95">添加我的产品</view>
                <view class="empty__name data-v-07af3e95">上传发票领维豆</view>
                <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-07af3e95" data-event-opts="{{[ [ '^getUserInfo',[ ['clickAdd'] ] ] ]}}" openType="getUserInfo" vueId="b10b868c-2" vueSlots="{{['default']}}">
                    <view class="empty__add flex-row col-center row-center data-v-07af3e95">立即添加</view>
                </comp-button>
            </view>
        </view>
        <swiper class="data-v-07af3e95" nextMargin="{{$root.g2>1?'48rpx':'24rpx'}}" style="{{'height:'+'380rpx'+';'}}" wx:else>
            <swiper-item class="data-v-07af3e95" wx:for="{{$root.l0}}" wx:key="index">
                <view class="product__wrap data-v-07af3e95">
                    <view bindtap="__e" class="product data-v-07af3e95" data-event-opts="{{[ [ 'tap',[ [ 'clickProduct',['$event'] ] ] ] ]}}">
                        <view class="product__hd info flex-row data-v-07af3e95">
                            <view class="info__hd data-v-07af3e95">
                                <comp-image bind:__l="__l" class="data-v-07af3e95" height="144rpx" isServer="{{true}}" mode="widthFix" name="{{item[$orig].modeImg}}" vueId="{{'b10b868c-3-'+index}}" width="144rpx"></comp-image>
                                <view class="info__tag data-v-07af3e95">{{item[$orig].category}}</view>
                            </view>
                            <view class="info__bd flex-one flex-col space-between data-v-07af3e95">
                                <view class="info__mode data-v-07af3e95">{{item[$orig].mode}}</view>
                                <view class="info__code data-v-07af3e95">{{item[$orig].no}}</view>
                            </view>
                            <view class="info__index flex-row col-center row-center data-v-07af3e95">
                                <view class="current data-v-07af3e95">{{index+1}}</view>
                                <view class="data-v-07af3e95">{{'/ '+item.g3}}</view>
                            </view>
                        </view>
                        <view class="product__bd flex-row space-between data-v-07af3e95">
                            <view catchtap="__e" class="product__btn flex-col row-center col-center data-v-07af3e95" data-event-opts="{{[ [ 'tap',[ [ 'clickBtn',['$0','$1'],[ [ ['btns','',subIndex] ],[ ['list','',index] ] ] ] ] ] ]}}" wx:for="{{btns}}" wx:for-index="subIndex" wx:for-item="subItem" wx:key="subIndex">
                                <comp-icon bind:__l="__l" class="data-v-07af3e95" color="#000" icon="{{subItem.icon}}" size="56rpx" vueId="{{'b10b868c-4-'+index+'-'+subIndex}}"></comp-icon>
                                <view class="btn__name data-v-07af3e95">{{subItem.name}}</view>
                                <view class="btn__tag flex-row col-center row-center nowrap data-v-07af3e95" wx:if="{{subItem.tag}}">{{subItem.tag}}</view>
                            </view>
                        </view>
                    </view>
                </view>
            </swiper-item>
        </swiper>
    </view>
</view>
