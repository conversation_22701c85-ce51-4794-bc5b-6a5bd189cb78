function e(e, t, i) {
  return t in e ? Object.defineProperty(e, t, {
    value: i,
    enumerable: !0,
    configurable: !0,
    writable: !0
  }) : e[t] = i, e
}
var t, i = require("./util.js"),
  a = "https://api.milinzone.com";
Component({
  properties: {
    options: {
      type: Object,
      value: {
        autoplay: !0,
        loop: !0,
        avatar: "",
        shareSDK: !0,
        circleOfFriends: !0,
        userName: "",
        describe: "",
        userLikeState: !0,
        scaleIconState: !1
      },
      observer: function(e, t, i) {
        var a = this;
        this.setData({
          options: e
        }, function() {
          (e.width || e.height) && a.getChangeInfo(e)
        })
      }
    }
  },
  data: (t = {
    videoUrl: "",
    likeState: !1,
    likeNum: 0,
    friendCircle: "../../images/friend-circle.png",
    miLinIcon: "../../images/mi_lin.png",
    likeIcon: "../../images/like.png",
    likeSelectIcon: "../../images/like-select.png",
    weChatIcon: "../../images/wechat.png",
    scaleBig: "../../images/scale-big.png",
    scaleSmall: "../../images/scale-small.png",
    heightNum: 0,
    widthNum: 0,
    getVideoId: "",
    userLikeState: !0,
    avatar: "",
    shareSDK: "",
    circleOfFriends: "",
    userName: "",
    describe: ""
  }, e(t, "userLikeState", ""), e(t, "autoplay", !1), e(t, "mode", "contain"), e(t, "iconShow", !0), e(t, "scaleIconState", !1), e(t, "miLinIconState", !0), e(t, "containerWidth", 170), e(t, "containerBot", 160), e(t, "userImageHei", 98), e(t, "userImageWid", 98), e(t, "userLikeHei", 55), e(t, "userLikeWid", 60), e(t, "marginTop", 50), e(t, "userShareWid", 58), e(t, "userShareHei", 58), e(t, "userFriendWid", 58), e(t, "userFriendHei", 58), e(t, "miLinWid", 50), e(t, "miLinHei", 50), e(t, "containerPadding", 22), e(t, "titleFont", 32), e(t, "directionFont", 28), e(t, "bottomMargin", 30), e(t, "playStatue", !0), t),
  lifetimes: {
    attached: function() {
      var t, i = this,
        a = wx.getSystemInfoSync().windowHeight,
        n = wx.getSystemInfoSync().windowWidth;
      console.log(a), console.log(i.data.options);
      var o = i.getRpx(a),
        s = i.getRpx(n);
      this.setData((t = {
        heightNum: parseInt(o),
        widthNum: parseInt(s),
        userLikeState: i.data.options.userLikeState,
        avatar: i.data.options.avatar,
        shareSDK: i.data.options.shareSDK,
        circleOfFriends: i.data.options.circleOfFriends,
        userName: i.data.options.userName,
        describe: i.data.options.describe
      }, e(t, "userLikeState", i.data.options.userLikeState), e(t, "autoplay", !!i.data.options.autoplay && i.data.options.autoplay), e(t, "scaleIconState", !!i.data.options.scaleIconState && i.data.options.scaleIconState), t)), this.createDeviceId(), this.getInformation()
    },
    ready: function() {
      wx._setVideoContext = wx.createVideoContext("myVideo", this)
    }
  },
  methods: {
    getChangeInfo: function(e) {
      var t = this.data,
        i = t.userImageHei,
        a = t.userLikeHei,
        n = t.userLikeWid,
        o = t.marginTop,
        s = t.userShareHei,
        r = t.miLinHei,
        d = t.containerBot,
        c = t.containerWidth,
        u = t.containerPadding,
        l = t.titleFont,
        h = t.directionFont,
        g = t.bottomMargin;
      if (e.height && e.height <= 375 && e.width && e.width <= 300) this.setData({
        userLikeState: !1,
        miLinWid: 45,
        miLinHei: 45,
        containerBot: 50,
        miLinIconState: !1,
        containerWidth: 60,
        avatar: "",
        shareSDK: !1,
        circleOfFriends: !1,
        userName: "",
        describe: "",
        mode: "fill",
        iconShow: !1,
        scaleIconState: !!e.scaleIconState && e.scaleIconState
      });
      else if (e.height && e.height > 375) {
        var m = e.height / 1206,
          p = i * m,
          S = a * m,
          f = S * (n / a),
          v = o * m,
          I = s * m,
          k = r * m,
          w = d * m > 55 ? d * m : 55,
          y = e.width ? c * (e.width / 750) : c,
          L = u * m,
          D = l * m > 24 ? l * l < 32 ? l * l : 32 : 24,
          x = h * m > 22 ? h * m : 22,
          b = 1 == m ? 30 : e.height >= 700 ? g * m * .7 : e.height > 500 ? g * m * 1.3 : 0;
        e.width && e.width < 500 && this.setData({
          miLinIconState: !1
        }), this.setData({
          userImageHei: p,
          userImageWid: p,
          marginTop: v,
          userLikeHei: S,
          userLikeWid: f,
          userShareWid: I,
          userShareHei: I,
          userFriendHei: I,
          userFriendWid: I,
          miLinHei: k,
          miLinWid: k,
          containerBot: w,
          containerWidth: y,
          containerPadding: L,
          titleFont: D,
          directionFont: x,
          bottomMargin: b,
          iconShow: !0,
          scaleIconState: !!e.scaleIconState && e.scaleIconState
        })
      }
    },
    getRpx: function(e) {
      return e ? 750 * e / wx.getSystemInfoSync().windowWidth : 0
    },
    createDeviceId: function() {
      var e = wx.getStorageSync("pluginDeviceId");
      e || (e = i.uuid(), wx.setStorageSync("pluginDeviceId", e))
    },
    getInformation: function() {
      var e = this,
        t = this.data.options.videoId,
        i = wx.getStorageSync("pluginDeviceId"),
        n = void 0;
      n = t || "15f4fa0e43234e2c97820a9789c9bb86", this.setData({
        getVideoId: n
      }), n && wx.request({
        url: a + "/feeds/video/getVideoInfo?videoId=" + n + "&deviceId=" + i,
        method: "GET",
        header: {
          "content-type": "application/json",
          Authorization: "Bearer "
        },
        timeout: 5e3,
        success: function(t) {
          if (t.data && 0 == t.data.code) {
            console.log(t.data);
            var i = t.data.data;
            e.setData({
              videoUrl: i.playUrl,
              likeState: i.isLike,
              likeNum: i.likeNum
            })
          } else wx.showToast({
            title: "接口错误信息码" + t.code,
            icon: "none"
          })
        },
        fail: function(e) {}
      })
    },
    videoErrorCallback: function() {},
    videoScale: function() {
      this.triggerEvent("videoScale")
    },
    getPortrait: function() {
      this.triggerEvent("getPortrait")
    },
    getLikeInfo: function() {
      var e = this.data,
        t = e.likeState;
      e.likeNum;
      t ? this.removeGuestLike() : this.addGuestLike()
    },
    addGuestLike: function() {
      var e = this.data.getVideoId,
        t = wx.getStorageSync("pluginDeviceId"),
        i = this.data.likeNum,
        n = this;
      e && wx.request({
        url: a + "/feeds/video/addGuestLike?videoId=" + e + "&deviceId=" + t,
        method: "GET",
        header: {
          "content-type": "application/json",
          Authorization: "Bearer "
        },
        timeout: 5e3,
        success: function(e) {
          e.data && 0 == e.data.code ? n.setData({
            likeState: !0,
            likeNum: i + 1
          }) : wx.showToast({
            title: "接口错误信息码" + e.code,
            icon: "none"
          })
        },
        fail: function(e) {}
      })
    },
    removeGuestLike: function() {
      var e = this.data.getVideoId,
        t = wx.getStorageSync("pluginDeviceId"),
        i = this.data.likeNum,
        n = this;
      e && wx.request({
        url: a + "/feeds/video/removeGuestLike?videoId=" + e + "&deviceId=" + t,
        method: "DELETE",
        header: {
          "content-type": "application/json",
          Authorization: "Bearer "
        },
        timeout: 5e3,
        success: function(e) {
          e.data && 0 == e.data.code ? n.setData({
            likeState: !1,
            likeNum: i - 1
          }) : wx.showToast({
            title: "接口错误信息码" + e.code,
            icon: "none"
          })
        },
        fail: function(e) {}
      })
    },
    getShareSDK: function() {
      this.triggerEvent("getShareSDK")
    },
    getCircleOfFriends: function() {
      this.triggerEvent("getCircleOfFriends")
    },
    videoPlay: function() {
      var e = this.data.playStatue,
        t = this.data.options.videoId;
      e && (this.request({
        url: a + "/interactive/view",
        method: "PUT",
        header: {
          "content-type": "application/json"
        },
        data: {
          clickTypeId: 1,
          contentId: t,
          contentType: 2,
          createUid: null,
          toUid: null,
          deviceId: null
        },
        success: function(e) {}
      }), this.setData({
        playStatue: !1
      }))
    },
    videoEnd: function() {
      var e = {
        videoId: this.data.options.videoId,
        createUid: null,
        deviceId: null,
        terminalType: 2
      };
      this.setData({
        playStatue: !0
      }), this.request({
        url: a + "/feeds/stat/addVideoPlayEnd",
        method: "POST",
        header: {
          "content-type": "application/json"
        },
        data: e,
        success: function(e) {},
        fail: function(e) {}
      })
    }
  }
});