<view class="comp-packets flex-row col-center row-center data-v-b5a4dddc">
    <view class="main data-v-b5a4dddc">
        <slot></slot>
        <view class="close data-v-b5a4dddc" wx:if="{{isClose}}">
            <view catchtap="__e" class="close__main data-v-b5a4dddc" data-event-opts="{{[ [ 'tap',[ [ 'close',['$event'] ] ] ] ]}}">
                <comp-icon bind:__l="__l" class="data-v-b5a4dddc" color="rgba(255, 255, 255, .79)" icon="iconclose" size="56rpx" vueId="f071b23c-1"></comp-icon>
            </view>
        </view>
    </view>
</view>
