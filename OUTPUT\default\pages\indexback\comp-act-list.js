require("../../@babel/runtime/helpers/Arrayincludes");
(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/indexback/comp-act-list"], {
    "38d0": function d0(t, e, n) {


      (function(t) {
        var a = n("47a9");
        Object.defineProperty(e, "__esModule", {
          value: !0
        }), e.default = void 0;
        var i = a(n("5e82")),
          r = {
            components: {
              PagePanel: function PagePanel() {
                n.e("pages/indexback/page-panel").then(function() {
                  return resolve(n("f4ec"));
                }.bind(null, n)).catch(n.oe);
              }
            },
            props: {
              list: {
                type: Array,
                default: function _default() {
                  return [];
                }
              }
            },
            methods: {
              clickMore: function clickMore() {
                i.default.to({
                  page: "activityCenter"
                });
              },
              clickAct: function clickAct(e) {
                1 == e.jumpType ? ["wxff438d3c60c63fb6", "wxa1d1df2295fbe77e"].includes(e.miniProgramId) ? t.navigateTo({
                  url: e.miniProgramUrl
                }) : t.navigateToMiniProgram({
                  appId: e.miniProgramId,
                  path: e.miniProgramUrl,
                  success: function success() {
                    console.log("Navigate: toMp success");
                  },
                  fail: function fail(t) {
                    console.log("Navigate: toMp fail", t);
                  }
                }) : 2 == e.jumpType && i.default.to({
                  page: "webviewH5",
                  query: {
                    url: encodeURIComponent(e.miniProgramUrl)
                  }
                });
              },
              formatDate: function formatDate(t) {
                return t ? t.split(" ")[0] : "";
              }
            }
          };
        e.default = r;
      }).call(this, n("df3c")["default"]);
    },
    3953: function _(t, e, n) {


      var a = n("8aaf"),
        i = n.n(a);
      i.a;
    },
    "5e43d": function e43d(t, e, n) {


      n.d(e, "b", function() {
        return a;
      }), n.d(e, "c", function() {
        return i;
      }), n.d(e, "a", function() {});
      var a = function a() {
          var t = this,
            e = t.$createElement,
            n = (t._self._c, t.list.length),
            a = n > 0 ? t.__map(t.list.slice(1), function(e, n) {
              var a = t.__get_orig(e),
                i = t.formatDate(e.startTime),
                r = t.formatDate(e.endTime);
              return {
                $orig: a,
                m0: i,
                m1: r
              };
            }) : null;
          t._isMounted || (t.e0 = function(e, n) {
            var a = arguments[arguments.length - 1].currentTarget.dataset,
              i = a.eventParams || a["event-params"];
            n = i.item;
            return t.clickAct(n);
          }), t.$mp.data = Object.assign({}, {
            $root: {
              g0: n,
              l0: a
            }
          });
        },
        i = [];
    },
    "8aaf": function aaf(t, e, n) {},
    9198: function _(t, e, n) {


      n.r(e);
      var a = n("38d0"),
        i = n.n(a);
      for (var r in a)["default"].indexOf(r) < 0 && function(t) {
        n.d(e, t, function() {
          return a[t];
        });
      }(r);
      e["default"] = i.a;
    },
    d4e0: function d4e0(t, e, n) {


      n.r(e);
      var a = n("5e43d"),
        i = n("9198");
      for (var r in i)["default"].indexOf(r) < 0 && function(t) {
        n.d(e, t, function() {
          return i[t];
        });
      }(r);
      n("3953");
      var o = n("828b"),
        c = Object(o["a"])(i["default"], a["b"], a["c"], !1, null, "872dcafc", null, !1, a["a"], void 0);
      e["default"] = c.exports;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/indexback/comp-act-list-create-component', {
    'pages/indexback/comp-act-list-create-component': function pagesIndexbackCompActListCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("d4e0"));
    }
  },
  [
    ['pages/indexback/comp-act-list-create-component']
  ]
]);