(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["components/comp-swiper/comp-swiper"], {
    "055f": function f(e, t, n) {


      n.r(t);
      var o = n("e06a"),
        r = n.n(o);
      for (var a in o)["default"].indexOf(a) < 0 && function(e) {
        n.d(t, e, function() {
          return o[e];
        });
      }(a);
      t["default"] = r.a;
    },
    "13bd": function bd(e, t, n) {


      var o = n("3fe3"),
        r = n.n(o);
      r.a;
    },
    "3fe3": function fe3(e, t, n) {},
    "8ae3": function ae3(e, t, n) {


      n.r(t);
      var o = n("ebce"),
        r = n("055f");
      for (var a in r)["default"].indexOf(a) < 0 && function(e) {
        n.d(t, e, function() {
          return r[e];
        });
      }(a);
      n("13bd");
      var i = n("828b"),
        u = Object(i["a"])(r["default"], o["b"], o["c"], !1, null, "d0047d96", null, !1, o["a"], void 0);
      t["default"] = u.exports;
    },
    e06a: function e06a(e, t, n) {


      var o = n("47a9");
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.default = void 0;
      var r = o(n("a23b")),
        a = o(n("5e82")),
        i = {
          components: {
            CompImage: function CompImage() {
              Promise.all([n.e("common/vendor"), n.e("components/comp-image/comp-image")]).then(function() {
                return resolve(n("31bf"));
              }.bind(null, n)).catch(n.oe);
            },
            CompButton: function CompButton() {
              Promise.all([n.e("common/vendor"), n.e("components/comp-button/comp-button")]).then(function() {
                return resolve(n("ca5a"));
              }.bind(null, n)).catch(n.oe);
            }
          },
          props: {
            list: {
              type: Array,
              default: function _default() {
                return [];
              }
            },
            height: {
              type: Number,
              default: 97
            },
            interval: {
              type: Number,
              default: 5e3
            },
            isIndicatorLeft: {
              type: Boolean,
              default: !1
            },
            isIndicatorCenter: {
              type: Boolean,
              default: !1
            },
            indicatorBottom: {
              type: String,
              default: "5%"
            },
            openType: {
              type: String,
              default: ""
            },
            radius: {
              type: String,
              default: ""
            }
          },
          data: function data() {
            return {
              current: 0
            };
          },
          methods: {
            change: function change(e) {
              this.current = e.detail.current;
            },
            navigateTo: function navigateTo(e) {
              a.default.toBannerUrl(e);
            },
            clickItem: function clickItem(e) {
              e.openType || (r.default.log("CompSwiper: clickItem", e), this.navigateTo(e));
            },
            authClickItem: function authClickItem(e) {
              r.default.log("CompSwiper: authClickItem", e), this.navigateTo(e);
            }
          }
        };
      t.default = i;
    },
    ebce: function ebce(e, t, n) {


      n.d(t, "b", function() {
        return r;
      }), n.d(t, "c", function() {
        return a;
      }), n.d(t, "a", function() {
        return o;
      });
      var o = {
          compButton: function compButton() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-button/comp-button")]).then(n.bind(null, "ca5a"));
          },
          compImage: function compImage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-image/comp-image")]).then(n.bind(null, "31bf"));
          }
        },
        r = function r() {
          var e = this,
            t = e.$createElement,
            n = (e._self._c, e.__map(e.list, function(t, n) {
              var o = e.__get_orig(t),
                r = t.url.indexOf("jingdong");
              return {
                $orig: o,
                g0: r
              };
            }));
          e.$mp.data = Object.assign({}, {
            $root: {
              l0: n
            }
          });
        },
        a = [];
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['components/comp-swiper/comp-swiper-create-component', {
    'components/comp-swiper/comp-swiper-create-component': function componentsCompSwiperCompSwiperCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("8ae3"));
    }
  },
  [
    ['components/comp-swiper/comp-swiper-create-component']
  ]
]);