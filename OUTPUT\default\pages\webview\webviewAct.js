(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/webview/webviewAct"], {
    "44e0": function e0(e, t, n) {


      n.r(t);
      var r = n("c6cd"),
        o = n("f36a");
      for (var a in o)["default"].indexOf(a) < 0 && function(e) {
        n.d(t, e, function() {
          return o[e];
        });
      }(a);
      var i = n("828b"),
        c = Object(i["a"])(o["default"], r["b"], r["c"], !1, null, null, null, !1, r["a"], void 0);
      t["default"] = c.exports;
    },
    8586: function _(e, t, n) {


      (function(e, r) {
        var o = n("47a9");
        Object.defineProperty(t, "__esModule", {
          value: !0
        }), t.default = void 0;
        var a = o(n("7eb4")),
          i = o(n("ee10")),
          c = o(n("7ca3")),
          u = n("8f59"),
          l = o(n("bdc5")),
          f = o(n("a23b")),
          s = o(n("8b9c")),
          d = o(n("5e82")),
          p = n("b3c5");

        function g(e, t) {
          var n = Object.keys(e);
          if (Object.getOwnPropertySymbols) {
            var r = Object.getOwnPropertySymbols(e);
            t && (r = r.filter(function(t) {
              return Object.getOwnPropertyDescriptor(e, t).enumerable;
            })), n.push.apply(n, r);
          }
          return n;
        }
        var b = (0, p.getConfig)(),
          h = {
            data: function data() {
              return {
                isFirst: !0,
                url: "",
                backgroundColor: "#ffffff",
                frontColor: "#000000",
                tag: ""
              };
            },
            computed: function(e) {
              for (var t = 1; t < arguments.length; t++) {
                var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? g(Object(n), !0).forEach(function(t) {
                  (0, c.default)(e, t, n[t]);
                }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : g(Object(n)).forEach(function(t) {
                  Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
                });
              }
              return e;
            }({}, (0, u.mapState)(["profile"])),
            onShareAppMessage: function onShareAppMessage() {
              var e = "/pages/webview/webviewAct?url=".concat(encodeURIComponent(this.url), "&backgroundColor=").concat(this.backgroundColor, "&fontColor=").concat(this.frontColor);
              return this.tag && (e = "/pages/webview/webviewAct?tag=".concat(this.tag)), f.default.generateShareInfo({
                path: e,
                imageUrl: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/share_logo.jpg"
              });
            },
            onShow: function onShow() {
              f.default.log("Webview onShow", "当前 URL ".concat(this.url)), this.isFirst || this.refresh();
            },
            onHide: function onHide() {
              this.url = "", e.redirectTo({
                url: "/pages/index/index"
              }), f.default.log("Webview onHide", "当前 URL ".concat(this.url));
            },
            methods: {
              fetch: function fetch() {
                var e = f.default.getRoute().options || {},
                  t = getCurrentPages(),
                  n = t[t.length - 1],
                  r = n.options || {};
                f.default.log("pageObj newQuery.url", "".concat(r.url)), f.default.log("Webview fetch 当前 query 为", "".concat(e)), e.tag ? this.initWithTag(e) : this.init(e);
              },
              afterFetch: function afterFetch() {
                this.isFirst = !1;
              },
              init: function init() {
                var t = arguments,
                  n = this;
                return (0, i.default)(a.default.mark(function o() {
                  var i, c, u, s, p, g;
                  return a.default.wrap(function(o) {
                    while (1) switch (o.prev = o.next) {
                      case 0:
                        return i = t.length > 0 && void 0 !== t[0] ? t[0] : {}, c = b.duibaActUrl, u = "", i.url ? u = decodeURIComponent(i.url || "") : (console.log("没有取到option.url 用固定抽奖活动链接重启小程序"), u = decodeURIComponent(c), e.reLaunch({
                          url: "/pages/webview/webviewAct?url=".concat(c)
                        })), console.log("url", u), o.next = 7, d.default.getUrlByTokenUrl({
                          tokenUrl: u
                        });
                      case 7:
                        s = o.sent, console.log("tokenUrl", s), p = i.backgroundColor || "#ffffff", g = i.frontColor || "#000000", n.url = s, n.backgroundColor = p, n.frontColor = g, r.setNavigationBarColor({
                          frontColor: g,
                          backgroundColor: p,
                          fail: function fail(e) {
                            f.default.log("Webview: setNavigationBarColor fail", e);
                          }
                        }), l.default.completeTask({
                          isDelay: !0,
                          type: "webview"
                        });
                      case 16:
                      case "end":
                        return o.stop();
                    }
                  }, o);
                }))();
              },
              initWithTag: function initWithTag() {
                var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                  t = e.tag;
                "questionnaire" !== t || this.initTagQuestionnaire(e);
              },
              initTagQuestionnaire: function initTagQuestionnaire() {
                var e = arguments,
                  t = this;
                return (0, i.default)(a.default.mark(function n() {
                  var o, i, c, u, p, g;
                  return a.default.wrap(function(n) {
                    while (1) switch (n.prev = n.next) {
                      case 0:
                        if (o = e.length > 0 && void 0 !== e[0] ? e[0] : {}, t.profile) {
                          n.next = 3;
                          break;
                        }
                        return n.abrupt("return");
                      case 3:
                        return r.showLoading(), n.next = 6, s.default.banner({
                          position: "bnindextop"
                        });
                      case 6:
                        if (i = n.sent, c = f.default.get(i, "data.data", []) || [], u = encodeURIComponent("/hdtool/index?id=134324798357656"), p = c.find(function(e) {
                            return e.tokenUrl && e.tokenUrl.indexOf(u) > -1;
                          }), p) {
                          n.next = 12;
                          break;
                        }
                        return n.abrupt("return");
                      case 12:
                        if (g = !!Number(o.task), !g) {
                          n.next = 16;
                          break;
                        }
                        return n.next = 16, l.default.completeTask({
                          taskCodeDefault: "TS00037",
                          isDelay: !0,
                          type: "webview"
                        });
                      case 16:
                        return "prod" !== b.env && (p.tokenUrl = "https://test-uc-api.skyallhere.com/miniprogram/tp/duiba-nologin?dbredirect=%2F%2F74367.activity-1.m.duiba.com.cn%2Fchw%2Fvisual-editor%2Fskins%3Fid%3D131445"), n.next = 19, d.default.getUrlByTokenUrl({
                          tokenUrl: p.tokenUrl
                        });
                      case 19:
                        t.url = n.sent, r.hideLoading();
                      case 21:
                      case "end":
                        return n.stop();
                    }
                  }, n);
                }))();
              }
            }
          };
        t.default = h;
      }).call(this, n("3223")["default"], n("df3c")["default"]);
    },
    c6cd: function c6cd(e, t, n) {


      n.d(t, "b", function() {
        return o;
      }), n.d(t, "c", function() {
        return a;
      }), n.d(t, "a", function() {
        return r;
      });
      var r = {
          compPage: function compPage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-page/comp-page")]).then(n.bind(null, "fccb"));
          }
        },
        o = function o() {
          var e = this.$createElement;
          this._self._c;
        },
        a = [];
    },
    f36a: function f36a(e, t, n) {


      n.r(t);
      var r = n("8586"),
        o = n.n(r);
      for (var a in r)["default"].indexOf(a) < 0 && function(e) {
        n.d(t, e, function() {
          return r[e];
        });
      }(a);
      t["default"] = o.a;
    },
    fcd9: function fcd9(e, t, n) {


      (function(e, t) {
        var r = n("47a9");
        n("5c38");
        r(n("3240"));
        var o = r(n("44e0"));
        e.__webpack_require_UNI_MP_PLUGIN__ = n, t(o.default);
      }).call(this, n("3223")["default"], n("df3c")["createPage"]);
    }
  },
  [
    ["fcd9", "common/runtime", "common/vendor"]
  ]
]);