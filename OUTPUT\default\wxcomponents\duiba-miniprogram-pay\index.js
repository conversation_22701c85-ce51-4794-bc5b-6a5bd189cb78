var _typeof2 = require("../../@babel/runtime/helpers/typeof");
module.exports = /******/ function(modules) {
  // webpackBootstrap
  /******/ // The module cache
  /******/
  var installedModules = {};
  /******/
  /******/ // The require function
  /******/
  function __webpack_require__(moduleId) {
    /******/
    /******/ // Check if module is in cache
    /******/
    if (installedModules[moduleId]) {
      /******/
      return installedModules[moduleId].exports;
      /******/
    }
    /******/ // Create a new module (and put it into the cache)
    /******/
    var module = installedModules[moduleId] = {
      /******/
      i: moduleId,
      /******/
      l: false,
      /******/
      exports: {}
      /******/
    };
    /******/
    /******/ // Execute the module function
    /******/
    modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
    /******/
    /******/ // Flag the module as loaded
    /******/
    module.l = true;
    /******/
    /******/ // Return the exports of the module
    /******/
    return module.exports;
    /******/
  }
  /******/
  /******/
  /******/ // expose the modules object (__webpack_modules__)
  /******/
  __webpack_require__.m = modules;
  /******/
  /******/ // expose the module cache
  /******/
  __webpack_require__.c = installedModules;
  /******/
  /******/ // define getter function for harmony exports
  /******/
  __webpack_require__.d = function(exports, name, getter) {
    /******/
    if (!__webpack_require__.o(exports, name)) {
      /******/
      Object.defineProperty(exports, name, {
        enumerable: true,
        get: getter
      });
      /******/
    }
    /******/
  };
  /******/
  /******/ // define __esModule on exports
  /******/
  __webpack_require__.r = function(exports) {
    /******/
    if (typeof Symbol !== 'undefined' && Symbol.toStringTag) {
      /******/
      Object.defineProperty(exports, Symbol.toStringTag, {
        value: 'Module'
      });
      /******/
    }
    /******/
    Object.defineProperty(exports, '__esModule', {
      value: true
    });
    /******/
  };
  /******/
  /******/ // create a fake namespace object
  /******/ // mode & 1: value is a module id, require it
  /******/ // mode & 2: merge all properties of value into the ns
  /******/ // mode & 4: return value when already ns object
  /******/ // mode & 8|1: behave like require
  /******/
  __webpack_require__.t = function(value, mode) {
    /******/
    if (mode & 1) value = __webpack_require__(value);
    /******/
    if (mode & 8) return value;
    /******/
    if (mode & 4 && _typeof2(value) === 'object' && value && value.__esModule) return value;
    /******/
    var ns = Object.create(null);
    /******/
    __webpack_require__.r(ns);
    /******/
    Object.defineProperty(ns, 'default', {
      enumerable: true,
      value: value
    });
    /******/
    if (mode & 2 && typeof value != 'string')
      for (var key in value) __webpack_require__.d(ns, key, function(key) {
        return value[key];
      }.bind(null, key));
    /******/
    return ns;
    /******/
  };
  /******/
  /******/ // getDefaultExport function for compatibility with non-harmony modules
  /******/
  __webpack_require__.n = function(module) {
    /******/
    var getter = module && module.__esModule ? /******/ function getDefault() {
      return module['default'];
    } : /******/ function getModuleExports() {
      return module;
    };
    /******/
    __webpack_require__.d(getter, 'a', getter);
    /******/
    return getter;
    /******/
  };
  /******/
  /******/ // Object.prototype.hasOwnProperty.call
  /******/
  __webpack_require__.o = function(object, property) {
    return Object.prototype.hasOwnProperty.call(object, property);
  };
  /******/
  /******/ // __webpack_public_path__
  /******/
  __webpack_require__.p = "";
  /******/
  /******/
  /******/ // Load entry module and return exports
  /******/
  return __webpack_require__(__webpack_require__.s = 0);
  /******/
}
/************************************************************************/
/******/
([ /* 0 */
  /***/
  function(module, exports, __webpack_require__) {


    Component({
      properties: {
        prop: {
          type: String,
          value: 'index.properties'
        }
      },
      data: {
        flag: false
      },
      lifetimes: {
        ready: function ready() {
          var _this = this;
          wx.showLoading({
            title: '唤起支付中'
          });
          this.login().then(function(res) {
            if (res.code) {
              _this.requestDuibaPay(res.code);
            }
            return res;
          }).catch(function() {
            wx.hideLoading();
          });
        }
      },
      methods: {
        getCurrentPageUrlQuery: function getCurrentPageUrlQuery() {
          var pages = getCurrentPages(); // 获取加载的页面
          var currentPage = pages[pages.length - 1]; // 获取当前页面的对象
          var options = currentPage.options; // 如果要获取url中所带的参数可以查看options
          return options;
        },
        login: function login() {
          return new Promise(function(resolve, reject) {
            wx.login({
              success: function success(res) {
                resolve(res);
              },
              fail: function fail(res) {
                reject(res);
              },
              timeout: function timeout(res) {
                reject(res);
              }
            });
          });
        },
        requestDuibaPay: function requestDuibaPay(code) {
          var _getCurrentPageUrlQue = this.getCurrentPageUrlQuery(),
            orderId = _getCurrentPageUrlQue.orderId,
            env = _getCurrentPageUrlQue.env,
            cookie = _getCurrentPageUrlQue.cookie;
          var decodeEnv = decodeURIComponent(env);
          wx.request({
            url: (decodeURIComponent(env) || 'http://activity.m.duiba.com.cn') + '/ambPay/wxPayLite/charge',
            // jk
            data: {
              orderId: orderId,
              authCode: code
            },
            header: {
              cookie: decodeURIComponent(cookie)
            },
            success: function success(_ref) {
              var data = _ref.data;
              if (!data.success) return;
              var _data$data = data.data,
                timeStamp = _data$data.timeStamp,
                nonceStr = _data$data.nonceStr,
                signType = _data$data.signType,
                paySign = _data$data.paySign;
              wx.requestPayment({
                timeStamp: timeStamp,
                nonceStr: nonceStr,
                package: data.data.package,
                signType: signType,
                paySign: paySign,
                success: function success() {
                  // console.log(res)
                  wx.redirectTo({
                    url: '/pages/duibaRedirect/duibaRedirect?redirect=' + encodeURIComponent(decodeEnv + '/crecord/orderPayResult?orderId=' + orderId)
                  });
                },
                fail: function fail(res) {
                  var errMsg = res.errMsg;
                  if (errMsg === 'requestPayment:fail cancel') {
                    wx.redirectTo({
                      url: '/pages/duibaRedirect/duibaRedirect?redirect=' + encodeURIComponent(decodeEnv + '/ambPay/cashier?orderId=' + orderId)
                    });
                  }
                }
              });
            },
            complete: function complete() {
              wx.hideLoading();
            }
          });
        }
      }
    });

    /***/
  }
  /******/
]);