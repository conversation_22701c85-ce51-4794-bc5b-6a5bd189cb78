Component({
  options: {
    multipleSlots: !0
  },
  properties: {
    miLinToken: {
      type: String
    },
    src: {
      type: String,
      value: "",
      observer: function(e, t, n) {
        this.checkVideoCanPlay(e)
      }
    },
    id: {
      type: String
    },
    duration: {
      type: Number
    },
    controls: {
      type: <PERSON><PERSON><PERSON>,
      value: !0
    },
    danmuList: {
      type: Array
    },
    danmuBtn: {
      type: <PERSON>olean
    },
    enableDanmu: {
      type: Boolean
    },
    autoplay: {
      type: Boolean
    },
    loop: {
      type: Boolean
    },
    muted: {
      type: Boolean
    },
    initialTime: {
      type: Number,
      value: 0
    },
    pageGesture: {
      type: Boolean
    },
    direction: {
      type: Number,
      value: -1
    },
    showProgress: {
      type: Boolean,
      value: !0
    },
    showFullscreenBtn: {
      type: Boolean,
      value: !0
    },
    showPlayBtn: {
      type: Boolean,
      value: !0
    },
    showCenterPlayBtn: {
      type: Boolean,
      value: !0
    },
    enableProgressGesture: {
      type: <PERSON>olean
    },
    objectFit: {
      type: String,
      value: "contain"
    },
    poster: {
      type: String
    },
    showMuteBtn: {
      type: <PERSON>olean
    },
    title: {
      type: String
    },
    playBtnPosition: {
      type: String,
      value: "bottom"
    },
    enablePlayGesture: {
      type: Boolean
    },
    autoPauseIfNavigate: {
      type: Boolean,
      value: !0
    },
    autoPauseIfOpenNative: {
      type: Boolean,
      value: !0
    },
    vslideGesture: {
      type: Boolean
    },
    vslideGestureInFullscreen: {
      type: Boolean,
      value: !0
    },
    adUnitId: {
      type: String
    },
    posterForCrawler: {
      type: String
    },
    showCastingButton: {
      type: Boolean
    },
    pictureInPictureMode: {
      type: [String, Array]
    },
    pictureInPictureShowProgress: {
      type: Boolean
    },
    enableAutoRotation: {
      type: Boolean
    },
    showScreenLockButton: {
      type: Boolean
    },
    showSnapshotButton: {
      type: Boolean
    },
    slotName: {
      type: String
    }
  },
  data: {
    miLinSrc: "",
    appId: null,
    envVersion: null,
    videoContext: null
  },
  lifetimes: {
    attached: function() {
      console.log("attached");
      var e = wx.createVideoContext(this.properties.id, this);
      this.setData({
        videoContext: e
      })
    }
  },
  behaviors: ["wx://component-export"],
  export: function() {
    return this.data.videoContext
  },
  methods: {
    bindPlay: function(e) {
      this.triggerEvent("play", e)
    },
    bindPause: function(e) {
      this.triggerEvent("pause", e)
    },
    bindEnded: function(e) {
      this.triggerEvent("ended", e)
    },
    bindTimeUpdate: function(e) {
      this.triggerEvent("timeupdate", e)
    },
    bindFullScreenChange: function(e) {
      this.triggerEvent("fullscreenchange", e)
    },
    bindWaiting: function(e) {
      this.triggerEvent("waiting", e)
    },
    bindError: function(e) {
      this.triggerEvent("error", e)
    },
    bindProgress: function(e) {
      this.triggerEvent("progress", e)
    },
    bindLoadedMetaData: function(e) {
      this.triggerEvent("loadedmetadata", e)
    },
    bindControlStoggle: function(e) {
      this.triggerEvent("controlstoggle", e)
    },
    bindEnterPictureInPicture: function(e) {
      this.triggerEvent("enterpictureinpicture", e)
    },
    bindLeavePictureInPicture: function(e) {
      this.triggerEvent("leavepictureinpicture", e)
    },
    bindSeekComplete: function(e) {
      this.triggerEvent("seekcomplete", e)
    },
    bindVertifySuccess: function(e) {
      this.triggerEvent("videoVertifySuccess", e)
    },
    bindVertifyFailed: function(e) {
      this.triggerEvent("videoVertifyFailed", e)
    },
    setVideoUrlData: function(e) {
      this.setData({
        miLinSrc: e
      })
    },
    checkVideoCanPlay: function(e) {
      if (e) {
        var t = this,
          n = t.data,
          a = n.appId,
          i = n.envVersion;
        if (!a || !i) {
          try {
            i = __wxConfig.envVersion ? __wxConfig.envVersion : null
          } catch (e) {}
          var o = wx.getAccountInfoSync();
          o && o.miniProgram && (a = o.miniProgram.appId, t.setData({
            appId: a,
            envVersion: i
          }))
        }
        var r = t.properties.miLinToken;
        wx.request({
          url: "https://api.milinzone.com:8095/plugin/video/canPlayV3",
          method: "POST",
          header: {
            "content-type": "application/json"
          },
          data: {
            url: e,
            appId: a,
            envVersion: i,
            token: r
          },
          success: function(n) {
            n.data && 0 === n.data.code && n.data.data ? !0 === n.data.data.canPlay && n.data.data.realUrl ? t.onCheckPlaySuccess(e, n.data.data.realUrl) : t.onCheckPlayFailed(n, e, n.data.data.realUrl) : t.onCheckPlayFailed(n, e, null)
          },
          fail: function(n) {
            t.onCheckPlayFailed(n, e, null)
          }
        })
      }
    },
    onCheckPlaySuccess: function(e, t) {
      this.setVideoUrlData(t);
      var n = {
        code: 200,
        message: "success",
        url: e,
        playUrl: t
      };
      this.bindVertifySuccess(n)
    },
    onCheckPlayFailed: function(e, t, n) {
      this.setVideoUrlData("");
      var a = 0,
        i = "未知异常",
        o = "";
      e && (200 === e.statusCode ? e.data && (a = e.data.code, i = e.data.error ? e.data.error : "视频审核未通过！", o = e.data.message) : (a = e.statusCode, e.data && (o = e.data.message, i = e.data.error)));
      var r = {
        code: a,
        error: i,
        message: o,
        url: t,
        playUrl: n
      };
      this.bindVertifyFailed(r)
    }
  }
});