.main.data-v-222a3e86 {
    background: url(https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/packets-normal.png) no-repeat;
    background-size: 100%;
    box-sizing: border-box;
    height: 740rpx;
    padding: 20rpx;
    position: relative;
    width: 560rpx
}

.hd.data-v-222a3e86 {
    height: 452rpx
}

.name.data-v-222a3e86 {
    color: #933718;
    font-size: 56rpx;
    font-weight: var(--font-weight-bold);
    line-height: 80rpx;
    margin-bottom: 36rpx;
    text-align: center
}

.subname.data-v-222a3e86 {
    color: #933718;
    font-size: var(--font-size-34);
    line-height: 56rpx;
    text-align: center
}

.subname__bold.data-v-222a3e86 {
    font-size: 44rpx;
    font-weight: var(--font-weight-bold)
}

.bd.data-v-222a3e86 {
    height: 105rpx
}

.btn.data-v-222a3e86 {
    color: #d01210;
    font-size: var(--font-size-40);
    font-weight: var(--font-weight-bold);
    height: 105rpx;
    text-shadow: 0 1px 2px rgba(0,0,0,.12)
}

.disbtn.data-v-222a3e86 {
    background: var(--color-text-weak);
    border-radius: 49rpx;
    color: var(--color-fill-grey-inverse);
    font-size: var(--font-size-34);
    margin: 0 auto;
    padding: 24rpx;
    width: 420rpx
}

.agreement.data-v-222a3e86 {
    color: var(--color-fill-grey-inverse);
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    font-size: var(--font-size-24);
    line-height: 28rpx
}

.agreement .link.data-v-222a3e86 {
    color: #ffe6a5;
    font-weight: var(--font-weight-bold);
    padding: 20rpx 0
}
