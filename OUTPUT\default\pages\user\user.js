(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/user/user"], {
    "0094": function _(e, t, n) {


      n.d(t, "b", function() {
        return a;
      }), n.d(t, "c", function() {
        return i;
      }), n.d(t, "a", function() {
        return r;
      });
      var r = {
          compPage: function compPage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-page/comp-page")]).then(n.bind(null, "fccb"));
          }
        },
        a = function a() {
          var e = this.$createElement,
            t = (this._self._c, this.adData.list && this.adData.list.length);
          this.$mp.data = Object.assign({}, {
            $root: {
              g0: t
            }
          });
        },
        i = [];
    },
    "68df": function df(e, t, n) {


      (function(e, t) {
        var r = n("47a9");
        n("5c38");
        r(n("3240"));
        var a = r(n("836b"));
        e.__webpack_require_UNI_MP_PLUGIN__ = n, t(a.default);
      }).call(this, n("3223")["default"], n("df3c")["createPage"]);
    },
    "6c20": function c20(e, t, n) {


      var r = n("47a9");
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.default = void 0;
      var a = r(n("7eb4")),
        i = r(n("ee10")),
        u = r(n("7ca3")),
        c = n("8f59"),
        o = r(n("8b9c")),
        s = r(n("c148")),
        f = r(n("a13b")),
        l = r(n("a23b")),
        d = r(n("bdc5"));

      function p(e, t) {
        var n = Object.keys(e);
        if (Object.getOwnPropertySymbols) {
          var r = Object.getOwnPropertySymbols(e);
          t && (r = r.filter(function(t) {
            return Object.getOwnPropertyDescriptor(e, t).enumerable;
          })), n.push.apply(n, r);
        }
        return n;
      }
      var m = {
        components: {
          CompMyRights: function CompMyRights() {
            n.e("pages/user/comp-my-rights").then(function() {
              return resolve(n("9f12"));
            }.bind(null, n)).catch(n.oe);
          },
          CompRecommand: function CompRecommand() {
            n.e("pages/user/comp-recommand").then(function() {
              return resolve(n("def8"));
            }.bind(null, n)).catch(n.oe);
          },
          PageAd: function PageAd() {
            n.e("pages/user/page-ad").then(function() {
              return resolve(n("1870"));
            }.bind(null, n)).catch(n.oe);
          },
          PageInfo: function PageInfo() {
            n.e("pages/user/page-info").then(function() {
              return resolve(n("766f"));
            }.bind(null, n)).catch(n.oe);
          },
          PageNav: function PageNav() {
            n.e("pages/user/page-nav").then(function() {
              return resolve(n("5afa"));
            }.bind(null, n)).catch(n.oe);
          },
          PageNavList: function PageNavList() {
            n.e("pages/user/page-nav-list").then(function() {
              return resolve(n("859e"));
            }.bind(null, n)).catch(n.oe);
          },
          PageProduct: function PageProduct() {
            n.e("pages/user/page-product").then(function() {
              return resolve(n("6488"));
            }.bind(null, n)).catch(n.oe);
          },
          PageOrder: function PageOrder() {
            n.e("pages/user/page-order").then(function() {
              return resolve(n("fa38"));
            }.bind(null, n)).catch(n.oe);
          }
        },
        data: function data() {
          return {
            isFirst: !1,
            gameData: {
              list: []
            },
            pictureData: {
              list: []
            },
            educationData: {
              list: []
            },
            adData: {
              list: []
            },
            recommandList: [],
            income: "",
            couponNum: "",
            pageScrollTop: 0,
            orderCounts: {
              pendingPayNum: 0,
              pendingSendNum: 0,
              pendingReceiveNum: 0,
              pendingCommentNum: 0,
              refundNum: 0
            },
            rightNum: 0,
            rights: [{
              active: !1,
              icon: "rights_icon_2",
              name: "产品反馈直通车",
              levels: ["青铜", "白银", "黄金", "铂金", "钻石", "黑金"]
            }, {
              active: !1,
              icon: "rights_icon_3",
              name: "生日特权",
              levels: ["青铜", "白银", "黄金", "铂金", "钻石", "黑金"]
            }, {
              active: !1,
              icon: "rights_icon_6",
              name: "发票送维豆",
              levels: ["白银", "黄金", "铂金", "钻石", "黑金"]
            }, {
              active: !1,
              icon: "rights_icon_7",
              name: "每月好礼",
              levels: ["白银", "黄金", "铂金", "钻石", "黑金"]
            }, {
              active: !1,
              icon: "rights_icon_4",
              name: "电视周边配件",
              levels: ["黄金", "铂金", "钻石", "黑金"]
            }, {
              active: !1,
              icon: "rights_icon_5",
              name: "0元领延保",
              levels: ["黄金", "铂金", "钻石", "黑金"]
            }, {
              active: !1,
              icon: "rights_icon_10",
              name: "5折购机券",
              levels: ["铂金", "钻石", "黑金"]
            }, {
              active: !1,
              icon: "rights_icon_9",
              name: "keep健身",
              levels: ["钻石", "黑金"]
            }, {
              active: !1,
              icon: "rights_icon_11",
              name: "网易云音乐",
              levels: ["钻石", "黑金"]
            }, {
              active: !1,
              icon: "rights_icon_1",
              name: "上门服务",
              levels: ["黑金"]
            }, {
              active: !1,
              icon: "rights_icon_8",
              name: "腾讯视频季卡",
              levels: ["黑金"]
            }, {
              active: !1,
              icon: "rights_icon_12",
              name: "爱奇艺年卡",
              levels: ["黑金"]
            }]
          };
        },
        computed: function(e) {
          for (var t = 1; t < arguments.length; t++) {
            var n = null != arguments[t] ? arguments[t] : {};
            t % 2 ? p(Object(n), !0).forEach(function(t) {
              (0, u.default)(e, t, n[t]);
            }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : p(Object(n)).forEach(function(t) {
              Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
            });
          }
          return e;
        }({}, (0, c.mapState)(["profile", "token"])),
        onShow: function onShow() {
          !this.isFirst && this.token && (d.default.getProfile(), this.getMyincome(), this.getMyCoupons(), this.getRecommandList(), this.getOrderCount());
        },
        onLoad: function onLoad() {
          var e = this,
            t = 0;
          this.profile && (this.rights.map(function(n) {
            n.levels.includes(e.profile.gradeName) && t++;
          }), this.rightNum = t);
        },
        onShareAppMessage: function onShareAppMessage() {
          return l.default.generateShareInfo();
        },
        methods: {
          previousFetch: function previousFetch() {
            var e = this;
            return (0, i.default)(a.default.mark(function t() {
              return a.default.wrap(function(t) {
                while (1) switch (t.prev = t.next) {
                  case 0:
                    if (!e.profile) {
                      t.next = 5;
                      break;
                    }
                    return t.next = 3, d.default.getProfile();
                  case 3:
                    return t.next = 5, e.getMyincome();
                  case 5:
                  case "end":
                    return t.stop();
                }
              }, t);
            }))();
          },
          fetch: function fetch() {
            if (this.token) return Promise.all([this.getAd(), this.getRecommandList(), this.getIndexNav(), this.getOrderCount()]);
          },
          afterFetch: function afterFetch() {
            this.isFirst = !1;
          },
          getIndexNav: function getIndexNav() {
            var e = this;
            return (0, i.default)(a.default.mark(function t() {
              var n, r;
              return a.default.wrap(function(t) {
                while (1) switch (t.prev = t.next) {
                  case 0:
                    return t.next = 2, o.default.indexNav();
                  case 2:
                    n = t.sent, r = l.default.get(n, "data.data.storeTokenUrl", ""), e.storeTokenUrl = r;
                  case 5:
                  case "end":
                    return t.stop();
                }
              }, t);
            }))();
          },
          jumpMore: function jumpMore() {
            navigate.toWebviewByTokenUrl({
              tokenUrl: this.storeTokenUrl
            });
          },
          getAd: function getAd() {
            var e = this;
            return (0, i.default)(a.default.mark(function t() {
              var n;
              return a.default.wrap(function(t) {
                while (1) switch (t.prev = t.next) {
                  case 0:
                    return t.next = 2, o.default.banner({
                      position: "bnminemid"
                    });
                  case 2:
                    n = t.sent, e.adData.list = l.default.get(n, "data.data", []);
                  case 4:
                  case "end":
                    return t.stop();
                }
              }, t);
            }))();
          },
          getMyincome: function getMyincome() {
            var e = this;
            return (0, i.default)(a.default.mark(function t() {
              var n;
              return a.default.wrap(function(t) {
                while (1) switch (t.prev = t.next) {
                  case 0:
                    return t.next = 2, s.default.myincome();
                  case 2:
                    n = t.sent, e.income = n && n.amount;
                  case 4:
                  case "end":
                    return t.stop();
                }
              }, t);
            }))();
          },
          getMyCoupons: function getMyCoupons() {
            var e = this;
            return (0, i.default)(a.default.mark(function t() {
              var n, r;
              return a.default.wrap(function(t) {
                while (1) switch (t.prev = t.next) {
                  case 0:
                    return t.next = 2, s.default.getUserCoupon({});
                  case 2:
                    n = t.sent, r = l.default.get(n, "data.data"), e.couponNum = r;
                  case 5:
                  case "end":
                    return t.stop();
                }
              }, t);
            }))();
          },
          getGame: function getGame() {
            var e = this;
            return (0, i.default)(a.default.mark(function t() {
              var n, r;
              return a.default.wrap(function(t) {
                while (1) switch (t.prev = t.next) {
                  case 0:
                    return t.next = 2, o.default.gameList();
                  case 2:
                    n = t.sent, r = l.default.get(n, "data.data", []), e.gameData.list = r;
                  case 5:
                  case "end":
                    return t.stop();
                }
              }, t);
            }))();
          },
          getPictureList: function getPictureList() {
            var e = this;
            return (0, i.default)(a.default.mark(function t() {
              var n, r;
              return a.default.wrap(function(t) {
                while (1) switch (t.prev = t.next) {
                  case 0:
                    if (e.profile) {
                      t.next = 2;
                      break;
                    }
                    return t.abrupt("return");
                  case 2:
                    return t.next = 4, s.default.picMyList();
                  case 4:
                    n = t.sent, r = l.default.get(n, "data.data.records", []), e.pictureData.list = r;
                  case 7:
                  case "end":
                    return t.stop();
                }
              }, t);
            }))();
          },
          getRecommandList: function getRecommandList() {
            var e = this;
            return (0, i.default)(a.default.mark(function t() {
              var n, r;
              return a.default.wrap(function(t) {
                while (1) switch (t.prev = t.next) {
                  case 0:
                    if (e.profile) {
                      t.next = 2;
                      break;
                    }
                    return t.abrupt("return");
                  case 2:
                    return t.next = 4, s.default.queryRecommandList({
                      pageNum: 1,
                      pageSize: 20,
                      bannerPosLabel: "bnrecommendedforyou"
                    });
                  case 4:
                    n = t.sent, r = l.default.get(n, "data.data.records", []), e.recommandList = r;
                  case 7:
                  case "end":
                    return t.stop();
                }
              }, t);
            }))();
          },
          getEducationList: function getEducationList() {
            var e = this;
            return (0, i.default)(a.default.mark(function t() {
              var n, r;
              return a.default.wrap(function(t) {
                while (1) switch (t.prev = t.next) {
                  case 0:
                    if (e.profile) {
                      t.next = 2;
                      break;
                    }
                    return t.abrupt("return");
                  case 2:
                    return t.next = 4, o.default.educationOrderList();
                  case 4:
                    n = t.sent, r = l.default.get(n, "data.data.list", []), e.educationData.list = r;
                  case 7:
                  case "end":
                    return t.stop();
                }
              }, t);
            }))();
          },
          getOrderCount: function getOrderCount() {
            var e = this;
            return (0, i.default)(a.default.mark(function t() {
              var n;
              return a.default.wrap(function(t) {
                while (1) switch (t.prev = t.next) {
                  case 0:
                    return t.prev = 0, t.next = 3, f.default.getCWDSCustomerCenterAPI();
                  case 3:
                    n = t.sent, n.customerIndexInfo && (e.orderCounts = {
                      pendingPayNum: n.customerIndexInfo.waitPayOrderCount,
                      pendingSendNum: n.customerIndexInfo.waitShipOrderCount,
                      pendingReceiveNum: n.customerIndexInfo.waitReceiveOrderCount,
                      pendingCommentNum: n.customerIndexInfo.waitAppraiseOrderCount,
                      refundNum: n.customerIndexInfo.afterserviceOrderCount
                    }), t.next = 10;
                    break;
                  case 7:
                    t.prev = 7, t.t0 = t["catch"](0), console.log(t.t0);
                  case 10:
                  case "end":
                    return t.stop();
                }
              }, t, null, [
                [0, 7]
              ]);
            }))();
          }
        }
      };
      t.default = m;
    },
    "836b": function b(e, t, n) {


      n.r(t);
      var r = n("0094"),
        a = n("a58b");
      for (var i in a)["default"].indexOf(i) < 0 && function(e) {
        n.d(t, e, function() {
          return a[e];
        });
      }(i);
      var u = n("828b"),
        c = Object(u["a"])(a["default"], r["b"], r["c"], !1, null, "4c9cb033", null, !1, r["a"], void 0);
      t["default"] = c.exports;
    },
    a58b: function a58b(e, t, n) {


      n.r(t);
      var r = n("6c20"),
        a = n.n(r);
      for (var i in r)["default"].indexOf(i) < 0 && function(e) {
        n.d(t, e, function() {
          return r[e];
        });
      }(i);
      t["default"] = a.a;
    }
  },
  [
    ["68df", "common/runtime", "common/vendor"]
  ]
]);