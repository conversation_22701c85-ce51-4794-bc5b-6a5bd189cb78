.article-row.data-v-b223e146 {
    display: inline-block;
    margin: 10rpx auto 0 24rpx;
    overflow-x: scroll
}

.article-row .act-card.data-v-b223e146 {
    background: #f6f7f9;
    border-radius: 24rpx;
    display: -webkit-flex;
    display: flex;
    height: 196rpx;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    margin: 0 0 24rpx;
    width: 702rpx
}

.article-row .act-card .left-box.data-v-b223e146 {
    margin-left: 36rpx;
    width: 420rpx
}

.article-row .act-card .left-box .title.data-v-b223e146 {
    color: rgba(0,0,0,.85);
    font-size: 32rpx;
    height: 45rpx;
    line-height: 45rpx;
    margin-top: 24rpx
}

.article-row .act-card .left-box .sub-title.data-v-b223e146,.article-row .act-card .left-box .title.data-v-b223e146 {
    font-family: PingFang-SC-Medium,PingFang-SC;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.article-row .act-card .left-box .sub-title.data-v-b223e146 {
    color: rgba(0,0,0,.35);
    font-size: 22rpx;
    height: 30rpx;
    line-height: 30rpx;
    margin: 4rpx 0 16rpx
}

.article-row .act-card .left-box .btn.data-v-b223e146 {
    background: #306dff;
    border-radius: 27rpx;
    color: #fff;
    font-family: PingFang-SC-Medium,PingFang-SC;
    font-size: 24rpx;
    font-weight: 500;
    height: 53rpx;
    line-height: 53rpx;
    text-align: center;
    width: 144rpx
}

.article-row .act-card .right-box.data-v-b223e146 {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    height: 196rpx;
    -webkit-justify-content: center;
    justify-content: center;
    width: 222rpx
}

.article-row .act-card .right-box .pic-wrap.data-v-b223e146 {
    border-radius: 12rpx;
    height: 148rpx;
    margin-right: 24rpx;
    overflow: hidden;
    position: relative;
    width: 198rpx
}

.article-row .act-card .right-box .pic-wrap .status-btn.data-v-b223e146 {
    -webkit-align-items: center;
    align-items: center;
    background: rgba(0,0,0,.5);
    border-radius: 0 16rpx 0 16rpx;
    color: #fff;
    display: -webkit-flex;
    display: flex;
    font-size: 20rpx;
    height: 32rpx;
    -webkit-justify-content: center;
    justify-content: center;
    line-height: 32rpx;
    position: absolute;
    right: 0;
    text-align: center;
    top: 0;
    width: 94rpx;
    z-index: 9
}
