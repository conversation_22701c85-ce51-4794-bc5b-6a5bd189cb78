<comp-page bind:__l="__l" class="data-v-c509e3c4 vue-ref" data-ref="page" navbarIsBack="{{false}}" navbarIsTransparent="{{true}}" navbarTitle="活动社区" vueId="145f0dc0-1" vueSlots="{{['default']}}">
    <view class="bg data-v-c509e3c4"></view>
    <page-header banner="{{headerData.banner}}" bind:__l="__l" class="data-v-c509e3c4" vueId="{{'145f0dc0-2'+','+'145f0dc0-1'}}"></page-header>
    <view class="data-v-c509e3c4">
        <view class="tablist data-v-c509e3c4">
            <view bindtap="__e" class="imgItem _div data-v-c509e3c4" data-event-opts="{{[ [ 'tap',[ [ 'toActiviey',[1] ] ] ] ]}}">
                <span class="imgItemTitle data-v-c509e3c4">探家大揭秘</span>
                <image class="data-v-c509e3c4" src="https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/community-1.png"></image>
            </view>
            <view bindtap="__e" class="imgItem _div data-v-c509e3c4" data-event-opts="{{[ [ 'tap',[ [ 'toActiviey',[2] ] ] ] ]}}">
                <span class="imgItemTitle data-v-c509e3c4">维粉故事汇</span>
                <image class="data-v-c509e3c4" src="https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/community-2.png"></image>
            </view>
            <view bindtap="__e" class="imgItem _div data-v-c509e3c4" data-event-opts="{{[ [ 'tap',[ [ 'toActiviey',[3] ] ] ] ]}}">
                <span class="imgItemTitle data-v-c509e3c4">创维新闻</span>
                <image class="data-v-c509e3c4" src="https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/community-3.png"></image>
            </view>
        </view>
        <view class="activity-center data-v-c509e3c4" wx:if="{{$root.g0}}">
            <view class="activity-center-top data-v-c509e3c4">
                <view class="title data-v-c509e3c4">活动中心</view>
                <view class="info data-v-c509e3c4" wx:if="{{$root.g1}}">
                    <swiper autoplay="{{true}}" circular="{{true}}" class="data-v-c509e3c4" interval="{{3000}}" style="width:100%;height:46rpx;" vertical="{{true}}">
                        <swiper-item class="data-v-c509e3c4" style="display:flex;justify-content:center;align-items:center;" wx:for="{{textList}}" wx:for-item="el" wx:key="index">
                            <text class="data-v-c509e3c4">{{el.value}}</text>
                            <text class="data-v-c509e3c4" style="color:#FF0808;margin-left:10rpx;">{{el.name}}</text>
                        </swiper-item>
                    </swiper>
                </view>
                <view class="right data-v-c509e3c4">
                    <comp-button bind:__l="__l" bind:getUserInfo="__e" bind:onClick="__e" class="data-v-c509e3c4" data-event-opts="{{[ [ '^getUserInfo',[ ['clickMore'] ] ],[ '^onClick',[ ['clickMore'] ] ] ]}}" vueId="{{'145f0dc0-3'+','+'145f0dc0-1'}}" vueSlots="{{['default']}}">
                        <view class="right-txt data-v-c509e3c4">
                            <text class="data-v-c509e3c4">更多</text>
                            <comp-icon bind:__l="__l" class="data-v-c509e3c4" color="rgba(0, 0, 0, 0.30)" icon="iconarrow_right_o" size="28rpx" vueId="{{'145f0dc0-4'+','+'145f0dc0-3'}}"></comp-icon>
                        </view>
                    </comp-button>
                </view>
            </view>
            <view class="activity-center-bottom data-v-c509e3c4">
                <view class="activity-center-item data-v-c509e3c4" wx:for="{{$root.l0}}" wx:for-index="__i0__" wx:key="id">
                    <view class="item-left data-v-c509e3c4">
                        <view class="img data-v-c509e3c4" style="{{'background-image:'+'url('+item[$orig].image+')'+';'+'background-size:'+'100%'+';'+'background-position:'+'center center'+';'}}"></view>
                        <view class="item-left-info data-v-c509e3c4">
                            <view class="name data-v-c509e3c4">{{item[$orig].name}}</view>
                            <view class="status data-v-c509e3c4">活动进行中</view>
                            <view class="time data-v-c509e3c4">{{item.g2+'至'+item.g3}}</view>
                        </view>
                    </view>
                    <view class="item-right data-v-c509e3c4">
                        <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-c509e3c4" data-event-opts="{{[ [ '^getUserInfo',[ [ 'clickAct',['$0'],[ [ ['visibleItems','id',item[$orig].id] ] ] ] ] ] ]}}" openType="getUserInfo" vueId="{{'145f0dc0-5-'+__i0__+','+'145f0dc0-1'}}" vueSlots="{{['default']}}">
                            <view class="btn data-v-c509e3c4">立即参加</view>
                        </comp-button>
                    </view>
                </view>
            </view>
        </view>
        <view class="imgList data-v-c509e3c4">
            <view class="imgListTitle data-v-c509e3c4">种草社区</view>
            <scroll-view class="data-v-c509e3c4" scrollX="true" showScrollbar="false">
                <view class="subjectList data-v-c509e3c4">
                    <view bindtap="__e" class="subjectItem data-v-c509e3c4" data-event-opts="{{[ [ 'tap',[ [ 'slectSubject',['$0',index],[ [ ['subjectList','id',item.id] ] ] ] ] ] ]}}" wx:for="{{subjectList}}" wx:key="id">
                        <image class="subject-i data-v-c509e3c4" src="https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/community-7.png" wx:if="{{subjectActive==index}}"></image>
                        <span class="{{['subject-t','','data-v-c509e3c4',subjectActive==index?'subject-t-active':'']}}">{{item.subjectName}}</span>
                    </view>
                </view>
                <view class="data-v-c509e3c4" style="height:20rpx;"></view>
            </scroll-view>
        </view>
        <view class="inspiration-section data-v-c509e3c4" style="min-height:400rpx;">
            <view class="inspiration-list data-v-c509e3c4" wx:if="{{$root.g4>0}}">
                <view class="inspiration-column data-v-c509e3c4">
                    <view bindtap="__e" class="inspiration-card data-v-c509e3c4" data-event-opts="{{[ [ 'tap',[ [ 'toDetail',['$0'],[ [ ['leftColumn','id',item.id] ] ] ] ] ] ]}}" wx:for="{{leftColumn}}" wx:for-index="__i1__" wx:key="id">
                        <image class="inspiration-img data-v-c509e3c4" mode="aspectFill" src="{{item.images[0]}}"></image>
                        <view class="inspiration-info data-v-c509e3c4">
                            <view class="inspiration-title-text data-v-c509e3c4" wx:if="{{item.title}}">{{item.title}}</view>
                            <view class="inspiration-user data-v-c509e3c4">
                                <image class="inspiration-avatar data-v-c509e3c4" src="{{item.userAvatar}}"></image>
                                <span class="inspiration-username data-v-c509e3c4">{{item.nickName||'微信用户'}}</span>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="inspiration-column data-v-c509e3c4">
                    <view bindtap="__e" class="inspiration-card data-v-c509e3c4" data-event-opts="{{[ [ 'tap',[ [ 'toDetail',['$0'],[ [ ['rightColumn','id',item.id] ] ] ] ] ] ]}}" wx:for="{{rightColumn}}" wx:for-index="__i2__" wx:key="id">
                        <image class="inspiration-img data-v-c509e3c4" mode="aspectFill" src="{{item.images[0]}}"></image>
                        <view class="inspiration-info data-v-c509e3c4">
                            <view class="inspiration-title-text data-v-c509e3c4" wx:if="{{item.title}}">{{item.title}}</view>
                            <view class="inspiration-user data-v-c509e3c4">
                                <image class="inspiration-avatar data-v-c509e3c4" src="{{item.userAvatar}}"></image>
                                <span class="inspiration-username data-v-c509e3c4">{{item.nickName||'微信用户'}}</span>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <comp-empty name bind:__l="__l" class="data-v-c509e3c4" img="empty@1" subname="暂无数据" vueId="{{'145f0dc0-6'+','+'145f0dc0-1'}}" wx:if="{{$root.g5<1}}"></comp-empty>
        </view>
    </view>
    <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-c509e3c4" data-event-opts="{{[ [ '^getUserInfo',[ ['clickMemberTask'] ] ] ]}}" openType="getUserInfo" vueId="{{'145f0dc0-7'+','+'145f0dc0-1'}}" vueSlots="{{['default']}}">
        <image class="toAdd data-v-c509e3c4" src="https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/linghongbao.png"></image>
    </comp-button>
</comp-page>
