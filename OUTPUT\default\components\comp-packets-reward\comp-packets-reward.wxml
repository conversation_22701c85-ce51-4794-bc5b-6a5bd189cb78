<view class="comp-packets-reward data-v-222a3e86">
    <view class="main data-v-222a3e86">
        <view class="hd flex-col row-center row-center data-v-222a3e86">
            <view class="name data-v-222a3e86">授权登录</view>
            <view class="subname data-v-222a3e86">获取<text class="subname__bold data-v-222a3e86">300</text>维豆</view>
            <view class="subname data-v-222a3e86">饿了么外卖红包天天领</view>
            <view class="subname data-v-222a3e86">搜狐视频会员月卡<text class="subname__bold data-v-222a3e86">8</text>元券</view>
        </view>
        <view class="bd data-v-222a3e86">
            <comp-button bind:__l="__l" bind:getPhoneNumber="__e" class="data-v-222a3e86" data-event-opts="{{[ [ '^getPhoneNumber',[ ['clickBtn'] ] ] ]}}" openType="getPhoneNumber" vueId="1db310e2-1" vueSlots="{{['default']}}" wx:if="{{checked}}">
                <view class="btn flex-row row-center col-center data-v-222a3e86">登录领取</view>
            </comp-button>
            <comp-button bind:__l="__l" class="data-v-222a3e86" vueId="1db310e2-2" vueSlots="{{['default']}}" wx:if="{{!checked}}">
                <view class="disbtn flex-row row-center col-center data-v-222a3e86">登录领取</view>
            </comp-button>
        </view>
        <view class="ft data-v-222a3e86">
            <view class="agreement flex-row col-center row-center data-v-222a3e86">
                <view bindtap="__e" class="flex-row col-center data-v-222a3e86" data-event-opts="{{[ [ 'tap',[ [ 'e0',['$event'] ] ] ] ]}}">
                    <comp-checkbox bind:__l="__l" checked="{{checked}}" class="data-v-222a3e86" vueId="1db310e2-3"></comp-checkbox>
                    <text class="data-v-222a3e86">我已阅读并同意</text>
                </view>
                <view bindtap="__e" class="link data-v-222a3e86" data-event-opts="{{[ [ 'tap',[ [ 'e1',['$event'] ] ] ] ]}}">创维账号协议、隐私政策</view>
            </view>
        </view>
    </view>
    <comp-agreement bind:__l="__l" bind:close="__e" class="data-v-222a3e86" data-event-opts="{{[ [ '^close',[ ['e2'] ] ] ]}}" show="{{isAgreement}}" vueId="1db310e2-4"></comp-agreement>
</view>
