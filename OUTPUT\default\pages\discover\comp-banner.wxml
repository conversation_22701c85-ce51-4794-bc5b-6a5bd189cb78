<view class="comp-banner data-v-b530e406">
    <swiper autoplay="{{$root.g0}}" bindchange="__e" class="data-v-b530e406" data-event-opts="{{[ [ 'change',[ [ 'change',['$event'] ] ] ] ]}}" interval="{{10000}}" style="{{'height:'+'250rpx'+';'}}">
        <swiper-item class="data-v-b530e406" wx:for="{{list}}" wx:key="index">
            <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-b530e406" data-event-opts="{{[ [ '^getUserInfo',[ [ 'clickBanner',['$0'],[ [ ['list','',index] ] ] ] ] ] ]}}" openType="getUserInfo" vueId="{{'2cd19429-1-'+index}}" vueSlots="{{['default']}}">
                <view class="wrap data-v-b530e406">
                    <comp-image bind:__l="__l" class="data-v-b530e406" height="250rpx" isServer="{{true}}" name="{{item.img}}" vueId="{{'2cd19429-2-'+index+','+'2cd19429-1-'+index}}" width="100%"></comp-image>
                </view>
            </comp-button>
        </swiper-item>
    </swiper>
    <view class="comp-swiper__indicator is-left flex-row row-center data-v-b530e406" wx:if="{{$root.g1}}">
        <view class="{{['indicator__item','data-v-b530e406',current===index?'is-active':'']}}" wx:for="{{list}}" wx:key="index"></view>
    </view>
</view>
