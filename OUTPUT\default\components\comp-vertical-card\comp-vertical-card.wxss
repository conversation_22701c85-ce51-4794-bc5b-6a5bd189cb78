.is-card--10.data-v-0780e7da {
    overflow: hidden;
    position: relative
}

.is-card--10 .card__main.data-v-0780e7da {
    border-radius: 16rpx;
    bottom: 0;
    left: 0;
    overflow: hidden;
    position: absolute;
    right: 0;
    top: 0
}

.is-card--10 .card__name.data-v-0780e7da {
    background: linear-gradient(1turn,transparent,rgba(0,0,0,.75));
    color: var(--color-fill-grey-inverse);
    font-size: var(--font-size-40);
    font-weight: var(--font-weight-bold);
    line-height: 56rpx;
    padding: 40rpx 32rpx
}

.is-card--10 .card__subname.data-v-0780e7da {
    background: linear-gradient(180deg,transparent,rgba(0,0,0,.75));
    color: var(--color-fill-grey-inverse);
    font-size: var(--font-size-28);
    line-height: 40rpx;
    padding: 30rpx 32rpx
}

.is-card--11.data-v-0780e7da {
    background: var(--color-fill-grey-inverse);
    border-radius: 8rpx;
    overflow: hidden
}

.is-card--11 .card__name.data-v-0780e7da {
    font-size: var(--font-size-30);
    font-weight: var(--font-weight-bold);
    line-height: 42rpx;
    padding: 18rpx 20rpx 14rpx
}
