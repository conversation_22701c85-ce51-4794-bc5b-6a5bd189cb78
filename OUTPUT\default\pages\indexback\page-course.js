(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/indexback/page-course"], {
    "242e": function e(_e, t, n) {


      n.r(t);
      var a = n("4ca8"),
        c = n.n(a);
      for (var u in a)["default"].indexOf(u) < 0 && function(e) {
        n.d(t, e, function() {
          return a[e];
        });
      }(u);
      t["default"] = c.a;
    },
    "24e8": function e8(e, t, n) {


      n.d(t, "b", function() {
        return c;
      }), n.d(t, "c", function() {
        return u;
      }), n.d(t, "a", function() {
        return a;
      });
      var a = {
          compSplit: function compSplit() {
            return n.e("components/comp-split/comp-split").then(n.bind(null, "9396"));
          },
          compVerticalCard: function compVerticalCard() {
            return n.e("components/comp-vertical-card/comp-vertical-card").then(n.bind(null, "ed84"));
          }
        },
        c = function c() {
          var e = this.$createElement,
            t = (this._self._c, this.list.length),
            n = this.list.length;
          this.$mp.data = Object.assign({}, {
            $root: {
              g0: t,
              g1: n
            }
          });
        },
        u = [];
    },
    3040: function _(e, t, n) {},
    "4ca8": function ca8(e, t, n) {


      var a = n("47a9");
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.default = void 0;
      var c = a(n("7eb4")),
        u = a(n("ee10")),
        i = a(n("8b9c")),
        r = a(n("a23b")),
        o = a(n("5e82")),
        l = {
          components: {
            PagePanel: function PagePanel() {
              n.e("pages/indexback/page-panel").then(function() {
                return resolve(n("f4ec"));
              }.bind(null, n)).catch(n.oe);
            }
          },
          data: function data() {
            return {
              list: []
            };
          },
          mounted: function mounted() {
            this.getData();
          },
          methods: {
            getData: function getData() {
              var e = this;
              return (0, u.default)(c.default.mark(function t() {
                var n, a;
                return c.default.wrap(function(t) {
                  while (1) switch (t.prev = t.next) {
                    case 0:
                      return t.next = 2, i.default.communityArticles({
                        label: "server-news-u8llumao",
                        page: 1,
                        pageSize: 6
                      });
                    case 2:
                      n = t.sent, a = r.default.get(n, "data.data.list", []), e.list = a;
                    case 5:
                    case "end":
                      return t.stop();
                  }
                }, t);
              }))();
            },
            clickCard: function clickCard(e) {
              o.default.to({
                page: "video",
                query: {
                  id: e.id
                }
              });
            },
            clickMore: function clickMore() {
              o.default.to({
                page: "course"
              });
            }
          }
        };
      t.default = l;
    },
    "9fe3": function fe3(e, t, n) {


      n.r(t);
      var a = n("24e8"),
        c = n("242e");
      for (var u in c)["default"].indexOf(u) < 0 && function(e) {
        n.d(t, e, function() {
          return c[e];
        });
      }(u);
      n("c6ab");
      var i = n("828b"),
        r = Object(i["a"])(c["default"], a["b"], a["c"], !1, null, "7d7d98fd", null, !1, a["a"], void 0);
      t["default"] = r.exports;
    },
    c6ab: function c6ab(e, t, n) {


      var a = n("3040"),
        c = n.n(a);
      c.a;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/indexback/page-course-create-component', {
    'pages/indexback/page-course-create-component': function pagesIndexbackPageCourseCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("9fe3"));
    }
  },
  [
    ['pages/indexback/page-course-create-component']
  ]
]);