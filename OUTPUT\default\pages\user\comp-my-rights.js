(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/user/comp-my-rights"], {
    "1c69": function c69(e, t, n) {


      var r = n("47a9");
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.default = void 0;
      var c = r(n("7ca3")),
        o = n("8f59"),
        i = r(n("5e82")),
        a = (r(n("a23b")), n("b3c5"));

      function l(e, t) {
        var n = Object.keys(e);
        if (Object.getOwnPropertySymbols) {
          var r = Object.getOwnPropertySymbols(e);
          t && (r = r.filter(function(t) {
            return Object.getOwnPropertyDescriptor(e, t).enumerable;
          })), n.push.apply(n, r);
        }
        return n;
      }
      (0, a.getConfig)();
      var u = {
        props: {
          navs: {
            type: Array,
            default: function _default() {
              return [];
            }
          }
        },
        data: function data() {
          return {
            showAll: !0,
            gradeName: "",
            grade: 1,
            baseUrl: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/",
            benefits: [{
              icon: "hywd",
              text: "会员维豆",
              level: 1
            }, {
              icon: "gjswd",
              text: "产品维豆",
              level: 1
            }, {
              icon: "cpfkyl",
              text: "产品反馈有礼",
              level: 1
            }, {
              icon: "lyb",
              text: "0元领延保",
              level: 1
            }, {
              icon: "smcl",
              text: "上门设计",
              level: 2
            }, {
              icon: "qwkf",
              text: "企微客服",
              level: 2
            }, {
              icon: "dydjj",
              text: "电影代金券",
              level: 3
            }, {
              icon: "srhl",
              text: "生日好礼",
              level: 3
            }, {
              icon: "ykq",
              text: "免费遥控器",
              level: 4
            }, {
              icon: "mkf",
              text: "免费麦克风",
              level: 4
            }, {
              icon: "jdqx",
              text: "家电清洗",
              level: 5
            }, {
              icon: "yshyk",
              text: "影视会员年卡",
              level: 5
            }]
          };
        },
        computed: function(e) {
          for (var t = 1; t < arguments.length; t++) {
            var n = null != arguments[t] ? arguments[t] : {};
            t % 2 ? l(Object(n), !0).forEach(function(t) {
              (0, c.default)(e, t, n[t]);
            }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : l(Object(n)).forEach(function(t) {
              Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
            });
          }
          return e;
        }({}, (0, o.mapState)(["token", "profile", "remoteConfig"])),
        watch: {
          profile: {
            deep: !0,
            handler: function handler(e) {
              e && (this.gradeName = e.gradeName, this.grade = e.grade);
            }
          }
        },
        methods: {
          updateIcons: function updateIcons() {
            var e = this,
              t = this.rights;
            t.map(function(t) {
              t.active = t.levels.includes(e.gradeName);
            }), this.rights = t;
          },
          clickNav: function clickNav(e, t) {
            i.default.to({
              page: "userRights",
              query: {
                index: t + 1
              }
            });
          }
        }
      };
      t.default = u;
    },
    "5ed2": function ed2(e, t, n) {


      n.d(t, "b", function() {
        return r;
      }), n.d(t, "c", function() {
        return c;
      }), n.d(t, "a", function() {});
      var r = function r() {
          var e = this.$createElement;
          this._self._c;
        },
        c = [];
    },
    "61cd": function cd(e, t, n) {


      var r = n("9103"),
        c = n.n(r);
      c.a;
    },
    "89e3": function e3(e, t, n) {


      n.r(t);
      var r = n("1c69"),
        c = n.n(r);
      for (var o in r)["default"].indexOf(o) < 0 && function(e) {
        n.d(t, e, function() {
          return r[e];
        });
      }(o);
      t["default"] = c.a;
    },
    9103: function _(e, t, n) {},
    "9f12": function f12(e, t, n) {


      n.r(t);
      var r = n("5ed2"),
        c = n("89e3");
      for (var o in c)["default"].indexOf(o) < 0 && function(e) {
        n.d(t, e, function() {
          return c[e];
        });
      }(o);
      n("61cd");
      var i = n("828b"),
        a = Object(i["a"])(c["default"], r["b"], r["c"], !1, null, "56065417", null, !1, r["a"], void 0);
      t["default"] = a.exports;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/user/comp-my-rights-create-component', {
    'pages/user/comp-my-rights-create-component': function pagesUserCompMyRightsCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("9f12"));
    }
  },
  [
    ['pages/user/comp-my-rights-create-component']
  ]
]);