<comp-page bind:__l="__l" class="data-v-226817f2 vue-ref" data-ref="page" isBrand="{{false}}" isShare="{{false}}" navbarBackground="#E8EEFB" navbarTitle="登录" vueId="35a7246c-1" vueSlots="{{['default','ft']}}">
    <view class="main data-v-226817f2" style="{{'padding-top:'+navbarHeight+'px'+';'}}">
        <view class="hd data-v-226817f2">
            <comp-image bind:__l="__l" class="data-v-226817f2" height="200rpx" name="login-center" vueId="{{'35a7246c-2'+','+'35a7246c-1'}}" width="220rpx"></comp-image>
            <view class="name data-v-226817f2">欢迎来到创维用户中心</view>
            <view class="subname data-v-226817f2">维豆商城优享生活，<text class="data-v-226817f2">超优惠！</text>
            </view>
            <view class="subname data-v-226817f2">发现圈子玩机评测，<text class="data-v-226817f2">超有料！</text>
            </view>
            <view class="subname data-v-226817f2">服务售后，<text class="data-v-226817f2">超保障！</text>
            </view>
        </view>
        <view class="bd flex-one flex-col row-center data-v-226817f2">
            <comp-button bind:__l="__l" class="data-v-226817f2" vueId="{{'35a7246c-3'+','+'35a7246c-1'}}" vueSlots="{{['default']}}" wx:if="{{!checked}}">
                <view class="disbtn flex-row col-center row-center data-v-226817f2">
                    <view class="btn__name data-v-226817f2">登录</view>
                </view>
            </comp-button>
            <comp-button bind:__l="__l" bind:getPhoneNumber="__e" class="data-v-226817f2" data-event-opts="{{[ [ '^getPhoneNumber',[ ['getPhoneNumber'] ] ] ]}}" openType="getPhoneNumber" vueId="{{'35a7246c-4'+','+'35a7246c-1'}}" vueSlots="{{['default']}}" wx:if="{{checked}}">
                <view class="btn flex-row col-center row-center data-v-226817f2">
                    <view class="btn__name data-v-226817f2">登录</view>
                </view>
            </comp-button>
        </view>
    </view>
    <view class="safe-bottom data-v-226817f2" slot="ft">
        <view class="tip flex-row col-center row-center data-v-226817f2">
            <view bindtap="__e" class="flex-row col-center data-v-226817f2" data-event-opts="{{[ [ 'tap',[ [ 'e0',['$event'] ] ] ] ]}}">
                <comp-checkbox bind:__l="__l" checked="{{checked}}" class="data-v-226817f2" vueId="{{'35a7246c-5'+','+'35a7246c-1'}}"></comp-checkbox>
                <text class="data-v-226817f2">我已阅读并同意</text>
            </view>
            <view bindtap="__e" class="tip__btn data-v-226817f2" data-event-opts="{{[ [ 'tap',[ [ 'e1',['$event'] ] ] ] ]}}">创维用户协议、隐私政策</view>
        </view>
    </view>
    <comp-agreement bind:__l="__l" bind:close="__e" class="data-v-226817f2" data-event-opts="{{[ [ '^close',[ ['e2'] ] ] ]}}" show="{{isAgreement}}" vueId="{{'35a7246c-6'+','+'35a7246c-1'}}"></comp-agreement>
</comp-page>
