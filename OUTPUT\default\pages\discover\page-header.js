(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/discover/page-header"], {
    "1b07": function b07(e, t, n) {


      n.d(t, "b", function() {
        return r;
      }), n.d(t, "c", function() {
        return c;
      }), n.d(t, "a", function() {});
      var r = function r() {
          var e = this.$createElement;
          this._self._c;
        },
        c = [];
    },
    "21ea": function ea(e, t, n) {


      n.r(t);
      var r = n("1b07"),
        c = n("78b3");
      for (var o in c)["default"].indexOf(o) < 0 && function(e) {
        n.d(t, e, function() {
          return c[e];
        });
      }(o);
      n("9706");
      var a = n("828b"),
        u = Object(a["a"])(c["default"], r["b"], r["c"], !1, null, "616b401f", null, !1, r["a"], void 0);
      t["default"] = u.exports;
    },
    "29eb": function eb(e, t, n) {},
    "78b3": function b3(e, t, n) {


      n.r(t);
      var r = n("89d1"),
        c = n.n(r);
      for (var o in r)["default"].indexOf(o) < 0 && function(e) {
        n.d(t, e, function() {
          return r[e];
        });
      }(o);
      t["default"] = c.a;
    },
    "89d1": function d1(e, t, n) {


      var r = n("47a9");
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.default = void 0;
      var c = r(n("7ca3")),
        o = n("8f59");

      function a(e, t) {
        var n = Object.keys(e);
        if (Object.getOwnPropertySymbols) {
          var r = Object.getOwnPropertySymbols(e);
          t && (r = r.filter(function(t) {
            return Object.getOwnPropertyDescriptor(e, t).enumerable;
          })), n.push.apply(n, r);
        }
        return n;
      }
      var u = {
        components: {
          CompBanner: function CompBanner() {
            n.e("pages/discover/comp-banner").then(function() {
              return resolve(n("2a94"));
            }.bind(null, n)).catch(n.oe);
          }
        },
        props: {
          banner: {
            type: Array,
            default: function _default() {
              return [];
            }
          }
        },
        data: function data() {
          return {};
        },
        computed: function(e) {
          for (var t = 1; t < arguments.length; t++) {
            var n = null != arguments[t] ? arguments[t] : {};
            t % 2 ? a(Object(n), !0).forEach(function(t) {
              (0, c.default)(e, t, n[t]);
            }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : a(Object(n)).forEach(function(t) {
              Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
            });
          }
          return e;
        }({}, (0, o.mapState)(["navbarHeight"]))
      };
      t.default = u;
    },
    9706: function _(e, t, n) {


      var r = n("29eb"),
        c = n.n(r);
      c.a;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/discover/page-header-create-component', {
    'pages/discover/page-header-create-component': function pagesDiscoverPageHeaderCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("21ea"));
    }
  },
  [
    ['pages/discover/page-header-create-component']
  ]
]);