<view class="comp-store-card data-v-e92ae3ca">
    <view bindtap="__e" class="hd flex-row col-center space-between data-v-e92ae3ca" data-event-opts="{{[ [ 'tap',[ [ 'clickName',['$event'] ] ] ] ]}}">
        <view class="name data-v-e92ae3ca">{{name}}</view>
        <comp-image bind:__l="__l" class="{{['triangle','data-v-e92ae3ca',isDropdownComputed?'is-dropdown':'']}}" height="36rpx" name="search-triangle" vueId="3ceb5802-1" width="36rpx" wx:if="{{type==='dropdown'}}"></comp-image>
    </view>
    <view class="{{['bd','data-v-e92ae3ca',isDropdownComputed?'is-dropdown':'']}}">
        <view bindtap="__e" class="store data-v-e92ae3ca" data-event-opts="{{[ [ 'tap',[ [ 'clickStore',['$0'],[ [ ['storeList','',index] ] ] ] ] ] ]}}" wx:for="{{storeList}}" wx:key="index">
            <comp-store-card bind:__l="__l" checked="{{item.id===checkedStoreId}}" class="data-v-e92ae3ca" name="{{item.storeName}}" subname="{{item.storeAddress}}" vueId="{{'3ceb5802-2-'+index}}"></comp-store-card>
        </view>
    </view>
</view>
