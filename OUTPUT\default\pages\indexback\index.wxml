<comp-page bind:__l="__l" class="vue-ref" data-ref="page" indexGray="{{memberCenterSwitch.indexGray}}" navbarColor="#fff" navbarIsBack="{{false}}" navbarIsTransparent="{{true}}" vueId="34bf565a-1" vueSlots="{{['default']}}">
    <page-header banner="{{headerData.banner}}" bind:__l="__l" score="{{headerData.score}}" signTokenUrl="{{headerData.signTokenUrl}}" vueId="{{'34bf565a-2'+','+'34bf565a-1'}}" vueSlots="{{['default']}}">
        <page-nav bind:__l="__l" bind:clickNav="__e" data-event-opts="{{[ [ '^clickNav',[ ['clickNav'] ] ] ]}}" navs="{{navsData.navs}}" vueId="{{'34bf565a-3'+','+'34bf565a-2'}}"></page-nav>
    </page-header>
    <comp-point-act bind:__l="__l" bind:clickItem="__e" data-event-opts="{{[ [ '^clickItem',[ ['clickPointItem'] ] ] ]}}" score="{{headerData.score}}" signTokenUrl="{{headerData.signTokenUrl}}" vueId="{{'34bf565a-4'+','+'34bf565a-1'}}"></comp-point-act>
    <page-ad bind:__l="__l" list="{{adData.list}}" vueId="{{'34bf565a-5'+','+'34bf565a-1'}}" wx:if="{{$root.g0}}"></page-ad>
    <comp-act-list bind:__l="__l" list="{{hpaList}}" vueId="{{'34bf565a-6'+','+'34bf565a-1'}}" wx:if="{{$root.g1}}"></comp-act-list>
    <comp-entry-tip bind:__l="__l" vueId="{{'34bf565a-7'+','+'34bf565a-1'}}"></comp-entry-tip>
</comp-page>
