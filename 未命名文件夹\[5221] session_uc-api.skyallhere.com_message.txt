POST /miniprogram/api/v2/user/signup HTTP/1.1
Host: uc-api.skyallhere.com
Connection: keep-alive
Content-Length: 2356
App-Path: /pages/login/login
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
Content-Type: application/json
App-System: Windows 10 x64
xweb_xhr: 1
App-Model: microsoft
App-Sdkversion: 3.8.12
App-Version: 3.9.12
Accept: */*
Sec-Fetch-Site: cross-site
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
Referer: https://servicewechat.com/wxff438d3c60c63fb6/342/page-frame.html
Accept-Encoding: gzip, deflate, br
Accept-Language: zh-CN,zh;q=0.9

{"phoneEncrypted":"KhvmawZcQN6O+1iXl+6wgqwHmVTTCrNcRWEnGDohkqlC76e77daG7R1vorK8qvbgd2oWsrZkJy9kW9Rdr1DwJS1dEl+o9uyGadn/2rABzoXRJHOzw+l+PeyGeKjyx82SlaW3HDw8Z2u1ODvtGgyaMa7n+OwbEMOHfRvQycqjkrB8GNIligJRcaNPk8rjp1ZOdHJiryJZV6ZvVuY8NBzzYw==","phoneCode":"44a6c5ed80efe02b6819d21a78e03db8dd2c374496e197b55b328f7b6526cb7a","phoneIv":"9bH4sXCGebnNTxFN7Z7NnA==","userInfoEncrypted":"WoIFuCrSeKGSP+SIUDrLM2WQfFTRklAocpJfFikUdFdD0ShD2pRO6BlpQIVfovFpwx+J0l5qGMdy8f2/nM5aWNn/VWYqTiNqGYZSW4rrq2ejCTRe/ZG1eWpfmK+minnwjmxaObtyK7dLRxMHwW5LMABNc2H+YmFJ7GxCLXmV+NwPxBQ6m70pi91/u25iowhUUhFv2CBnxd3dyoxS6yMMsd48IyCAWQw+TzLXF8FwvCGAeBEPD2rgFF+QklVR1up0+2dcfR00CTvFINLtRLXQGZqB1lxMGU+/3GLift9awn+LkmhhZZ149N5rLgNuuV8COBfaO7oSjAF5B9CDDU191pLN8tclAN/CoMriPlJS7IYjZ3LU9w+4PLYRzMkqa7RfezWAvZCVV4oPwaVhB2AvL4uBnR5gFVyflaRkWoC9nImQFA1jISsShip+h09ajdap","userInfoIv":"Ai/Of93ne0ckXwgDIImyOw==","extra":"{\"encryptedData\":\"WoIFuCrSeKGSP+SIUDrLM2WQfFTRklAocpJfFikUdFdD0ShD2pRO6BlpQIVfovFpwx+J0l5qGMdy8f2/nM5aWNn/VWYqTiNqGYZSW4rrq2ejCTRe/ZG1eWpfmK+minnwjmxaObtyK7dLRxMHwW5LMABNc2H+YmFJ7GxCLXmV+NwPxBQ6m70pi91/u25iowhUUhFv2CBnxd3dyoxS6yMMsd48IyCAWQw+TzLXF8FwvCGAeBEPD2rgFF+QklVR1up0+2dcfR00CTvFINLtRLXQGZqB1lxMGU+/3GLift9awn+LkmhhZZ149N5rLgNuuV8COBfaO7oSjAF5B9CDDU191pLN8tclAN/CoMriPlJS7IYjZ3LU9w+4PLYRzMkqa7RfezWAvZCVV4oPwaVhB2AvL4uBnR5gFVyflaRkWoC9nImQFA1jISsShip+h09ajdap\",\"iv\":\"Ai/Of93ne0ckXwgDIImyOw==\",\"signature\":\"213d3117854bf57582607e47e1fd32c12d74d858\",\"userInfo\":{\"nickName\":\"微信用户\",\"gender\":0,\"language\":\"\",\"city\":\"\",\"province\":\"\",\"country\":\"\",\"avatarUrl\":\"https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132\",\"is_demote\":true},\"rawData\":\"{\\\"nickName\\\":\\\"微信用户\\\",\\\"gender\\\":0,\\\"language\\\":\\\"\\\",\\\"city\\\":\\\"\\\",\\\"province\\\":\\\"\\\",\\\"country\\\":\\\"\\\",\\\"avatarUrl\\\":\\\"https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132\\\",\\\"is_demote\\\":true}\",\"errMsg\":\"getUserProfile:ok\"}","TDTicket":"9ca17ae2e6fed1af2aa8fef6c3a128e2beeedba15e8eada2b8f034bcb0acaec45b928b9ab3c46a88b88c99b85086b882aec170f6fee4c3f72afafe9e91f379a3ef97afb46dbc998dd4db46fcb4ff96d14bffe3ee9e","ticket":"bb3617cf0304949e599e4c0b6b18360e"}

HTTP/1.1 200 OK
Date: Fri, 01 Aug 2025 14:56:08 GMT
Content-Type: application/json; charset=utf-8
Content-Length: 376
Connection: keep-alive
X-Request-Id: 5cb22abce15a0a100490d116a6a13288
Access-Control-Allow-Origin: *
Access-Control-Allow-Credentials: true

{"code":0,"data":{"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJvcGVuaWQiOiJvTVNTZzRnbXBwOWp5ajJaS2F4OGc3TmhsUEJJIiwidW5pb25pZCI6Im9BdWVHamp0T1hUQmRuS2lhUDk2U0VUcUZiNTQiLCJ1c2VyaWQiOjE5NDYwMzk3LCJ1c2VyX2NvZGUiOiI0MmQ0MDY2MGRkOTIyOGMwIiwidXNlcl9waG9uZSI6IjE4MTExNDU1NjI3Iiwibmlja19uYW1lIjoiIiwiZXhwIjoxNzU0MDYxOTY4fQ.WSELB3eRXwxIL-ux1XyDzhrWgjt3vgGOXF3DmquKEiQ"},"msg":"ok"}