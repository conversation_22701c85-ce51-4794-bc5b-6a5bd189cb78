<page-panel bind:__l="__l" bind:clickMore="__e" class="page-act-list data-v-872dcafc" data-event-opts="{{[ [ '^clickMore',[ ['clickMore'] ] ] ]}}" isMore="{{true}}" title="活动中心" vueId="23e96864-1" vueSlots="{{['bd']}}">
    <view class="data-v-872dcafc" slot="bd">
        <view class="data-v-872dcafc" wx:if="{{$root.g0>0}}">
            <view bindtap="__e" class="act-banner data-v-872dcafc" data-event-opts="{{[ [ 'tap',[ [ 'clickAct',['$0'],['list.__$n0'] ] ] ] ]}}">
                <image class="banner-img data-v-872dcafc" mode="aspectFill" src="{{list[0].image}}"></image>
            </view>
            <view class="act-list data-v-872dcafc">
                <view bindtap="__e" class="act-card data-v-872dcafc" data-event-opts="{{[ [ 'tap',[ [ 'e0',['$event'] ] ] ] ]}}" data-event-params="{{({ item:item[$orig] })}}" wx:for="{{$root.l0}}" wx:key="id">
                    <image class="act-img data-v-872dcafc" mode="aspectFill" src="{{item[$orig].image}}"></image>
                    <view class="act-info data-v-872dcafc">
                        <view class="act-title data-v-872dcafc">{{item[$orig].name}}</view>
                        <view class="act-status data-v-872dcafc">活动进行中</view>
                        <view class="act-time data-v-872dcafc">{{item.m0+'至'+item.m1}}</view>
                    </view>
                    <view class="act-btn data-v-872dcafc">立即参加</view>
                </view>
            </view>
        </view>
    </view>
</page-panel>
