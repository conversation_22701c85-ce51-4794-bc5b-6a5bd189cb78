<view class="{{['comp-dialog','flex-row','col-center','data-v-fcfa84a4','is-'+type]}}" wx:if="{{show}}">
    <view class="main data-v-fcfa84a4">
        <view class="hd flex-col col-center data-v-fcfa84a4">
            <view class="icon data-v-fcfa84a4" wx:if="{{icon}}">
                <comp-image bind:__l="__l" class="data-v-fcfa84a4" height="120rpx" name="{{icon}}" vueId="480cbc34-1" width="120rpx"></comp-image>
            </view>
            <view class="name data-v-fcfa84a4" wx:if="{{name}}">{{name}}</view>
        </view>
        <view class="bd flex-col col-center data-v-fcfa84a4">
            <view class="subname data-v-fcfa84a4" wx:if="{{subname&&!activeSubname}}">{{subname}}</view>
            <view class="data-v-fcfa84a4" wx:if="{{activeSubname}}">
                <text class="subname data-v-fcfa84a4">{{subname?subname:''}}</text>
                <text bindtap="__e" class="subname data-v-fcfa84a4" data-event-opts="{{[ [ 'tap',[ [ 'handleActive',['$event'] ] ] ] ]}}" style="color:#08bd10;">{{activeSubname}}</text>
                <text class="subname data-v-fcfa84a4">{{subnameBottom?subnameBottom:''}}</text>
            </view>
        </view>
        <view class="ft data-v-fcfa84a4" wx:if="{{btnName||extBtnName}}">
            <view class="{{['btn__wrap','flex-one','data-v-fcfa84a4',btnType?'is-'+btnType:'']}}" wx:if="{{btnName}}">
                <comp-button bind:__l="__l" bind:getPhoneNumber="__e" bind:getUserInfo="__e" bind:onClick="__e" class="data-v-fcfa84a4" data-event-opts="{{[ [ '^onClick',[ ['btnClick'] ] ],[ '^getUserInfo',[ ['btnGetUserInfo'] ] ],[ '^getPhoneNumber',[ ['btnGetPhoneNumber'] ] ] ]}}" openType="{{btnOpenType}}" vueId="480cbc34-2" vueSlots="{{['default']}}">
                    <view class="btn flex-row row-center col-center data-v-fcfa84a4">{{''+btnName+''}}</view>
                </comp-button>
            </view>
            <view class="{{['btn__wrap','flex-one','is-ext','data-v-fcfa84a4',extBtnType?'is-'+extBtnType:'']}}" wx:if="{{extBtnName}}">
                <comp-button bind:__l="__l" bind:getPhoneNumber="__e" bind:getUserInfo="__e" bind:onAgreeprivacyauthorization="__e" bind:onClick="__e" class="data-v-fcfa84a4" data-event-opts="{{[ [ '^onClick',[ ['extBtnClick'] ] ],[ '^getUserInfo',[ ['extBtnGetUserInfo'] ] ],[ '^onAgreeprivacyauthorization',[ ['handleAuthorization'] ] ],[ '^getPhoneNumber',[ ['extBtnGetPhoneNumber'] ] ] ]}}" openType="{{extBtnOpenType}}" vueId="480cbc34-3" vueSlots="{{['default']}}">
                    <view class="btn flex-row row-center col-center data-v-fcfa84a4">{{''+extBtnName+''}}</view>
                </comp-button>
            </view>
        </view>
    </view>
</view>
