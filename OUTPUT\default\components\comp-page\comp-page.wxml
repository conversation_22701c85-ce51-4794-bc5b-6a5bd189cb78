<view class="{{['comp-page','data-v-5dca7865',indexGray?'indexGray':'']}}">
    <view class="{{['data-v-5dca7865',true?'mp-navigation-bar--transparent ':'',true?'mp-navigation-bar':'']}}" style="{{'background:'+pageNavbarBackground+';'}}">
        <view class="nav-top data-v-5dca7865" style="{{'display:'+'flex'+';'+'align-items:'+'center'+';'+'height:'+'88rpx'+';'+'text-align:'+'center'+';'+'padding-top:'+pageNavbarHeight+'rpx'+';'+'color:'+pageNavbarColor+';'}}">
            <view class="data-v-5dca7865" style="display:flex;justify-content:center;width:100%;">{{''+pageNavbarTitle+''}}</view>
            <view class="nav-left data-v-5dca7865" slot="left" wx:if="{{!pageIsLoading}}">
                <view bindtap="__e" class="navbar-home flex-row col-center data-v-5dca7865" data-event-opts="{{[ [ 'tap',[ [ 'clickNavbarHome',['$event'] ] ] ] ]}}" style="{{'border-color:'+pageNavbarColor+';'}}" wx:if="{{pageNavbarIsHome}}">
                    <mp-icon bind:__l="__l" class="data-v-5dca7865" color="{{pageNavbarColor}}" data-com-type="wx" icon="home" size="{{24}}" type="field" vueId="79cc3fe6-1"></mp-icon>
                </view>
                <block wx:else>
                    <view bindtap="__e" class="navbar-back flex-row col-center data-v-5dca7865" data-event-opts="{{[ [ 'tap',[ [ 'clickNavbarBack',['$event'] ] ] ] ]}}" style="{{'border-color:'+pageNavbarColor+';'}}" wx:if="{{pageNavbarIsBack}}">
                        <mp-icon bind:__l="__l" class="data-v-5dca7865" color="{{pageNavbarColor}}" data-com-type="wx" icon="back" size="{{12}}" type="field" vueId="79cc3fe6-2"></mp-icon>
                    </view>
                </block>
            </view>
        </view>
    </view>
    <comp-error bind:__l="__l" class="data-v-5dca7865" message="{{pageError.message}}" title="{{pageError.title}}" top="{{pageNavbarHeight}}" type="{{pageError.type}}" vueId="79cc3fe6-3" wx:if="{{pageError}}"></comp-error>
    <comp-skeleton bind:__l="__l" class="data-v-5dca7865" loading="{{pageLoading}}" top="{{isNavbar?pageNavbarHeight:0}}" vueId="79cc3fe6-4" wx:if="{{isSkeleton&&pageLoading}}"></comp-skeleton>
    <mp-emoji bind:__l="__l" class="emoji data-v-5dca7865 vue-ref" data-com-type="wx" data-ref="emoji" source="{{pageEmoji}}" vueId="79cc3fe6-5" wx:if="{{isEmoji}}"></mp-emoji>
    <view class="main data-v-5dca7865" style="{{'min-height:'+pageMainMinHeight+';'+'background:'+pageMainBackground+';'+'background-size:'+pageMainBackgroundSize+';'}}" wx:if="{{isMain&&!pageLoading&&!pageError}}">
        <view class="hd data-v-5dca7865" style="{{'top:'+pageNavbarHeight+'px'+';'+'z-index:'+pageHdZindex+';'}}">
            <slot name="hd"></slot>
        </view>
        <view class="bd data-v-5dca7865" style="{{'background:'+pageBdBackground+';'+'padding-top:'+(pageNavbarIsTransparent?0:88+pageNavbarHeight+'rpx')+';'+'padding-bottom:'+pageFtHeight+'px'+';'}}">
            <slot></slot>
            <view class="{{['brand','data-v-5dca7865',brandSafeBottom?'safe-bottom':'']}}" style="{{'z-index:'+brandZindex+';'}}" wx:if="{{isBrand}}">
                <comp-image bind:__l="__l" class="data-v-5dca7865" height="100%" name="{{brandIcon}}" vueId="79cc3fe6-6" width="100%"></comp-image>
            </view>
            <comp-contact bind:__l="__l" class="data-v-5dca7865" vueId="79cc3fe6-7" wx:if="{{showContact}}"></comp-contact>
        </view>
        <view class="ft data-v-5dca7865" style="{{'z-index:'+pageFtZindex+';'}}">
            <slot name="ft"></slot>
        </view>
        <view class="btn data-v-5dca7865" style="{{'z-index:'+pageBtnZindex+';'+'padding-bottom:'+pageBtnPaddingBottom+';'}}">
            <slot name="btn"></slot>
        </view>
    </view>
</view>
