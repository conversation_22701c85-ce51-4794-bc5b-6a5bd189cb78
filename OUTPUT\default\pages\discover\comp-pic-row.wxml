<view class="pic-row data-v-de8c5bac">
    <swiper autoplay="{{true}}" bindchange="__e" circular="{{true}}" class="data-v-de8c5bac" data-event-opts="{{[ [ 'change',[ [ 'change',['$event'] ] ] ] ]}}" interval="{{3000}}" nextMargin="154px" style="height:101px;overflow:hidden;">
        <swiper-item class="data-v-de8c5bac" wx:if="{{item.isShow}}" wx:for="{{list}}" wx:key="index">
            <view class="width400 data-v-de8c5bac">
                <comp-button bind:__l="__l" bind:onClick="__e" class="data-v-de8c5bac" data-event-opts="{{[ [ '^onClick',[ [ 'clickAct',['$0'],[ [ ['list','',index] ] ] ] ] ] ]}}" vueId="{{'7bfee330-1-'+index}}" vueSlots="{{['default']}}">
                    <view class="card data-v-de8c5bac" style="{{'background:'+'url('+item.columnIcon+'?t=1'+')'+';'+'background-size:'+'100%'+';'+'background-position:'+'center center'+';'}}"></view>
                </comp-button>
            </view>
        </swiper-item>
    </swiper>
</view>
