.page-nav.data-v-0e8688be {
    background: #fff;
    border-bottom: 24rpx solid #f5f5f5;
    margin-bottom: 24rpx;
    padding: 21rpx 32rpx 42rpx
}

.bbnone.data-v-0e8688be {
    border-bottom: none;
    margin-bottom: 0;
    padding: 21rpx 32rpx 0
}

.h3.data-v-0e8688be {
    color: rgba(0,0,0,.85);
    font-family: PingFangSC-Semibold,PingFang SC;
    font-size: 32rpx;
    font-weight: 600;
    height: 48rpx;
    line-height: 48rpx;
    margin: 16rpx 0 26rpx
}

.card-box.data-v-0e8688be {
    background: #fff
}

.nav.data-v-0e8688be {
    padding: 9rpx 8rpx;
    width: 50%
}

.item.data-v-0e8688be {
    border-radius: 16rpx;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: center;
    justify-content: center
}

.item .icon.data-v-0e8688be {
    -webkit-flex: 1;
    flex: 1;
    margin: 0 auto
}

.item .name.data-v-0e8688be {
    margin-top: 8rpx
}

.item .name.data-v-0e8688be,.item .subname.data-v-0e8688be {
    color: rgba(0,0,0,.85);
    font-family: PingFang-SC-Medium,PingFang-SC;
    font-size: 24rpx;
    font-weight: 500;
    height: 33rpx;
    line-height: 33rpx
}

.one_row.data-v-0e8688be {
    height: 66rpx;
    line-height: 66rpx
}

.page-nav__item.data-v-0e8688be {
    color: var(--color-text-title);
    font-size: var(--font-size-24);
    margin-top: 8rpx;
    width: 25%
}
