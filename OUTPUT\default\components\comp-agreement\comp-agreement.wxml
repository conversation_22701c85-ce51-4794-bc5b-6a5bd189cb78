<view class="comp-agreement flex-col row-center col-center data-v-537724d3" wx:if="{{show}}">
    <view class="main flex-col data-v-537724d3">
        <view class="hd flex-row row-end data-v-537724d3">
            <view bindtap="__e" class="close data-v-537724d3" data-event-opts="{{[ [ 'tap',[ [ 'close',['$event'] ] ] ] ]}}">
                <comp-icon bind:__l="__l" class="data-v-537724d3" icon="iconclose_o" size="56rpx" vueId="243253c2-1"></comp-icon>
            </view>
        </view>
        <view class="bd flex-col data-v-537724d3">
            <view class="tabs flex-row data-v-537724d3">
                <view bindtap="__e" class="{{['tab','data-v-537724d3',activeTab===item.id?'is-active':'',showCoocaa?'font28':'']}}" data-event-opts="{{[ [ 'tap',[ [ 'switchTab',['$0'],[ [ ['tabsData','',index] ] ] ] ] ] ]}}" wx:for="{{tabsData}}" wx:key="index">{{''+item.name+''}}</view>
            </view>
        </view>
        <view class="ft flex-col flex-one data-v-537724d3">
            <view class="ft__main account flex-col data-v-537724d3" wx:if="{{activeTab===1}}">
                <view class="data-v-537724d3" style="{{'font-weight:'+(item.isBlod?'bold':'normal')+';'+'margin-top:'+(item.isTopSpace?'40rpx':'auto')+';'}}" wx:for="{{accountAgreement}}" wx:key="index">{{''+item.block+''}}</view>
            </view>
            <view class="ft__main privacy flex-col data-v-537724d3" wx:if="{{activeTab===2}}">
                <view class="data-v-537724d3" style="{{'font-weight:'+(item.isBlod?'bold':'normal')+';'+'margin-top:'+(item.isTopSpace?'40rpx':'auto')+';'}}" wx:for="{{privacyAgreement}}" wx:key="index">{{''+item.block+''}}</view>
            </view>
            <view class="ft__main privacy flex-col data-v-537724d3" wx:if="{{showCoocaa&&activeTab===3}}">
                <view class="data-v-537724d3" style="{{'font-weight:'+(item.isBlod?'bold':'normal')+';'+'margin-top:'+(item.isTopSpace?'40rpx':'auto')+';'}}" wx:for="{{coocaaAgreement}}" wx:key="index">{{''+item.block+''}}</view>
            </view>
        </view>
        <view class="gradients data-v-537724d3"></view>
    </view>
</view>
