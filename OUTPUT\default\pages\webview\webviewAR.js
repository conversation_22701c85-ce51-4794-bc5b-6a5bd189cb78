(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/webview/webviewAR"], {
    "2ecc": function ecc(t, e, o) {


      (function(t, e) {
        var n = o("47a9");
        o("5c38");
        n(o("3240"));
        var r = n(o("4111"));
        t.__webpack_require_UNI_MP_PLUGIN__ = o, e(r.default);
      }).call(this, o("3223")["default"], o("df3c")["createPage"]);
    },
    4111: function _(t, e, o) {


      o.r(e);
      var n = o("e37c"),
        r = o("5de7");
      for (var c in r)["default"].indexOf(c) < 0 && function(t) {
        o.d(e, t, function() {
          return r[t];
        });
      }(c);
      var a = o("828b"),
        i = Object(a["a"])(r["default"], n["b"], n["c"], !1, null, null, null, !1, n["a"], void 0);
      e["default"] = i.exports;
    },
    "5de7": function de7(t, e, o) {


      o.r(e);
      var n = o("cf56"),
        r = o.n(n);
      for (var c in n)["default"].indexOf(c) < 0 && function(t) {
        o.d(e, t, function() {
          return n[t];
        });
      }(c);
      e["default"] = r.a;
    },
    cf56: function cf56(t, e, o) {


      (function(t) {
        var n = o("47a9");
        Object.defineProperty(e, "__esModule", {
          value: !0
        }), e.default = void 0;
        var r = n(o("7ca3")),
          c = o("8f59"),
          a = n(o("a23b"));
        n(o("bdc5"));

        function i(t, e) {
          var o = Object.keys(t);
          if (Object.getOwnPropertySymbols) {
            var n = Object.getOwnPropertySymbols(t);
            e && (n = n.filter(function(e) {
              return Object.getOwnPropertyDescriptor(t, e).enumerable;
            })), o.push.apply(o, n);
          }
          return o;
        }
        var u = {
          data: function data() {
            return {
              isFirst: !0,
              isClickBack: !1,
              url: "",
              backgroundColor: "#ffffff",
              frontColor: "#000000",
              tag: ""
            };
          },
          computed: function(t) {
            for (var e = 1; e < arguments.length; e++) {
              var o = null != arguments[e] ? arguments[e] : {};
              e % 2 ? i(Object(o), !0).forEach(function(e) {
                (0, r.default)(t, e, o[e]);
              }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(o)) : i(Object(o)).forEach(function(e) {
                Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(o, e));
              });
            }
            return t;
          }({}, (0, c.mapState)(["profile"])),
          onShareAppMessage: function onShareAppMessage() {
            var t = "/pages/webview/webviewAR?url=".concat(encodeURIComponent(this.url), "&backgroundColor=").concat(this.backgroundColor, "&fontColor=").concat(this.frontColor);
            return this.tag && (t = "/pages/webview/webviewAR?tag=".concat(this.tag)), a.default.generateShareInfo({
              path: t,
              imageUrl: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/share_logo.jpg"
            });
          },
          onShow: function onShow() {
            a.default.log("Webview onShow", "当前 URL ".concat(this.url)), this.isClickBack = !1;
          },
          onHide: function onHide() {
            this.url = "", this.isClickBack, a.default.log("Webview onHide", "当前 URL ".concat(this.url));
          },
          methods: {
            navbarBack: function navbarBack() {
              this.clickBack = !0, t.navigateBack({
                delta: 1
              });
            },
            fetch: function fetch() {
              var t = a.default.getRoute().options || {};
              a.default.log("Webview fetch 当前 query 为", "".concat(t)), this.init(t);
            },
            afterFetch: function afterFetch() {
              this.isFirst = !1;
            },
            init: function init() {
              var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
                o = decodeURIComponent(e.url || ""),
                n = e.backgroundColor || "#ffffff",
                r = e.frontColor || "#000000";
              e.from;
              /\?/g.test(o) ? this.url = o : this.url = o + "?r=" + Math.random(), console.log("h5页面URL:", this.url), this.backgroundColor = n, this.frontColor = r, t.setNavigationBarColor({
                frontColor: r,
                backgroundColor: n,
                fail: function fail(t) {
                  a.default.log("Webview: setNavigationBarColor fail", t);
                }
              });
            }
          }
        };
        e.default = u;
      }).call(this, o("df3c")["default"]);
    },
    e37c: function e37c(t, e, o) {


      o.d(e, "b", function() {
        return r;
      }), o.d(e, "c", function() {
        return c;
      }), o.d(e, "a", function() {
        return n;
      });
      var n = {
          compPage: function compPage() {
            return Promise.all([o.e("common/vendor"), o.e("components/comp-page/comp-page")]).then(o.bind(null, "fccb"));
          }
        },
        r = function r() {
          var t = this.$createElement;
          this._self._c;
        },
        c = [];
    }
  },
  [
    ["2ecc", "common/runtime", "common/vendor"]
  ]
]);