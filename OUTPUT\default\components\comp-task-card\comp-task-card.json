{"component": true, "usingComponents": {"comp-image": "components\\comp-task-card\\components\\comp-image\\comp-image", "comp-button": "components\\comp-task-card\\components\\comp-button\\comp-button", "duiba-pay": "components\\comp-task-card\\wxcomponents\\duiba-miniprogram-pay\\index", "painter": "components\\comp-task-card\\wxcomponents\\painter\\painter", "mp-action-sheet": "components\\comp-task-card\\miniprogram_npm\\weui-miniprogram\\actionsheet\\actionsheet", "mp-cell": "components\\comp-task-card\\miniprogram_npm\\weui-miniprogram\\cell\\cell", "mp-cells": "components\\comp-task-card\\miniprogram_npm\\weui-miniprogram\\cells\\cells", "mp-emoji": "components\\comp-task-card\\wxcomponents\\emoji\\index", "mp-half-screen-dialog": "components\\comp-task-card\\miniprogram_npm\\weui-miniprogram\\half-screen-dialog\\half-screen-dialog", "mp-icon": "components\\comp-task-card\\miniprogram_npm\\weui-miniprogram\\icon\\icon", "mp-loading": "components\\comp-task-card\\miniprogram_npm\\weui-miniprogram\\loading\\loading", "mp-navigation-bar": "components\\comp-task-card\\miniprogram_npm\\weui-miniprogram\\navigation-bar\\navigation-bar", "mp-slideview": "components\\comp-task-card\\miniprogram_npm\\weui-miniprogram\\slideview\\slideview", "mi-lin-video": ".\\components\\comp-task-card\\plugin:\\wx8d04ad0492dab24d\\miLin-video"}}