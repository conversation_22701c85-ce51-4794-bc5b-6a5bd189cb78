<view class="comp-vertical-card data-v-0780e7da">
    <view bindtap="__e" class="is-card--10 data-v-0780e7da" data-event-opts="{{[ [ 'tap',[ [ 'clickCard',['$event'] ] ] ] ]}}" style="{{'width:'+(width?width:'410rpx')+';'+'height:'+(height?height:'546rpx')+';'}}" wx:if="{{type===10}}">
        <comp-image bind:__l="__l" class="data-v-0780e7da" height="{{height?height:'546rpx'}}" isServer="{{true}}" isVideoCover="{{isVideo}}" name="{{img}}" radius="16rpx" vueId="4ea2e962-1" width="{{width?width:'410rpx'}}"></comp-image>
        <view class="card__main flex-col space-between data-v-0780e7da">
            <view class="card__name data-v-0780e7da">
                <view class="multi-nowrap data-v-0780e7da">{{name}}</view>
            </view>
            <view class="card__subname data-v-0780e7da">
                <view class="multi-nowrap data-v-0780e7da">{{subname}}</view>
            </view>
        </view>
    </view>
    <view bindtap="__e" class="is-card--11 data-v-0780e7da" data-event-opts="{{[ [ 'tap',[ [ 'clickCard',['$event'] ] ] ] ]}}" style="{{'width:'+(width?width:'292rpx')+';'}}" wx:if="{{type===11}}">
        <comp-image bind:__l="__l" class="data-v-0780e7da" height="{{height?height:'220rpx'}}" isServer="{{true}}" isVideoCover="{{isVideo}}" name="{{img}}" radius="8rpx 8rpx 0 0" videoIconSize="64rpx" vueId="4ea2e962-2" width="{{width?width:'292rpx'}}"></comp-image>
        <view class="card__main data-v-0780e7da">
            <view class="card__name data-v-0780e7da">
                <view class="multi-nowrap data-v-0780e7da">{{name}}</view>
            </view>
        </view>
    </view>
</view>
