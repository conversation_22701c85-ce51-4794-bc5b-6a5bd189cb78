.fixed-header {
    -webkit-align-items: center;
    align-items: center;
    background-color: #bb0204;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    height: 130rpx;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
    left: 0;
    position: fixed;
    top: 0;
    -webkit-transform: translateY(-100%);
    transform: translateY(-100%);
    transition: -webkit-transform .4s ease;
    transition: transform .4s ease;
    transition: transform .4s ease,-webkit-transform .4s ease;
    width: 100%;
    will-change: transform;
    z-index: 100
}

.header-area {
    overflow: hidden;
    position: relative;
    width: 100%
}

.header-image {
    display: block;
    width: 100%
}

.countdown-section {
    bottom: 0;
    left: 0;
    padding: 20rpx 40rpx;
    position: absolute;
    right: 0;
    z-index: 10
}

.countdown-container {
    -webkit-justify-content: center;
    justify-content: center
}

.countdown-container,.countdown-item {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex
}

.countdown-item {
    margin-right: 10rpx
}

.countdown-label {
    color: #fff;
    font-size: 28rpx;
    font-weight: 600
}

.countdown-time {
    -webkit-align-items: center;
    align-items: center;
    background-color: #fff;
    border-radius: 8rpx;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: center;
    justify-content: center;
    margin-left: 10rpx;
    padding: 2rpx 10rpx;
    width: 50rpx
}

.countdown-number {
    color: #ff0c0f;
    font-size: 40rpx;
    font-weight: 600
}

.countdown-unit {
    color: #fff;
    font-size: 28rpx;
    font-weight: 600;
    margin-left: 10rpx
}

.activity-tabs-color {
    background-color: #f7342f
}

.activity-tabs {
    border-top-left-radius: 30rpx;
    border-top-right-radius: 30rpx;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: center;
    justify-content: center
}

.tab-item-color {
    -webkit-flex: 1;
    flex: 1
}

.tab-item-color-first-1 {
    background-color: #e6e5e5;
    border-top-left-radius: 30rpx
}

.tab-item-first-1 {
    border-top-left-radius: 30rpx;
    padding: 20rpx
}

.tab-item-color-first-2,.tab-item-first-1 {
    background-color: #f5f5f5;
    border-top-right-radius: 30rpx
}

.tab-item-first-2 {
    background-color: #e6e5e5;
    border-bottom-left-radius: 30rpx;
    border-top-right-radius: 30rpx;
    padding: 20rpx
}

.tab-item-color-second-1 {
    background-color: #f5f5f5;
    border-top-left-radius: 30rpx
}

.tab-item-second-1 {
    background-color: #e6e5e5;
    border-bottom-right-radius: 30rpx;
    border-top-left-radius: 30rpx;
    padding: 20rpx
}

.tab-item-color-second-2 {
    background-color: #e6e5e5;
    border-top-right-radius: 30rpx
}

.tab-item-second-2 {
    padding: 20rpx
}

.tab-item-second-2,.tab-item-single,.tab-item-single-inner {
    background-color: #f5f5f5;
    border-top-left-radius: 30rpx;
    border-top-right-radius: 30rpx
}

.tab-item-single-inner {
    padding: 20rpx
}

.tab-item,.tab-title {
    text-align: center
}

.tab-title {
    color: rgba(0,0,0,.4);
    font-size: 32rpx;
    font-weight: 700;
    margin-bottom: 8rpx
}

.tab-item.active .tab-title {
    color: #f44
}

.tab-status {
    text-align: center
}

.tab-status-text {
    color: rgba(0,0,0,.4);
    font-size: 22rpx;
    line-height: 1.2
}

.tab-item.active .tab-status-text {
    color: #f44
}

.tab-indicator {
    border-radius: 3rpx;
    height: 6rpx;
    margin-top: 10rpx;
    width: 60rpx
}

.tab-item.active .tab-indicator {
    background-color: #fff
}

.product-list {
    margin: 30rpx 0;
    padding: 0 24rpx
}

.product-item {
    background-color: #fff;
    border-radius: 24rpx;
    display: -webkit-flex;
    display: flex;
    margin-bottom: 16rpx;
    padding: 16rpx
}

.product-image-container {
    border-radius: 12rpx;
    height: 262rpx;
    overflow: hidden;
    width: 262rpx
}

.product-image {
    height: 100%;
    width: 100%
}

.product-info {
    -webkit-flex: 1;
    flex: 1;
    margin-left: 20rpx;
    position: relative
}

.product-title {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    color: rgba(0,0,0,.8);
    display: -webkit-box;
    font-size: 32rpx;
    font-weight: 700;
    line-height: 45rpx;
    min-height: 90rpx;
    overflow: hidden;
    text-overflow: ellipsis
}

.product-price-container {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex;
    margin-top: 20rpx
}

.product-price {
    -webkit-align-items: flex-end;
    align-items: flex-end;
    display: -webkit-flex;
    display: flex
}

.price-symbol {
    color: #f7342f;
    font-size: 24rpx;
    line-height: 1;
    margin-bottom: 15rpx
}

.price-value {
    color: #f7342f;
    font-size: 44rpx;
    font-weight: 700
}

.product-save-price {
    color: #fe301a;
    font-size: 22rpx;
    font-weight: 700;
    margin-left: 10rpx
}

.product-original-price {
    color: rgba(0,0,0,.4);
    font-size: 22rpx;
    margin-left: 10rpx
}

.product-tag {
    background-color: #fff2f3;
    border: 1rpx solid #ffb3b6;
    border-radius: 15rpx 15rpx 15rpx 0;
    display: inline-block;
    padding: 0 7rpx
}

.tag-text {
    color: #fe301a;
    font-size: 22rpx;
    font-weight: 700
}

.product-progress {
    -webkit-align-items: center;
    align-items: center;
    background-image: url(https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/%<EMAIL>);
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: cover;
    border-radius: 8rpx;
    display: -webkit-flex;
    display: flex;
    height: 80rpx;
    margin-top: 10rpx;
    position: relative
}

.progress-bar {
    background-color: #fdced2;
    border-radius: 9rpx;
    height: 18rpx;
    margin-left: 10rpx;
    overflow: hidden;
    width: 40%
}

.progress-inner {
    background: linear-gradient(90deg,#ff6b6b,#fe301a);
    border-radius: 9rpx;
    height: 100%
}

.progress-text {
    color: #fe301a;
    font-size: 22rpx;
    font-weight: 700;
    margin-left: 20rpx
}

.product-badge {
    background-color: #ff1e2b;
    border-bottom-left-radius: 24rpx;
    border-top-right-radius: 8rpx;
    -webkit-justify-content: center;
    justify-content: center;
    position: absolute;
    right: 0;
    top: 0;
    width: 80rpx
}

.product-badge,.reservation-button {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex;
    height: 80rpx
}

.reservation-button {
    background-image: url(https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/<EMAIL>);
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: cover;
    border-radius: 8rpx;
    margin-top: 10rpx;
    position: relative
}

.reservation-button.reserved {
    background-image: url(https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/<EMAIL>);
    cursor: not-allowed;
    opacity: .8;
    pointer-events: none
}

.reservation-button.disabled {
    background-image: url(https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/<EMAIL>);
    cursor: not-allowed;
    opacity: .6;
    pointer-events: none
}

.reservation-text {
    color: #ff9c1e;
    font-size: 22rpx;
    font-weight: 700;
    margin-left: 20rpx
}

.reservation-button:active {
    -webkit-transform: scale(.95);
    transform: scale(.95);
    transition: -webkit-transform .1s;
    transition: transform .1s;
    transition: transform .1s,-webkit-transform .1s
}

.badge-text {
    color: #fff;
    font-size: 48rpx;
    font-weight: 700
}

.empty-state {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    -webkit-justify-content: center;
    justify-content: center;
    padding: 80rpx 40rpx;
    text-align: center
}

.empty-image {
    margin-bottom: 30rpx;
    width: 300rpx
}

.empty-text {
    color: rgba(0,0,0,.4);
    font-size: 25rpx;
    font-weight: 500;
    line-height: 40rpx;
    margin: 10rpx 0
}

.loading-more {
    text-align: center
}

.loading-text {
    color: rgba(0,0,0,.4);
    font-size: 24rpx;
    line-height: 40rpx
}
