<page-panel bind:__l="__l" bind:clickMore="__e" class="page-guide data-v-46b1b01c" data-event-opts="{{[ [ '^clickMore',[ ['clickMore'] ] ] ]}}" title="玩机指南" vueId="71c4314e-1" vueSlots="{{['bd']}}">
    <scroll-view class="banner data-v-46b1b01c" scrollX="{{true}}" slot="bd">
        <view class="list flex-row data-v-46b1b01c" style="{{'width:'+205*$root.g0+28+'px'+';'}}">
            <view bindtap="__e" class="{{['item','data-v-46b1b01c',index===$root.g1-1?'is-last':'']}}" data-event-opts="{{[ [ 'tap',[ [ 'clickItem',['$0'],[ [ ['list','',index] ] ] ] ] ] ]}}" wx:for="{{list}}" wx:key="index">
                <comp-vertical-card bind:__l="__l" class="data-v-46b1b01c" img="{{item.img}}" isVideo="{{item.type===2}}" name="{{item.name}}" subname="{{item.subname}}" type="{{10}}" vueId="{{'71c4314e-2-'+index+','+'71c4314e-1'}}" width="205px"></comp-vertical-card>
            </view>
        </view>
    </scroll-view>
</page-panel>
