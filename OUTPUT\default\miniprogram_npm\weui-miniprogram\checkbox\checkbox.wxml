<label bindtap="checkedChange">
    <mp-cell extClass="weui-check__label {{outerClass}} {{extClass}} {{!multi?'^weui-cell_radio':'^weui-cell_checkbox'}}" extHoverClass="weui-active" hasFooter="{{!multi}}" hasHeader="{{multi}}">
        <view slot="icon" wx:if="{{multi}}">
            <checkbox checked="{{checked}}" class="weui-check" color="{{color}}" disabled="{{disabled}}" value="{{value}}"></checkbox>
            <icon class="weui-icon-checked"></icon>
        </view>
        <view>{{label}}</view>
        <view slot="footer" wx:if="{{!multi}}">
            <radio checked="{{checked}}" class="weui-check" color="{{color}}" disabled="{{disabled}}" value="{{value}}"></radio>
            <icon class="weui-icon-checked"></icon>
        </view>
    </mp-cell>
</label>
