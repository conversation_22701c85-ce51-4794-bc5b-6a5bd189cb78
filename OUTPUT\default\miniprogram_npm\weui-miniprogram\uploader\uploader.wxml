<view class="weui-uploader {{extClass}}">
    <view class="weui-uploader__hd">
        <view class="weui-uploader__overview">
            <view class="weui-uploader__title">{{title}}</view>
            <view class="weui-uploader__info" wx:if="{{maxCount>1}}">{{currentFiles.length}}/{{maxCount}}</view>
        </view>
        <view class="weui-uploader__tips" wx:if="{{tips}}">{{tips}}</view>
        <view wx:else>
            <slot name="tips"></slot>
        </view>
    </view>
    <view class="weui-uploader__bd">
        <view class="weui-uploader__files">
            <block wx:for="{{currentFiles}}" wx:key="*this">
                <view bindtap="previewImage" class="weui-uploader__file weui-uploader__file_status" data-index="{{index}}" wx:if="{{item.error}}">
                    <image class="weui-uploader__img" mode="aspectFill" src="{{item.url}}"></image>
                    <view class="weui-uploader__file-content">
                        <icon color="#F43530" size="23" type="warn"></icon>
                    </view>
                </view>
                <view bindtap="previewImage" class="weui-uploader__file weui-uploader__file_status" data-index="{{index}}" wx:elif="{{item.loading}}">
                    <image class="weui-uploader__img" mode="aspectFill" src="{{item.url}}"></image>
                    <view class="weui-uploader__file-content">
                        <view class="weui-loading"></view>
                    </view>
                </view>
                <view bindtap="previewImage" class="weui-uploader__file" data-index="{{index}}" wx:else>
                    <image class="weui-uploader__img" mode="aspectFill" src="{{item.url}}"></image>
                </view>
            </block>
        </view>
        <view class="weui-uploader__input-box" hoverClass="weui-active" wx:if="{{currentFiles.length<maxCount}}">
            <view bindtap="chooseImage" class="weui-uploader__input"></view>
        </view>
    </view>
</view>
<mp-gallery binddelete="deletePic" class="gallery" current="{{previewCurrent}}" hideOnClick="{{true}}" imgUrls="{{previewImageUrls}}" show="{{showPreview}}" showDelete="{{showDelete}}"></mp-gallery>
