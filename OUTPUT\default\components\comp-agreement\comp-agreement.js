(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["components/comp-agreement/comp-agreement"], {
    2587: function _(t, e, n) {


      n.r(e);
      var r = n("93710"),
        c = n("8a1a");
      for (var o in c)["default"].indexOf(o) < 0 && function(t) {
        n.d(e, t, function() {
          return c[t];
        });
      }(o);
      n("9e2c");
      var a = n("828b"),
        i = Object(a["a"])(c["default"], r["b"], r["c"], !1, null, "537724d3", null, !1, r["a"], void 0);
      e["default"] = i.exports;
    },
    "4f85": function f85(t, e, n) {


      var r = n("47a9");
      Object.defineProperty(e, "__esModule", {
        value: !0
      }), e.default = void 0;
      var c = r(n("7ca3")),
        o = n("8f59");

      function a(t, e) {
        var n = Object.keys(t);
        if (Object.getOwnPropertySymbols) {
          var r = Object.getOwnPropertySymbols(t);
          e && (r = r.filter(function(e) {
            return Object.getOwnPropertyDescriptor(t, e).enumerable;
          })), n.push.apply(n, r);
        }
        return n;
      }
      var i = {
        props: {
          show: {
            type: Boolean,
            default: !1
          },
          showCoocaa: {
            type: Boolean,
            default: !1
          }
        },
        data: function data() {
          return {
            activeTab: 1,
            tabsData: [{
              id: 1,
              name: "创维用户协议"
            }, {
              id: 2,
              name: "创维隐私政策"
            }]
          };
        },
        created: function created() {
          this.showCoocaa && this.tabsData.push({
            id: 3,
            name: "酷开服务条款"
          });
        },
        computed: function(t) {
          for (var e = 1; e < arguments.length; e++) {
            var n = null != arguments[e] ? arguments[e] : {};
            e % 2 ? a(Object(n), !0).forEach(function(e) {
              (0, c.default)(t, e, n[e]);
            }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(t, Object.getOwnPropertyDescriptors(n)) : a(Object(n)).forEach(function(e) {
              Object.defineProperty(t, e, Object.getOwnPropertyDescriptor(n, e));
            });
          }
          return t;
        }({}, (0, o.mapState)(["accountAgreement", "privacyAgreement", "coocaaAgreement"])),
        methods: {
          close: function close() {
            this.$emit("close");
          },
          switchTab: function switchTab(t) {
            this.activeTab = t.id;
          }
        }
      };
      e.default = i;
    },
    "7c5d": function c5d(t, e, n) {},
    "8a1a": function a1a(t, e, n) {


      n.r(e);
      var r = n("4f85"),
        c = n.n(r);
      for (var o in r)["default"].indexOf(o) < 0 && function(t) {
        n.d(e, t, function() {
          return r[t];
        });
      }(o);
      e["default"] = c.a;
    },
    93710: function _(t, e, n) {


      n.d(e, "b", function() {
        return c;
      }), n.d(e, "c", function() {
        return o;
      }), n.d(e, "a", function() {
        return r;
      });
      var r = {
          compIcon: function compIcon() {
            return n.e("components/comp-icon/comp-icon").then(n.bind(null, "84bb2"));
          }
        },
        c = function c() {
          var t = this.$createElement;
          this._self._c;
        },
        o = [];
    },
    "9e2c": function e2c(t, e, n) {


      var r = n("7c5d"),
        c = n.n(r);
      c.a;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['components/comp-agreement/comp-agreement-create-component', {
    'components/comp-agreement/comp-agreement-create-component': function componentsCompAgreementCompAgreementCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("2587"));
    }
  },
  [
    ['components/comp-agreement/comp-agreement-create-component']
  ]
]);