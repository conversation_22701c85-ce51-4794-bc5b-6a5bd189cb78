<comp-page bind:__l="__l" class="data-v-5f095cf5 vue-ref" data-ref="page" mainBackground="#f5f5f5" navbarColor="#fff" navbarIsBack="{{false}}" navbarIsTransparent="{{true}}" navbarTitle="服务直通车" vueId="651146d4-1" vueSlots="{{['default']}}">
    <view class="bg data-v-5f095cf5"></view>
    <card bind:__l="__l" class="data-v-5f095cf5" marginTop="-48rpx" title="自助预约" vueId="{{'651146d4-2'+','+'651146d4-1'}}" vueSlots="{{['default']}}">
        <card-buttons bind:__l="__l" buttons="{{prearrangeBtnList}}" class="data-v-5f095cf5" vueId="{{'651146d4-3'+','+'651146d4-2'}}"></card-buttons>
        <card-buttons bind:__l="__l" buttons="{{policyBtnList}}" class="data-v-5f095cf5" size="small" titlePosition="outside" vueId="{{'651146d4-4'+','+'651146d4-2'}}"></card-buttons>
    </card>
    <card bind:__l="__l" class="data-v-5f095cf5" title="自助服务" vueId="{{'651146d4-5'+','+'651146d4-1'}}" vueSlots="{{['default']}}">
        <card-buttons bind:__l="__l" buttons="{{serverBtnList}}" class="data-v-5f095cf5" columns="4" size="small" titlePosition="outside" vueId="{{'651146d4-6'+','+'651146d4-5'}}"></card-buttons>
    </card>
</comp-page>
