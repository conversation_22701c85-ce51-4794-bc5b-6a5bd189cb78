<view class="comp-packets-reward-wrap data-v-036706cd">
    <view class="main data-v-036706cd">
        <view class="hd flex-col col-center data-v-036706cd">
            <slot name="hd"></slot>
        </view>
        <view class="bd flex-col col-center data-v-036706cd">
            <slot name="bd" wx:if="{{$slots.bd}}"></slot>
            <block wx:else>
                <comp-button bind:__l="__l" bind:onClick="__e" class="data-v-036706cd" data-event-opts="{{[ [ '^onClick',[ ['clickBtn'] ] ] ]}}" openType="{{btn.type==='share'?'share':''}}" vueId="787b0f7c-1" vueSlots="{{['default']}}" wx:if="{{btn}}">
                    <view class="btn flex-row row-center col-center data-v-036706cd">{{''+btn.name+''}}</view>
                </comp-button>
            </block>
        </view>
        <view class="ft flex-col col-center data-v-036706cd">
            <slot name="ft"></slot>
        </view>
    </view>
</view>
