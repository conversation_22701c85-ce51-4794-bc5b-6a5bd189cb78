.comp-entry-tip.data-v-79ab5c1c {
    -webkit-animation: fadein .5s;
    animation: fadein .5s;
    background: rgba(0,0,0,.5);
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: var(--zindex-5)
}

.hand-tip.data-v-79ab5c1c {
    position: absolute;
    right: 90rpx
}

.main-tip.data-v-79ab5c1c {
    position: relative
}

.btn.data-v-79ab5c1c {
    bottom: 40rpx;
    height: 100rpx;
    left: 50%;
    position: absolute;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 480rpx
}
