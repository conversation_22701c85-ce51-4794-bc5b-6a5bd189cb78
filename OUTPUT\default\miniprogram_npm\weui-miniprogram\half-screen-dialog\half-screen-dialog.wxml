<view ariaModal="true" ariaRole="dialog" wx:if="{{wrapperShow}}">
    <view ariaLabel="关闭" ariaRole="button" bindtap="close" catch:touchmove="onMaskMouseMove" class="weui-mask {{innerShow?'weui-animate-fade-in':'weui-animate-fade-out'}}" data-type="tap" wx:if="{{mask}}"></view>
    <view catch:touchmove="onMaskMouseMove" class="weui-half-screen-dialog {{innerShow?'weui-animate-slide-up':'weui-animate-slide-down'}} {{extClass}}">
        <view class="weui-half-screen-dialog__hd">
            <view bindtap="close" class="weui-half-screen-dialog__hd__side" data-type="close" wx:if="{{closabled}}">
                <view ariaRole="button" class="weui-icon-btn" hoverClass="weui-active">关闭<i class="weui-icon-close-thin"></i>
                </view>
            </view>
            <view class="weui-half-screen-dialog__hd__main" tabindex="0">
                <block wx:if="{{title}}">
                    <text class="weui-half-screen-dialog__title">{{title}}</text>
                    <text class="weui-half-screen-dialog__subtitle">{{subTitle}}</text>
                </block>
                <view class="weui-half-screen-dialog__title" wx:else>
                    <slot name="title"></slot>
                </view>
            </view>
            <view class="weui-half-screen-dialog__hd__side">
                <view class="weui-icon-btn weui-icon-btn_more" hoverClass="weui-active">更多</view>
            </view>
        </view>
        <view class="weui-half-screen-dialog__bd">
            <block wx:if="{{desc}}">
                <view class="weui-half-screen-dialog__desc">{{desc}}</view>
                <view class="weui-half-screen-dialog__tips">{{tips}}</view>
            </block>
            <slot name="desc" wx:else></slot>
        </view>
        <view class="weui-half-screen-dialog__ft">
            <view class="weui-half-screen-dialog__btn-area" wx:if="{{buttons&&buttons.length}}">
                <button ariaRole="button" bindtap="buttonTap" class="weui-btn {{item.className}}" data-index="{{index}}" type="{{item.type}}" wx:for="{{buttons}}" wx:key="index">{{item.text}}</button>
            </view>
            <slot name="footer" wx:else></slot>
        </view>
    </view>
</view>
