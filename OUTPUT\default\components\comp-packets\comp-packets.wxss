.comp-packets.data-v-b5a4dddc {
    -webkit-animation: fadein .5s;
    animation: fadein .5s;
    background: rgba(0,0,0,.75);
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: var(--zindex-5)
}

.close.data-v-b5a4dddc {
    bottom: -104rpx;
    padding-top: 48rpx
}

.close.data-v-b5a4dddc,.close.data-v-b5a4dddc::after {
    left: 50%;
    position: absolute;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%)
}

.close.data-v-b5a4dddc::after {
    background-color: hsla(0,0%,100%,.79);
    content: "";
    height: 48rpx;
    top: 0;
    width: 3rpx
}
