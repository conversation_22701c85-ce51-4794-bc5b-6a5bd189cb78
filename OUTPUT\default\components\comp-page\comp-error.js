(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["components/comp-page/comp-error"], {
    "004a": function a(t, e, n) {


      var r = n("9c47"),
        o = n.n(r);
      o.a;
    },
    "24ec": function ec(t, e, n) {


      n.r(e);
      var r = n("a28b"),
        o = n.n(r);
      for (var u in r)["default"].indexOf(u) < 0 && function(t) {
        n.d(e, t, function() {
          return r[t];
        });
      }(u);
      e["default"] = o.a;
    },
    "6c42": function c42(t, e, n) {


      n.r(e);
      var r = n("9027"),
        o = n("24ec");
      for (var u in o)["default"].indexOf(u) < 0 && function(t) {
        n.d(e, t, function() {
          return o[t];
        });
      }(u);
      n("004a");
      var a = n("828b"),
        i = Object(a["a"])(o["default"], r["b"], r["c"], !1, null, "22dacf18", null, !1, r["a"], void 0);
      e["default"] = i.exports;
    },
    9027: function _(t, e, n) {


      n.d(e, "b", function() {
        return o;
      }), n.d(e, "c", function() {
        return u;
      }), n.d(e, "a", function() {
        return r;
      });
      var r = {
          compImage: function compImage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-image/comp-image")]).then(n.bind(null, "31bf"));
          },
          compButton: function compButton() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-button/comp-button")]).then(n.bind(null, "ca5a"));
          },
          compIcon: function compIcon() {
            return n.e("components/comp-icon/comp-icon").then(n.bind(null, "84bb2"));
          }
        },
        o = function o() {
          var t = this.$createElement;
          this._self._c;
        },
        u = [];
    },
    "9c47": function c47(t, e, n) {},
    a28b: function a28b(t, e, n) {


      var r = n("47a9");
      Object.defineProperty(e, "__esModule", {
        value: !0
      }), e.default = void 0;
      var o = r(n("a23b")),
        u = {
          props: {
            type: {
              type: String,
              default: o.default.ERROR_TYPE.DEFAULT
            },
            title: {
              type: String,
              default: ""
            },
            message: {
              type: String,
              default: ""
            },
            isRefresh: {
              type: Boolean,
              default: !1
            },
            top: {
              type: Number,
              default: 64
            }
          },
          computed: {
            errorImg: function errorImg() {
              return this.type === o.default.ERROR_TYPE.NETWORK ? "error-network" : this.type === o.default.ERROR_TYPE.NODATA ? "error-nodata" : this.type === o.default.ERROR_TYPE.NORECORD ? "error-norecord" : "error-app";
            },
            errorName: function errorName() {
              return this.title ? this.title : this.type === o.default.ERROR_TYPE.NETWORK ? "网络异常" : this.type === o.default.ERROR_TYPE.NORECORD ? "暂无记录" : this.type === o.default.ERROR_TYPE.NODATA ? "暂无数据" : this.type === o.default.ERROR_TYPE.DISABLED_INVITE ? "系统升级维护中" : "页面暂时无法访问";
            },
            errorSubname: function errorSubname() {
              return this.message ? this.message : this.type === o.default.ERROR_TYPE.NETWORK ? "请检查网络设置并再次刷新" : this.type === o.default.ERROR_TYPE.NORECORD || this.type === o.default.ERROR_TYPE.NODATA ? "" : "请再次刷新或返回";
            },
            errorIsRefresh: function errorIsRefresh() {
              return this.isRefresh ? this.isRefresh : this.type === o.default.ERROR_TYPE.NETWORK || this.type !== o.default.ERROR_TYPE.NORECORD && this.type !== o.default.ERROR_TYPE.NODATA;
            }
          },
          data: function data() {
            return {
              rotate: !1
            };
          },
          methods: {
            clickRefresh: function clickRefresh() {
              this.rotate = !0;
              var t = o.default.get(this, "$root.$mp.page.onLoad", null);
              this.rotate = !1, t && (o.default.log("CompBaseError: 触发重新刷新"), t());
            }
          }
        };
      e.default = u;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['components/comp-page/comp-error-create-component', {
    'components/comp-page/comp-error-create-component': function componentsCompPageCompErrorCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("6c42"));
    }
  },
  [
    ['components/comp-page/comp-error-create-component']
  ]
]);