require("../../@babel/runtime/helpers/Arrayincludes");
(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/discover/discover"], {
    2462: function _(t, e, n) {


      var a = n("50e6"),
        i = n.n(a);
      i.a;
    },
    4779: function _(t, e, n) {


      (function(t, e) {
        var a = n("47a9");
        n("5c38");
        a(n("3240"));
        var i = a(n("b2ff"));
        t.__webpack_require_UNI_MP_PLUGIN__ = n, e(i.default);
      }).call(this, n("3223")["default"], n("df3c")["createPage"]);
    },
    "50e6": function e6(t, e, n) {},
    "5a81": function a81(t, e, n) {},
    a48f: function a48f(t, e, n) {


      n.d(e, "b", function() {
        return i;
      }), n.d(e, "c", function() {
        return r;
      }), n.d(e, "a", function() {
        return a;
      });
      var a = {
          compPage: function compPage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-page/comp-page")]).then(n.bind(null, "fccb"));
          },
          compButton: function compButton() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-button/comp-button")]).then(n.bind(null, "ca5a"));
          },
          compIcon: function compIcon() {
            return n.e("components/comp-icon/comp-icon").then(n.bind(null, "84bb2"));
          },
          compEmpty: function compEmpty() {
            return n.e("components/comp-empty/comp-empty").then(n.bind(null, "ddc4"));
          }
        },
        i = function i() {
          var t = this,
            e = t.$createElement,
            n = (t._self._c, t.activityList.length),
            a = n ? t.textList.length : null,
            i = n ? t.__map(t.visibleItems, function(e, n) {
              var a = t.__get_orig(e),
                i = e.startTime.substring(0, 10),
                r = e.endTime.substring(0, 10);
              return {
                $orig: a,
                g2: i,
                g3: r
              };
            }) : null,
            r = t.infoData.list.length,
            o = t.infoData.list.length;
          t.$mp.data = Object.assign({}, {
            $root: {
              g0: n,
              g1: a,
              l0: i,
              g4: r,
              g5: o
            }
          });
        },
        r = [];
    },
    b2ff: function b2ff(t, e, n) {


      n.r(e);
      var a = n("a48f"),
        i = n("c369");
      for (var r in i)["default"].indexOf(r) < 0 && function(t) {
        n.d(e, t, function() {
          return i[t];
        });
      }(r);
      n("ceff"), n("2462");
      var o = n("828b"),
        c = Object(o["a"])(i["default"], a["b"], a["c"], !1, null, "c509e3c4", null, !1, a["a"], void 0);
      e["default"] = c.exports;
    },
    c369: function c369(t, e, n) {


      n.r(e);
      var a = n("d24d"),
        i = n.n(a);
      for (var r in a)["default"].indexOf(r) < 0 && function(t) {
        n.d(e, t, function() {
          return a[t];
        });
      }(r);
      e["default"] = i.a;
    },
    ceff: function ceff(t, e, n) {


      var a = n("5a81"),
        i = n.n(a);
      i.a;
    },
    d24d: function d24d(t, e, n) {


      (function(t) {
        var a = n("47a9");
        Object.defineProperty(e, "__esModule", {
          value: !0
        }), e.default = void 0;
        var i = a(n("7eb4")),
          r = a(n("af34")),
          o = a(n("ee10")),
          c = a(n("8b9c")),
          u = a(n("c148")),
          s = a(n("a23b")),
          l = a(n("5e82")),
          f = n("b3c5"),
          d = (0, f.getConfig)(),
          p = d.appId,
          g = {
            components: {
              PageHeader: function PageHeader() {
                n.e("pages/discover/page-header").then(function() {
                  return resolve(n("21ea"));
                }.bind(null, n)).catch(n.oe);
              },
              PageAd: function PageAd() {
                n.e("pages/discover/page-ad").then(function() {
                  return resolve(n("5589"));
                }.bind(null, n)).catch(n.oe);
              },
              CompPicRow: function CompPicRow() {
                n.e("pages/discover/comp-pic-row").then(function() {
                  return resolve(n("6894"));
                }.bind(null, n)).catch(n.oe);
              },
              CompClub: function CompClub() {
                n.e("pages/discover/comp-club").then(function() {
                  return resolve(n("95fc"));
                }.bind(null, n)).catch(n.oe);
              },
              CompArticleList: function CompArticleList() {
                n.e("pages/discover/comp-article-list").then(function() {
                  return resolve(n("6182"));
                }.bind(null, n)).catch(n.oe);
              }
            },
            data: function data() {
              return {
                textList: [],
                activityList: [],
                clubList: [],
                exploreList: [],
                subjectList: [],
                headerData: {
                  banner: []
                },
                goodsData: {
                  moreTokenUrl: "",
                  list: []
                },
                adData: {
                  list: []
                },
                infoData: {
                  banner: [],
                  list: [],
                  page: 1,
                  loading: !1,
                  isEnd: !1,
                  total: 0
                },
                subjectActive: 0,
                subjectItem: {},
                czz: ""
              };
            },
            computed: {
              visibleItems: function visibleItems() {
                return this.activityList.slice(0, 3);
              },
              leftColumn: function leftColumn() {
                return this.infoData.list.filter(function(t, e) {
                  return e % 2 === 0;
                });
              },
              rightColumn: function rightColumn() {
                return this.infoData.list.filter(function(t, e) {
                  return e % 2 === 1;
                });
              }
            },
            onReachBottom: function onReachBottom() {
              this.getInfo(!0);
            },
            onShareAppMessage: function onShareAppMessage() {
              return s.default.generateShareInfo();
            },
            onShow: function onShow() {
              t.getStorageSync("czz") && (this.czz = t.getStorageSync("czz"), t.removeStorageSync("czz"), t.showToast({
                icon: "none",
                title: "在种草社区中，浏览任意一条带标题笔记10秒以上可赚取成长值",
                duration: 3e3
              }));
            },
            onLoad: function onLoad() {
              this.getWinningMessages();
            },
            methods: {
              clickMore: function clickMore() {
                l.default.to({
                  page: "activityCenter"
                });
              },
              fetch: function fetch() {
                return Promise.all([this.getHeaderBanner(), this.getSubjectList(), this.getActList()]);
              },
              clickAct: function clickAct(e) {
                if (3 == e.status) return t.showToast({
                  icon: "none",
                  title: "活动已结束"
                });
                1 == e.jumpType ? ["wxff438d3c60c63fb6", "wxa1d1df2295fbe77e"].includes(e.miniProgramId) ? t.navigateTo({
                  url: e.miniProgramUrl
                }) : t.navigateToMiniProgram({
                  appId: e.miniProgramId,
                  path: e.miniProgramUrl,
                  success: function success() {
                    console.log("Navigate: toMp success");
                  },
                  fail: function fail(t) {
                    console.log("Navigate: toMp fail", t);
                  }
                }) : 2 == e.jumpType && l.default.to({
                  page: "webviewH5",
                  query: {
                    url: encodeURIComponent(e.miniProgramUrl)
                  }
                });
              },
              clickMemberTask: function clickMemberTask() {
                l.default.to({
                  fullPath: "/pages-picture/picture/page-add-pic?activeTab=1"
                });
              },
              slectSubject: function slectSubject(t, e) {
                this.subjectActive = e, this.subjectItem = t, this.infoData.page = 1, this.infoData.isEnd = !1, this.getInfo();
              },
              toDetail: function toDetail(t) {
                if (t.title) {
                  var e = {
                    id: t.id
                  };
                  this.czz && (e.czz = this.czz), l.default.to({
                    page: "pictureDetail",
                    query: e
                  });
                } else this.preview(t.images, t.images[0]);
              },
              preview: function preview(e, n) {
                t.previewImage({
                  urls: e,
                  current: n
                });
              },
              getWinningMessages: function getWinningMessages() {
                var t = this;
                return (0, o.default)(i.default.mark(function e() {
                  var n, a;
                  return i.default.wrap(function(e) {
                    while (1) switch (e.prev = e.next) {
                      case 0:
                        return e.next = 2, u.default.getWinningMessages();
                      case 2:
                        n = e.sent, 200 == n.code && (a = n.data.map(function(t) {
                          return {
                            value: t.split("获得")[0] + "获得",
                            name: t.split("获得")[1]
                          };
                        }), t.textList = t.shuffleArray(a), console.log(t.textList[0]));
                      case 4:
                      case "end":
                        return e.stop();
                    }
                  }, e);
                }))();
              },
              shuffleArray: function shuffleArray(t) {
                for (var e = (0, r.default)(t), n = e.length - 1; n > 0; n--) {
                  var a = Math.floor(Math.random() * (n + 1)),
                    i = [e[a], e[n]];
                  e[n] = i[0], e[a] = i[1];
                }
                return e;
              },
              getActList: function getActList() {
                var t = this;
                return (0, o.default)(i.default.mark(function e() {
                  var n, a, r;
                  return i.default.wrap(function(e) {
                    while (1) switch (e.prev = e.next) {
                      case 0:
                        return n = {
                          pageNum: 1,
                          pageSize: 20,
                          miniProgramId: p,
                          status: 2
                        }, e.next = 3, u.default.queryMiniActList(n);
                      case 3:
                        a = e.sent, r = s.default.get(a, "data.data.records", []) || [], t.activityList = r;
                      case 6:
                      case "end":
                        return e.stop();
                    }
                  }, e);
                }))();
              },
              getHeaderBanner: function getHeaderBanner() {
                var t = this;
                return (0, o.default)(i.default.mark(function e() {
                  var n;
                  return i.default.wrap(function(e) {
                    while (1) switch (e.prev = e.next) {
                      case 0:
                        return e.next = 2, c.default.banner({
                          position: "bndiscovertop"
                        });
                      case 2:
                        n = e.sent, t.headerData.banner = s.default.get(n, "data.data", []);
                      case 4:
                      case "end":
                        return e.stop();
                    }
                  }, e);
                }))();
              },
              toActiviey: function toActiviey(t) {
                1 == t ? l.default.to({
                  page: "templateArticle",
                  query: {
                    id: 58,
                    title: "探家大揭秘",
                    articleType: 2,
                    columnLabel: "visited-reveal-secrets"
                  }
                }) : 2 == t ? l.default.to({
                  page: "templateArticle",
                  query: {
                    id: 57,
                    title: "维粉故事汇",
                    articleType: 2,
                    columnLabel: "weifan-story-collection"
                  }
                }) : l.default.to({
                  page: "templateArticle",
                  query: {
                    id: 70,
                    title: "创维新闻",
                    articleType: 2,
                    columnLabel: "Skyworth News"
                  }
                });
              },
              getAd: function getAd() {
                var t = this;
                return (0, o.default)(i.default.mark(function e() {
                  var n;
                  return i.default.wrap(function(e) {
                    while (1) switch (e.prev = e.next) {
                      case 0:
                        return e.next = 2, c.default.banner({
                          position: "bndiscovermid"
                        });
                      case 2:
                        n = e.sent, t.adData.list = s.default.get(n, "data.data", []);
                      case 4:
                      case "end":
                        return e.stop();
                    }
                  }, e);
                }))();
              },
              discoverPageData: function discoverPageData() {
                var t = this;
                return (0, o.default)(i.default.mark(function e() {
                  var n;
                  return i.default.wrap(function(e) {
                    while (1) switch (e.prev = e.next) {
                      case 0:
                        return e.next = 2, u.default.discoverPageData({});
                      case 2:
                        n = e.sent, t.clubList = s.default.get(n, "data.data.clubList", []), t.exploreList = s.default.get(n, "data.data.exploreList", []);
                      case 5:
                      case "end":
                        return e.stop();
                    }
                  }, e);
                }))();
              },
              getSubjectList: function getSubjectList() {
                var t = this;
                return (0, o.default)(i.default.mark(function e() {
                  var n;
                  return i.default.wrap(function(e) {
                    while (1) switch (e.prev = e.next) {
                      case 0:
                        return e.next = 2, u.default.getSubkectList({});
                      case 2:
                        n = e.sent, t.subjectList = s.default.get(n, "data", []), t.subjectItem = t.subjectList[0], t.getInfo();
                      case 6:
                      case "end":
                        return e.stop();
                    }
                  }, e);
                }))();
              },
              getInfo: function getInfo() {
                var t = arguments,
                  e = this;
                return (0, o.default)(i.default.mark(function n() {
                  var a, r, o;
                  return i.default.wrap(function(n) {
                    while (1) switch (n.prev = n.next) {
                      case 0:
                        if (a = t.length > 0 && void 0 !== t[0] && t[0], !e.infoData.isEnd) {
                          n.next = 3;
                          break;
                        }
                        return n.abrupt("return");
                      case 3:
                        return e.infoData.loading = !0, a && !e.infoData.isEnd && (e.infoData.page = e.infoData.page + 1), n.next = 7, u.default.getPictureListP({
                          pageNum: e.infoData.page,
                          pageSize: 1 === e.infoData.page ? 20 : 30,
                          subjectId: e.subjectItem.id
                        });
                      case 7:
                        r = n.sent, o = s.default.get(r, "data.records", []), e.infoData.loading = !1, e.infoData.list = 1 === e.infoData.page ? o : e.infoData.list.concat(o), e.infoData.total = s.default.get(r, "data.total", 0), e.infoData.isEnd = e.infoData.list.length >= e.infoData.total;
                      case 13:
                      case "end":
                        return n.stop();
                    }
                  }, n);
                }))();
              },
              getGoods: function getGoods() {
                var t = this;
                return (0, o.default)(i.default.mark(function e() {
                  var n;
                  return i.default.wrap(function(e) {
                    while (1) switch (e.prev = e.next) {
                      case 0:
                        return e.next = 2, c.default.choiceGoodsList();
                      case 2:
                        n = e.sent, t.goodsData.moreTokenUrl = s.default.get(n, "data.data.linkUrl", ""), t.goodsData.list = s.default.get(n, "data.data.list", []);
                      case 5:
                      case "end":
                        return e.stop();
                    }
                  }, e);
                }))();
              },
              getInfoBanner: function getInfoBanner() {
                var t = this;
                return (0, o.default)(i.default.mark(function e() {
                  var n;
                  return i.default.wrap(function(e) {
                    while (1) switch (e.prev = e.next) {
                      case 0:
                        return e.next = 2, c.default.choiceArthotList();
                      case 2:
                        n = e.sent, t.infoData.banner = s.default.get(n, "data.data", []);
                      case 4:
                      case "end":
                        return e.stop();
                    }
                  }, e);
                }))();
              }
            }
          };
        e.default = g;
      }).call(this, n("df3c")["default"]);
    }
  },
  [
    ["4779", "common/runtime", "common/vendor"]
  ]
]);