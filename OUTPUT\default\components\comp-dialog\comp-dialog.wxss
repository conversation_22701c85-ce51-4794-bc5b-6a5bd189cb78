.comp-dialog.data-v-fcfa84a4 {
    -webkit-animation: fadein .5s;
    animation: fadein .5s;
    background: rgba(0,0,0,.75);
    bottom: 0;
    left: 0;
    padding: 0 55rpx;
    position: fixed;
    right: 0;
    top: 0;
    z-index: var(--zindex-5)
}

.main.data-v-fcfa84a4 {
    background: url(https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/education-sign-dialog-bg.png);
    background-color: var(--color-fill-grey-inverse);
    background-repeat: no-repeat;
    background-size: 100% auto;
    border-radius: 24rpx;
    padding: 72rpx 64rpx 40rpx;
    width: 100%
}

.icon.data-v-fcfa84a4 {
    padding-bottom: 34rpx
}

.name.data-v-fcfa84a4 {
    color: #343434;
    font-size: 54rpx;
    font-weight: var(--font-weight-bold);
    line-height: 75rpx;
    text-align: center
}

.subname.data-v-fcfa84a4 {
    color: #343434;
    font-size: 36rpx;
    line-height: 50rpx;
    padding-top: 18rpx;
    text-align: center
}

.ft.data-v-fcfa84a4 {
    padding-top: 56rpx
}

.btn.data-v-fcfa84a4 {
    background: #f3f4f7;
    border-radius: 8rpx;
    color: #1d1d1d;
    font-size: 32rpx;
    height: 96rpx
}

.btn__wrap.is-primary .btn.data-v-fcfa84a4 {
    background: var(--color-brand-1);
    color: var(--color-fill-grey-inverse)
}

.is-horizontal .ft.data-v-fcfa84a4 {
    display: -webkit-flex;
    display: flex
}

.is-horizontal .btn__wrap.data-v-fcfa84a4 {
    padding-right: 8rpx
}

.is-horizontal .btn__wrap.is-ext.data-v-fcfa84a4 {
    padding-left: 8rpx;
    padding-right: 0
}

.is-vertical .btn__wrap.is-ext.data-v-fcfa84a4 {
    margin-top: 16rpx
}
