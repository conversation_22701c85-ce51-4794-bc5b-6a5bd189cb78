(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["common/main"], {
    "1d40": function d40(e, t, r) {


      r.r(t);
      var n = r("d0fe");
      for (var a in n)["default"].indexOf(a) < 0 && function(e) {
        r.d(t, e, function() {
          return n[e];
        });
      }(a);
      r("f328");
      var i = r("828b"),
        c = Object(i["a"])(n["default"], void 0, void 0, !1, null, null, null, !1, void 0, void 0);
      t["default"] = c.exports;
    },
    "67f1": function f1(e, t, r) {


      (function(e) {
        var n = r("47a9");
        Object.defineProperty(t, "__esModule", {
          value: !0
        }), t.default = void 0;
        var a = n(r("7eb4")),
          i = n(r("34cf")),
          c = n(r("ee10")),
          o = n(r("7ca3")),
          u = r("8f59"),
          f = n(r("bdc5")),
          l = n(r("5e82")),
          d = n(r("a23b")),
          p = r("b3c5");

        function s(e, t) {
          var r = Object.keys(e);
          if (Object.getOwnPropertySymbols) {
            var n = Object.getOwnPropertySymbols(e);
            t && (n = n.filter(function(t) {
              return Object.getOwnPropertyDescriptor(e, t).enumerable;
            })), r.push.apply(r, n);
          }
          return r;
        }

        function b(e) {
          for (var t = 1; t < arguments.length; t++) {
            var r = null != arguments[t] ? arguments[t] : {};
            t % 2 ? s(Object(r), !0).forEach(function(t) {
              (0, o.default)(e, t, r[t]);
            }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(r)) : s(Object(r)).forEach(function(t) {
              Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(r, t));
            });
          }
          return e;
        }
        var g = (0, p.getConfig)(),
          v = (d.default.getAccountInfo(), {
            computed: b({}, (0, u.mapState)(["token", "profile", "isEntryTip"])),
            watch: {
              profile: {
                deep: !0,
                handler: function handler(e, t) {
                  null === t && Object.keys(e).length > 0 && (d.default.log("App: watch profile 触发检查提示弹窗"), this.checkIsEntryTip());
                }
              }
            },
            data: function data() {
              return {
                appHide: !1,
                timer: null
              };
            },
            methods: b(b(b({}, (0, u.mapMutations)(["assign", "detectSf"])), (0, u.mapActions)(["getNavbarHeight"])), {}, {
              checkIsEntryTip: function checkIsEntryTip() {
                var t = this;
                return (0, c.default)(a.default.mark(function r() {
                  var n, c, o, u;
                  return a.default.wrap(function(r) {
                    while (1) switch (r.prev = r.next) {
                      case 0:
                        if (d.default.log("App: checkIsEntryTip 检查添加小程序提示弹窗"), t.token && t.profile) {
                          r.next = 4;
                          break;
                        }
                        return d.default.log("App: checkIsEntryTip， 未注册用户不进行弹窗", t.token, t.profile), r.abrupt("return");
                      case 4:
                        if (t.profile.todayScore) {
                          r.next = 7;
                          break;
                        }
                        return d.default.log("App: checkIsEntryTip， 今日无加维豆不进行弹窗", t.profile.todayScore), r.abrupt("return");
                      case 7:
                        return r.next = 9, e.getStorage({
                          key: "entryTip"
                        });
                      case 9:
                        if (n = r.sent, c = (0, i.default)(n, 2), o = c[1], u = d.default.get(o, "data.lastTime", ""), !u || !d.default.dayjs(u).isToday()) {
                          r.next = 16;
                          break;
                        }
                        return d.default.log("App: checkIsEntryTip 今日提示过不再进行弹窗提示"), r.abrupt("return");
                      case 16:
                        clearTimeout(t.timer), t.timer = setTimeout(function() {
                          t.assign({
                            isEntryTip: !0
                          });
                        }, 3e3);
                      case 18:
                      case "end":
                        return r.stop();
                    }
                  }, r);
                }))();
              },
              checkIsWebviewRedirectIndex: function checkIsWebviewRedirectIndex() {
                if (this.appHide) {
                  var e = d.default.getRoute();
                  if (e) {
                    var t = e.route.indexOf("/webview/webview") > -1;
                    if (t) {
                      var r = decodeURIComponent(e.options.url || "");
                      r ? -1 !== r.indexOf("duiba.com") ? l.default.to({
                        page: "index"
                      }) : d.default.log("App checkIsWebviewRedirectIndex webview url 不是兑吧链接") : d.default.log("App checkIsWebviewRedirectIndex webview 没有 url 参数");
                    } else d.default.log("App checkIsWebviewRedirectIndex 当前路径不是 webview");
                  } else d.default.log("App checkIsWebviewRedirectIndex 获取不到 route");
                } else d.default.log("App checkIsWebviewRedirectIndex appHide 为 false");
              }
            }),
            onLaunch: function onLaunch(t) {
              var r = this;
              return (0, c.default)(a.default.mark(function n() {
                return a.default.wrap(function(n) {
                  while (1) switch (n.prev = n.next) {
                    case 0:
                      if (d.default.log("App Launch", t), d.default.log("当前配置为: ", "prod" === g.env ? "正式环境配置" : "测试环境配置", g.env), r.detectSf(t.query), 1023 !== t.scene) {
                        n.next = 6;
                        break;
                      }
                      return n.next = 6, e.setStorage({
                        key: "entryTip",
                        data: {
                          lastTime: Number(new Date())
                        }
                      });
                    case 6:
                      f.default.getAgreement(), f.default.getProvilege(), f.default.getRegionData(), f.default.getStreetData();
                    case 10:
                    case "end":
                      return n.stop();
                  }
                }, n);
              }))();
            },
            onShow: function onShow() {
              d.default.log("App Show"), this.checkIsWebviewRedirectIndex(), this.appHide = !1, f.default.checkUpdate(), this.getNavbarHeight();
            },
            onHide: function onHide() {
              d.default.log("App Hide"), this.appHide = !0;
            }
          });
        t.default = v;
      }).call(this, r("df3c")["default"]);
    },
    d0fe: function d0fe(e, t, r) {


      r.r(t);
      var n = r("67f1"),
        a = r.n(n);
      for (var i in n)["default"].indexOf(i) < 0 && function(e) {
        r.d(t, e, function() {
          return n[e];
        });
      }(i);
      t["default"] = a.a;
    },
    d142: function d142(e, t, r) {


      (function(e, t, n) {
        var a = r("47a9"),
          i = a(r("7ca3"));
        r("5c38");
        var c = a(r("3240")),
          o = a(r("1d40")),
          u = a(r("66c7")),
          f = a(r("3e44")),
          l = r("b3c5");

        function d(e, t) {
          var r = Object.keys(e);
          if (Object.getOwnPropertySymbols) {
            var n = Object.getOwnPropertySymbols(e);
            t && (n = n.filter(function(t) {
              return Object.getOwnPropertyDescriptor(e, t).enumerable;
            })), r.push.apply(r, n);
          }
          return r;
        }
        e.__webpack_require_UNI_MP_PLUGIN__ = r;
        var p = (0, l.getConfig)();
        t.getSystemInfoSync();
        c.default.config.productionTip = !1, c.default.prototype.$store = u.default, c.default.prototype.$config = p, c.default.mixin(f.default.page), o.default.mpType = "app";
        var s = new c.default(function(e) {
          for (var t = 1; t < arguments.length; t++) {
            var r = null != arguments[t] ? arguments[t] : {};
            t % 2 ? d(Object(r), !0).forEach(function(t) {
              (0, i.default)(e, t, r[t]);
            }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(r)) : d(Object(r)).forEach(function(t) {
              Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(r, t));
            });
          }
          return e;
        }({
          store: u.default
        }, o.default));
        n(s).$mount();
      }).call(this, r("3223")["default"], r("df3c")["default"], r("df3c")["createApp"]);
    },
    f328: function f328(e, t, r) {


      var n = r("fb47"),
        a = r.n(n);
      a.a;
    },
    fb47: function fb47(e, t, r) {}
  },
  [
    ["d142", "common/runtime", "common/vendor"]
  ]
]);