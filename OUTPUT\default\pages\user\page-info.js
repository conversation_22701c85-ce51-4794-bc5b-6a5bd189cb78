(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/user/page-info"], {
    "0a7e": function a7e(e, t, n) {


      n.r(t);
      var r = n("fe58"),
        a = n.n(r);
      for (var o in r)["default"].indexOf(o) < 0 && function(e) {
        n.d(t, e, function() {
          return r[e];
        });
      }(o);
      t["default"] = a.a;
    },
    "18b1": function b1(e, t, n) {


      var r = n("3290"),
        a = n.n(r);
      a.a;
    },
    3290: function _(e, t, n) {},
    "766f": function f(e, t, n) {


      n.r(t);
      var r = n("fbd8"),
        a = n("0a7e");
      for (var o in a)["default"].indexOf(o) < 0 && function(e) {
        n.d(t, e, function() {
          return a[e];
        });
      }(o);
      n("18b1");
      var i = n("828b"),
        c = Object(i["a"])(a["default"], r["b"], r["c"], !1, null, "67ab8b30", null, !1, r["a"], void 0);
      t["default"] = c.exports;
    },
    fbd8: function fbd8(e, t, n) {


      n.d(t, "b", function() {
        return a;
      }), n.d(t, "c", function() {
        return o;
      }), n.d(t, "a", function() {
        return r;
      });
      var r = {
          compButton: function compButton() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-button/comp-button")]).then(n.bind(null, "ca5a"));
          },
          compImage: function compImage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-image/comp-image")]).then(n.bind(null, "31bf"));
          },
          compIcon: function compIcon() {
            return n.e("components/comp-icon/comp-icon").then(n.bind(null, "84bb2"));
          }
        },
        a = function a() {
          var e = this.$createElement,
            t = (this._self._c, this.token ? null : this.rgba(0, 0, 0, .3));
          this.$mp.data = Object.assign({}, {
            $root: {
              m0: t
            }
          });
        },
        o = [];
    },
    fe58: function fe58(e, t, n) {


      (function(e) {
        var r = n("47a9");
        Object.defineProperty(t, "__esModule", {
          value: !0
        }), t.default = void 0;
        var a = r(n("7eb4")),
          o = r(n("ee10")),
          i = r(n("7ca3")),
          c = n("8f59"),
          u = r(n("8b9c")),
          s = r(n("a23b")),
          f = r(n("5e82")),
          l = n("b3c5");

        function d(e, t) {
          var n = Object.keys(e);
          if (Object.getOwnPropertySymbols) {
            var r = Object.getOwnPropertySymbols(e);
            t && (r = r.filter(function(t) {
              return Object.getOwnPropertyDescriptor(e, t).enumerable;
            })), n.push.apply(n, r);
          }
          return n;
        }

        function g(e) {
          for (var t = 1; t < arguments.length; t++) {
            var n = null != arguments[t] ? arguments[t] : {};
            t % 2 ? d(Object(n), !0).forEach(function(t) {
              (0, i.default)(e, t, n[t]);
            }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : d(Object(n)).forEach(function(t) {
              Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
            });
          }
          return e;
        }
        var p = (0, l.getConfig)(),
          m = {
            props: {
              income: {
                type: Number,
                default: function _default() {
                  return 0;
                }
              },
              rightNum: {
                type: Number,
                default: function _default() {
                  return 0;
                }
              },
              couponNum: {
                type: [Number, String],
                default: 0
              }
            },
            watch: {
              profile: {
                deep: !0,
                handler: function handler(e) {
                  e ? (this.nickName = e.nickName, this.avatar = "".concat(e.avatar), this.gradeImg = e.gradeImg, this.gradeName = e.gradeName, this.grade = e.grade, this.score = e.score, this.growth = e.growth, this.gradeMax = e.gradeMax, this.barWidth = 5 == e.grade ? 510 : e.growth / e.gradeMax * 510) : this.islogin = !1;
                }
              }
            },
            data: function data() {
              return {
                nickName: "点击登录/注册",
                avatar: "".concat(p.oss).concat(p.staticPath, "/avatar-default.png"),
                gradeImg: "",
                gradeName: "",
                growth: 0,
                gradeMax: 0,
                barWidth: 0,
                grade: 1,
                score: 0,
                signTokenUrl: "",
                islogin: !0
              };
            },
            computed: g({}, (0, c.mapState)(["token", "profile", "navbarHeight"])),
            created: function created() {
              this.getIndexNav(), this.profile || (this.islogin = !1);
            },
            methods: g(g({}, (0, c.mapMutations)(["assign"])), {}, {
              getIndexNav: function getIndexNav() {
                var e = this;
                return (0, o.default)(a.default.mark(function t() {
                  var n;
                  return a.default.wrap(function(t) {
                    while (1) switch (t.prev = t.next) {
                      case 0:
                        return t.next = 2, u.default.indexNav();
                      case 2:
                        n = t.sent, e.signTokenUrl = s.default.get(n, "data.data.signTokenUrl", "");
                      case 4:
                      case "end":
                        return t.stop();
                    }
                  }, t);
                }))();
              },
              clickSign: function clickSign() {
                f.default.toWebviewByTokenUrl({
                  tokenUrl: this.signTokenUrl
                });
              },
              clickSetting: function clickSetting() {
                this.profile && f.default.to({
                  page: "userSetting",
                  query: {
                    from: "setting"
                  }
                });
              },
              myincomeClick: function myincomeClick() {
                f.default.to({
                  page: "myIncome"
                });
              },
              couponClick: function couponClick() {
                f.default.to({
                  page: "userScore"
                });
              },
              delUser: function delUser() {
                var t = this;
                return (0, o.default)(a.default.mark(function n() {
                  var r;
                  return a.default.wrap(function(n) {
                    while (1) switch (n.prev = n.next) {
                      case 0:
                        if ("prod" !== p.env) {
                          n.next = 2;
                          break;
                        }
                        return n.abrupt("return");
                      case 2:
                        return n.next = 4, u.default.delUser();
                      case 4:
                        r = n.sent, t.assign({
                          guaranteeToken: ""
                        }), e.showToast({
                          icon: "none",
                          title: JSON.stringify(s.default.get(r, "data", {}))
                        });
                      case 7:
                      case "end":
                        return n.stop();
                    }
                  }, n);
                }))();
              },
              clickMember: function clickMember() {
                f.default.to({
                  page: "member"
                });
              }
            })
          };
        t.default = m;
      }).call(this, n("df3c")["default"]);
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/user/page-info-create-component', {
    'pages/user/page-info-create-component': function pagesUserPageInfoCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("766f"));
    }
  },
  [
    ['pages/user/page-info-create-component']
  ]
]);