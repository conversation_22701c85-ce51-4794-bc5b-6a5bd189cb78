.card-buttons.data-v-32a38080 {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    gap: 16rpx;
    margin-top: 24rpx
}

.card-btn.data-v-32a38080 {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    height: 208rpx;
    -webkit-justify-content: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
    text-align: center
}

.card-btn.small.data-v-32a38080 {
    display: block;
    height: 180rpx;
    padding-top: 24rpx;
    text-align: center
}

.btn-title.data-v-32a38080 {
    color: rgba(0,0,0,.85);
    font-size: 24rpx;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.card-btn.small .btn-title.data-v-32a38080 {
    line-height: 1.4;
    margin-top: 8rpx
}

.card-btn.small .btn-title.data-v-32a38080:first-of-type {
    margin-top: 12rpx
}
