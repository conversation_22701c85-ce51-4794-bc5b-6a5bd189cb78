<view class="comp-tab flex-row data-v-e8ab23a0">
    <scroll-view class="tab-wrap flex-one data-v-e8ab23a0" scrollIntoView="{{'tab-'+view}}" scrollWithAnimation="{{true}}" scrollX="{{true}}">
        <view class="tab data-v-e8ab23a0" style="{{'padding-right:'+btnWidth+'px'+';'}}">
            <view bindtap="__e" class="{{['tab__item','data-v-e8ab23a0',active===index?'is-active':'',index===0?'is-first':'']}}" data-event-opts="{{[ [ 'tap',[ [ 'select',['$0',index],[ [ ['tabs','',index] ] ] ] ] ] ]}}" id="{{'tab-'+index}}" wx:for="{{tabs}}" wx:key="index">{{''+item.title+''}}</view>
        </view>
    </scroll-view>
    <view class="btn flex-row col-center data-v-e8ab23a0">
        <slot name="btn"></slot>
    </view>
</view>
