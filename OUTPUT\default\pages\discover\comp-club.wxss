.pic-row.data-v-3937ab41 {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    margin: 0 auto;
    width: 702rpx
}

.pic-row .card.data-v-3937ab41 {
    background-color: #f5f5f5;
    background-size: cover;
    border-radius: 24rpx;
    color: #002d29;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    height: 156rpx;
    width: 339rpx
}

.pic-row .card .name.data-v-3937ab41 {
    font-family: PingFang-SC-Medium,PingFang-SC;
    font-size: 28rpx;
    font-weight: 500;
    margin: 24rpx 0 0 32rpx
}

.pic-row .marb.data-v-3937ab41 {
    margin-bottom: 24rpx
}

.pic-row .color1.data-v-3937ab41 {
    color: #002d29
}

.pic-row .color2.data-v-3937ab41 {
    color: #100040
}

.pic-row .color3.data-v-3937ab41 {
    color: #00254d
}

.pic-row .color4.data-v-3937ab41 {
    color: #4f0010
}
