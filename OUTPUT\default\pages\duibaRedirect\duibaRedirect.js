(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/duibaRedirect/duibaRedirect"], {
    "798c": function c(e, t, n) {


      n.d(t, "b", function() {
        return u;
      }), n.d(t, "c", function() {
        return c;
      }), n.d(t, "a", function() {});
      var u = function u() {
          var e = this.$createElement;
          this._self._c;
        },
        c = [];
    },
    "82a3": function a3(e, t, n) {


      (function(e) {
        Object.defineProperty(t, "__esModule", {
          value: !0
        }), t.default = void 0;
        var n = {
          data: function data() {
            return {
              url: ""
            };
          },
          onLoad: function onLoad() {
            var t = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
              n = t.redirect;
            this.url = n ? decodeURIComponent(n) : "", e.hideShareMenu();
          }
        };
        t.default = n;
      }).call(this, n("df3c")["default"]);
    },
    "9cfc": function cfc(e, t, n) {


      n.r(t);
      var u = n("82a3"),
        c = n.n(u);
      for (var a in u)["default"].indexOf(a) < 0 && function(e) {
        n.d(t, e, function() {
          return u[e];
        });
      }(a);
      t["default"] = c.a;
    },
    "9dd8": function dd8(e, t, n) {


      (function(e, t) {
        var u = n("47a9");
        n("5c38");
        u(n("3240"));
        var c = u(n("a3e0"));
        e.__webpack_require_UNI_MP_PLUGIN__ = n, t(c.default);
      }).call(this, n("3223")["default"], n("df3c")["createPage"]);
    },
    a3e0: function a3e0(e, t, n) {


      n.r(t);
      var u = n("798c"),
        c = n("9cfc");
      for (var a in c)["default"].indexOf(a) < 0 && function(e) {
        n.d(t, e, function() {
          return c[e];
        });
      }(a);
      var r = n("828b"),
        i = Object(r["a"])(c["default"], u["b"], u["c"], !1, null, null, null, !1, u["a"], void 0);
      t["default"] = i.exports;
    }
  },
  [
    ["9dd8", "common/runtime", "common/vendor"]
  ]
]);