<view class="page-info data-v-67ab8b30" style="{{'padding-top:'+navbarHeight+'px'+';'}}">
    <view class="page-info__main flex-row data-v-67ab8b30">
        <comp-button bind:__l="__l" bind:getUserInfo="__e" class="avatar-wrap data-v-67ab8b30" data-event-opts="{{[ [ '^getUserInfo',[ ['clickSetting'] ] ] ]}}" openType="getUserInfo" vueId="109ab485-1" vueSlots="{{['default']}}">
            <comp-image bind:__l="__l" bind:longpress="__e" borderColor="#fff" borderWidth="3rpx" class="main__avatar data-v-67ab8b30" data-event-opts="{{[ [ '^longpress',[ ['delUser'] ] ] ]}}" height="120rpx" isBorder="{{true}}" isServer="{{true}}" name="{{avatar}}" radius="50%" vueId="{{'109ab485-2'+','+'109ab485-1'}}" width="120rpx"></comp-image>
            <comp-image bind:__l="__l" class="main__set data-v-67ab8b30" height="44rpx" name="icon_bianji" vueId="{{'109ab485-3'+','+'109ab485-1'}}" width="44rpx"></comp-image>
        </comp-button>
        <view class="main__hd flex-one top-name data-v-67ab8b30">
            <view class="flex-one flex-row col-center data-v-67ab8b30">
                <comp-button bind:__l="__l" class="data-v-67ab8b30" openType="getUserInfo" vueId="109ab485-4" vueSlots="{{['default']}}">
                    <view class="main__name data-v-67ab8b30">{{nickName||''}}</view>
                </comp-button>
            </view>
        </view>
        <comp-button bind:__l="__l" bind:getUserInfo="__e" class="signClass data-v-67ab8b30" data-event-opts="{{[ [ '^getUserInfo',[ ['clickSign'] ] ] ]}}" openType="getUserInfo" vueId="109ab485-5" vueSlots="{{['default']}}">
            <view class="page-header__sign flex-row col-center row-center data-v-67ab8b30">
                <comp-image bind:__l="__l" class="data-v-67ab8b30" height="33rpx" name="home-coin" vueId="{{'109ab485-6'+','+'109ab485-5'}}" width="33rpx"></comp-image>
                <view class="sign__name data-v-67ab8b30">签到</view>
            </view>
        </comp-button>
    </view>
    <view class="info-box data-v-67ab8b30">
        <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-67ab8b30" data-event-opts="{{[ [ '^getUserInfo',[ ['myincomeClick'] ] ] ]}}" openType="getUserInfo" vueId="109ab485-7" vueSlots="{{['default']}}">
            <view class="page-income data-v-67ab8b30">
                <view class="income_money data-v-67ab8b30">{{(token?income:'***')+''}}</view>
                <view class="small data-v-67ab8b30">我的金币</view>
            </view>
        </comp-button>
        <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-67ab8b30" data-event-opts="{{[ [ '^getUserInfo',[ ['couponClick'] ] ] ]}}" openType="getUserInfo" vueId="109ab485-8" vueSlots="{{['default']}}">
            <view class="page-income data-v-67ab8b30">
                <view class="income_money data-v-67ab8b30">{{token?score||0:'***'}}</view>
                <view class="small data-v-67ab8b30">我的维豆</view>
            </view>
        </comp-button>
    </view>
    <view bindtap="__e" class="{{['page-info__member','flex-row','space-between','data-v-67ab8b30','page-info__member-bg-'+grade]}}" data-event-opts="{{[ [ 'tap',[ [ 'clickMember',['$event'] ] ] ] ]}}" wx:if="{{token}}">
        <view class="{{['member__growth','data-v-67ab8b30','growth-color-'+grade]}}">
            <view class="member__growth_left data-v-67ab8b30">
                <view class="left_top data-v-67ab8b30">
                    <view class="data-v-67ab8b30" wx:if="{{grade==5}}">成长值76000以上</view>
                    <view class="data-v-67ab8b30" wx:else>{{'当前成长值'+growth+'/'+gradeMax}}</view>
                </view>
                <view class="progress_bar data-v-67ab8b30">
                    <view class="{{['progress','data-v-67ab8b30','process-'+grade]}}" style="{{'width:'+barWidth+'rpx'+';'}}"></view>
                </view>
            </view>
            <view class="{{['member__growth_right','data-v-67ab8b30','btn-color-'+grade]}}">去提升</view>
        </view>
    </view>
    <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-67ab8b30" data-event-opts="{{[ [ '^getUserInfo',[ ['clickMember'] ] ] ]}}" openType="getUserInfo" vueId="109ab485-9" vueSlots="{{['default']}}" wx:if="{{!token}}">
        <view class="no-token data-v-67ab8b30">
            <view class="no-token-l data-v-67ab8b30">会员尊享权益</view>
            <view class="no-token-r data-v-67ab8b30">更多<comp-icon bind:__l="__l" class="data-v-67ab8b30" color="{{$root.m0}}" icon="iconarrow_right_o" size="24rpx" vueId="{{'109ab485-10'+','+'109ab485-9'}}"></comp-icon>
            </view>
        </view>
    </comp-button>
</view>
