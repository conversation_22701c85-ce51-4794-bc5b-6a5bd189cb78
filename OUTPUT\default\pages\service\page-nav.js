(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/service/page-nav"], {
    6255: function _(e, t, n) {


      (function(e) {
        var r = n("47a9");
        Object.defineProperty(t, "__esModule", {
          value: !0
        }), t.default = void 0;
        var o = r(n("7ca3")),
          a = n("8f59"),
          c = r(n("5e82")),
          i = n("b3c5");

        function u(e, t) {
          var n = Object.keys(e);
          if (Object.getOwnPropertySymbols) {
            var r = Object.getOwnPropertySymbols(e);
            t && (r = r.filter(function(t) {
              return Object.getOwnPropertyDescriptor(e, t).enumerable;
            })), n.push.apply(n, r);
          }
          return n;
        }
        var f = (0, i.getConfig)(),
          l = {
            props: {
              boxTitle: {
                type: String,
                default: ""
              },
              bbnone: {
                type: <PERSON>olean,
                default: !1
              },
              navs: {
                type: Array,
                default: function _default() {
                  return [];
                }
              }
            },
            computed: function(e) {
              for (var t = 1; t < arguments.length; t++) {
                var n = null != arguments[t] ? arguments[t] : {};
                t % 2 ? u(Object(n), !0).forEach(function(t) {
                  (0, o.default)(e, t, n[t]);
                }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : u(Object(n)).forEach(function(t) {
                  Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
                });
              }
              return e;
            }({}, (0, a.mapState)(["navbarHeight"])),
            methods: {
              authClickNav: function authClickNav(e) {
                e.url ? c.default.to({
                  fullPath: e.url
                }) : this.$emit("clickNav", e);
              },
              clickNav: function clickNav(t) {
                t.openType || (t.url ? c.default.to({
                  fullPath: t.url
                }) : "rgkf" !== t.id ? "跳转优品附近的店" !== t.id ? this.$emit("clickNav", t) : e.navigateToMiniProgram({
                  appId: f.ypAppId,
                  path: "/pagesB/shopsNearby/shopsNearby",
                  extraData: {
                    JumpYpType: "applet"
                  },
                  envVersion: f.ypEnvVersion,
                  success: function success(e) {},
                  fail: function fail(t) {
                    e.showToast({
                      icon: "none",
                      title: "打开优品小程序失败"
                    });
                  }
                }) : e.navigateTo({
                  url: "/pages/webview/webviewKF"
                }));
              }
            }
          };
        t.default = l;
      }).call(this, n("df3c")["default"]);
    },
    "8e76": function e76(e, t, n) {


      n.r(t);
      var r = n("6255"),
        o = n.n(r);
      for (var a in r)["default"].indexOf(a) < 0 && function(e) {
        n.d(t, e, function() {
          return r[e];
        });
      }(a);
      t["default"] = o.a;
    },
    a504: function a504(e, t, n) {


      n.d(t, "b", function() {
        return o;
      }), n.d(t, "c", function() {
        return a;
      }), n.d(t, "a", function() {
        return r;
      });
      var r = {
          compButton: function compButton() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-button/comp-button")]).then(n.bind(null, "ca5a"));
          },
          compImage: function compImage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-image/comp-image")]).then(n.bind(null, "31bf"));
          }
        },
        o = function o() {
          var e = this.$createElement;
          this._self._c;
        },
        a = [];
    },
    ca95: function ca95(e, t, n) {},
    da5f: function da5f(e, t, n) {


      n.r(t);
      var r = n("a504"),
        o = n("8e76");
      for (var a in o)["default"].indexOf(a) < 0 && function(e) {
        n.d(t, e, function() {
          return o[e];
        });
      }(a);
      n("f861");
      var c = n("828b"),
        i = Object(c["a"])(o["default"], r["b"], r["c"], !1, null, "0e8688be", null, !1, r["a"], void 0);
      t["default"] = i.exports;
    },
    f861: function f861(e, t, n) {


      var r = n("ca95"),
        o = n.n(r);
      o.a;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/service/page-nav-create-component', {
    'pages/service/page-nav-create-component': function pagesServicePageNavCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("da5f"));
    }
  },
  [
    ['pages/service/page-nav-create-component']
  ]
]);