(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/discover/page-ad"], {
    "0d19": function d19(n, e, t) {


      t.r(e);
      var r = t("50b2"),
        u = t.n(r);
      for (var a in r)["default"].indexOf(a) < 0 && function(n) {
        t.d(e, n, function() {
          return r[n];
        });
      }(a);
      e["default"] = u.a;
    },
    "2f37": function f37(n, e, t) {


      t.d(e, "b", function() {
        return u;
      }), t.d(e, "c", function() {
        return a;
      }), t.d(e, "a", function() {
        return r;
      });
      var r = {
          compSwiper: function compSwiper() {
            return t.e("components/comp-swiper/comp-swiper").then(t.bind(null, "8ae3"));
          }
        },
        u = function u() {
          var n = this.$createElement;
          this._self._c;
        },
        a = [];
    },
    "50b2": function b2(n, e, t) {


      Object.defineProperty(e, "__esModule", {
        value: !0
      }), e.default = void 0;
      var r = {
        props: {
          list: {
            type: Array,
            default: function _default() {
              return [];
            }
          }
        }
      };
      e.default = r;
    },
    5589: function _(n, e, t) {


      t.r(e);
      var r = t("2f37"),
        u = t("0d19");
      for (var a in u)["default"].indexOf(a) < 0 && function(n) {
        t.d(e, n, function() {
          return u[n];
        });
      }(a);
      t("de8e");
      var i = t("828b"),
        c = Object(i["a"])(u["default"], r["b"], r["c"], !1, null, "3963aed2", null, !1, r["a"], void 0);
      e["default"] = c.exports;
    },
    ba70: function ba70(n, e, t) {},
    de8e: function de8e(n, e, t) {


      var r = t("ba70"),
        u = t.n(r);
      u.a;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/discover/page-ad-create-component', {
    'pages/discover/page-ad-create-component': function pagesDiscoverPageAdCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("5589"));
    }
  },
  [
    ['pages/discover/page-ad-create-component']
  ]
]);