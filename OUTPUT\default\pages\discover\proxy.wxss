.name.data-v-616b401f {
    color: #1d1d1d;
    font-size: var(--font-size-54);
    font-weight: var(--font-weight-bold);
    line-height: 75rpx;
    padding: 24rpx 40rpx 0
}

.subname.data-v-616b401f {
    color: #1d1d1d;
    font-size: var(--font-size-30);
    line-height: 42rpx;
    padding: 7rpx 40rpx
}

.banner.data-v-616b401f {
    padding-top: 24rpx
}

.pic-row.data-v-de8c5bac {
    margin: 0 auto 0 24rpx;
    overflow-x: scroll
}

.pic-row .card.data-v-de8c5bac {
    background-size: contain;
    border-radius: 24rpx;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    height: 190rpx;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
    width: 400rpx
}

.pic-row .width400.data-v-de8c5bac {
    overflow: hidden;
    width: 400rpx
}

.pic-row.data-v-3937ab41 {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    margin: 0 auto;
    width: 702rpx
}

.pic-row .card.data-v-3937ab41 {
    background-color: #f5f5f5;
    background-size: cover;
    border-radius: 24rpx;
    color: #002d29;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    height: 156rpx;
    width: 339rpx
}

.pic-row .card .name.data-v-3937ab41 {
    font-family: PingFang-SC-Medium,PingFang-SC;
    font-size: 28rpx;
    font-weight: 500;
    margin: 24rpx 0 0 32rpx
}

.pic-row .marb.data-v-3937ab41 {
    margin-bottom: 24rpx
}

.pic-row .color1.data-v-3937ab41 {
    color: #002d29
}

.pic-row .color2.data-v-3937ab41 {
    color: #100040
}

.pic-row .color3.data-v-3937ab41 {
    color: #00254d
}

.pic-row .color4.data-v-3937ab41 {
    color: #4f0010
}

.article-row.data-v-b223e146 {
    display: inline-block;
    margin: 10rpx auto 0 24rpx;
    overflow-x: scroll
}

.article-row .act-card.data-v-b223e146 {
    background: #f6f7f9;
    border-radius: 24rpx;
    display: -webkit-flex;
    display: flex;
    height: 196rpx;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    margin: 0 0 24rpx;
    width: 702rpx
}

.article-row .act-card .left-box.data-v-b223e146 {
    margin-left: 36rpx;
    width: 420rpx
}

.article-row .act-card .left-box .title.data-v-b223e146 {
    color: rgba(0,0,0,.85);
    font-size: 32rpx;
    height: 45rpx;
    line-height: 45rpx;
    margin-top: 24rpx
}

.article-row .act-card .left-box .sub-title.data-v-b223e146,.article-row .act-card .left-box .title.data-v-b223e146 {
    font-family: PingFang-SC-Medium,PingFang-SC;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.article-row .act-card .left-box .sub-title.data-v-b223e146 {
    color: rgba(0,0,0,.35);
    font-size: 22rpx;
    height: 30rpx;
    line-height: 30rpx;
    margin: 4rpx 0 16rpx
}

.article-row .act-card .left-box .btn.data-v-b223e146 {
    background: #306dff;
    border-radius: 27rpx;
    color: #fff;
    font-family: PingFang-SC-Medium,PingFang-SC;
    font-size: 24rpx;
    font-weight: 500;
    height: 53rpx;
    line-height: 53rpx;
    text-align: center;
    width: 144rpx
}

.article-row .act-card .right-box.data-v-b223e146 {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    height: 196rpx;
    -webkit-justify-content: center;
    justify-content: center;
    width: 222rpx
}

.article-row .act-card .right-box .pic-wrap.data-v-b223e146 {
    border-radius: 12rpx;
    height: 148rpx;
    margin-right: 24rpx;
    overflow: hidden;
    position: relative;
    width: 198rpx
}

.article-row .act-card .right-box .pic-wrap .status-btn.data-v-b223e146 {
    -webkit-align-items: center;
    align-items: center;
    background: rgba(0,0,0,.5);
    border-radius: 0 16rpx 0 16rpx;
    color: #fff;
    display: -webkit-flex;
    display: flex;
    font-size: 20rpx;
    height: 32rpx;
    -webkit-justify-content: center;
    justify-content: center;
    line-height: 32rpx;
    position: absolute;
    right: 0;
    text-align: center;
    top: 0;
    width: 94rpx;
    z-index: 9
}

.page-goods.data-v-3963aed2 {
    position: relative
}

.hd.data-v-3963aed2 {
    margin: 40rpx 24rpx 0
}

.name.data-v-3963aed2 {
    color: rgba(0,0,0,.85);
    font-size: var(--font-size-40);
    font-weight: var(--font-weight-bold);
    line-height: 56rpx
}

.bd.data-v-3963aed2 {
    padding: 24rpx
}

.page-panel__title {
    color: rgba(0,0,0,.85);
    font-size: var(--font-size-40);
    font-weight: var(--font-weight-bold);
    line-height: 56rpx;
    margin: 40rpx 24rpx 24rpx
}
