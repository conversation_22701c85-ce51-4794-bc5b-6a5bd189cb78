.nav-top.data-v-5dca7865 {
    font-size: 18px
}

.nav-left.data-v-5dca7865 {
    left: 15px;
    position: absolute
}

.test-transparent.data-v-5dca7865 {
    left: 0!important;
    position: fixed!important;
    right: 0!important;
    top: 0!important;
    z-index: var(--zindex-4)
}

.emoji.data-v-5dca7865 {
    left: 99999rpx;
    opacity: 0;
    position: fixed
}

.navbar-back.data-v-5dca7865 {
    padding: 0 40rpx 0 0
}

.navbar-home.data-v-5dca7865 {
    border: 2rpx solid rgba(0,0,0,.2);
    border-radius: 32rpx;
    height: 64rpx;
    padding: 0 24rpx
}

.hd.data-v-5dca7865 {
    left: 0;
    position: fixed;
    right: 0
}

.brand.data-v-5dca7865 {
    height: 200rpx;
    position: relative;
    z-index: -1
}

.ft.data-v-5dca7865 {
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0
}

.btn.data-v-5dca7865 {
    bottom: 60px;
    bottom: calc(60px + constant(safe-area-inset-bottom));
    bottom: calc(60px + env(safe-area-inset-bottom));
    position: fixed;
    right: 24rpx
}

.indexGray.data-v-5dca7865 {
    filter: progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);
    -webkit-filter: grayscale(100%);
    -moz-filter: grayscale(100%);
    -ms-filter: grayscale(100%);
    -o-filter: grayscale(100%);
    filter: grayscale(100%);
    -webkit-filter: grey;
    filter: gray
}
