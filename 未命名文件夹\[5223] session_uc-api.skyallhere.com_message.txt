GET /miniprogram/api/v1/get-user HTTP/1.1
Host: uc-api.skyallhere.com
Connection: keep-alive
App-Path: /pages/login/login
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJvcGVuaWQiOiJvTVNTZzRnbXBwOWp5ajJaS2F4OGc3TmhsUEJJIiwidW5pb25pZCI6Im9BdWVHamp0T1hUQmRuS2lhUDk2U0VUcUZiNTQiLCJ1c2VyaWQiOjE5NDYwMzk3LCJ1c2VyX2NvZGUiOiI0MmQ0MDY2MGRkOTIyOGMwIiwidXNlcl9waG9uZSI6IjE4MTExNDU1NjI3Iiwibmlja19uYW1lIjoiIiwiZXhwIjoxNzU0MDYxOTY4fQ.WSELB3eRXwxIL-ux1XyDzhrWgjt3vgGOXF3DmquKEiQ
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
Content-Type: application/json
App-System: Windows 10 x64
xweb_xhr: 1
App-Model: microsoft
App-Sdkversion: 3.8.12
App-Version: 3.9.12
Accept: */*
Sec-Fetch-Site: cross-site
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
Referer: https://servicewechat.com/wxff438d3c60c63fb6/342/page-frame.html
Accept-Encoding: gzip, deflate, br
Accept-Language: zh-CN,zh;q=0.9



HTTP/1.1 200 OK
Date: Fri, 01 Aug 2025 14:56:08 GMT
Content-Type: application/json; charset=utf-8
Content-Length: 1072
Connection: keep-alive
X-Request-Id: ea3265bcc9865b823b24d0403dea993a
Access-Control-Allow-Origin: *
Access-Control-Allow-Credentials: true

{"code":0,"data":{"baseInfo":{"address":"","birthday":null,"city":"","county":"","growthValue":300,"headImg":"https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/avatar-default.png","inviteTag":"42d40660dd9228c0","latitude":"","longitude":"","nickName":"微信用户","openid":"oMSSg4gmpp9jyj2ZKax8g7NhlPBI","phone":"18111455627","province":"","todayGrowth":0,"todayScore":300,"unionid":"oAueGjjtOXTBdnKiaP96SETqFb54","userGender":0,"userGrade":1,"userScore":300,"vipLevel":1},"extra":{"prizePage":"https://uc-api.skyallhere.com/miniprogram/tp/duiba-nologin?dbredirect=%2F%2F74367.activity-13.m.duiba.com.cn%2Fcrecord%2Frecord%3Fdbnewopen"},"memberGrade":[{"levelGrade":1,"levelName":"基础","max":999,"min":1},{"levelGrade":2,"levelName":"白银","max":11999,"min":1000},{"levelGrade":3,"levelName":"黄金","max":24999,"min":12000},{"levelGrade":4,"levelName":"钻石","max":75999,"min":25000},{"levelGrade":5,"levelName":"黑金","max":-1,"min":76000}],"product":[],"taskProgress":{"isCanUploadInvoice":true,"isImproved":false}},"msg":"ok"}