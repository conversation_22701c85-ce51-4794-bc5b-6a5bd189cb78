.comp-agreement.data-v-537724d3 {
    -webkit-animation: fadein .5s;
    animation: fadein .5s;
    background: rgba(0,0,0,.75);
    bottom: 0;
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: var(--zindex-6)
}

.main.data-v-537724d3 {
    background: var(--color-fill-grey-inverse);
    border-radius: 16rpx;
    height: 70%;
    overflow: hidden;
    position: relative;
    width: 670rpx
}

.close.data-v-537724d3 {
    padding: 8rpx 24rpx
}

.tabs.data-v-537724d3 {
    border-bottom: 2rpx solid var(--color-divider-line);
    padding: 0 24rpx
}

.tab.data-v-537724d3 {
    color: var(--color-text-subtitle);
    font-size: var(--font-size-34);
    font-weight: var(--font-weight-bold);
    padding: 12rpx 24rpx 28rpx
}

.font28.data-v-537724d3 {
    font-size: var(--font-size-28);
    padding: 12rpx 14rpx 28rpx
}

.tab.is-active.data-v-537724d3 {
    color: var(--color-brand-1);
    position: relative
}

.tab.is-active.data-v-537724d3::after {
    background: var(--color-brand-1);
    bottom: 10rpx;
    content: "";
    height: 6rpx;
    left: 50%;
    position: absolute;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 80rpx
}

.ft.data-v-537724d3 {
    color: var(--color-text-title);
    overflow-y: auto
}

.ft__main.data-v-537724d3 {
    padding: 40rpx 48rpx
}

.ft__main .bold.data-v-537724d3 {
    font-weight: var(--font-weight-bold)
}

.ft__main .space.data-v-537724d3 {
    height: 40rpx
}

.gradients.data-v-537724d3 {
    background: linear-gradient(180deg,hsla(0,0%,100%,0),#fff 67%,#fff);
    bottom: 0;
    height: 90rpx;
    position: absolute;
    width: 100%
}
