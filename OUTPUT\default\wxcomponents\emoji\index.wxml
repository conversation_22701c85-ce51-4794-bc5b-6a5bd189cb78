<view class="weui-emoji_area" style="height: {{height}}px; background-color: {{backgroundColor}}">
    <view class="weui-emotion_list {{hasSafeBottom?'weui-emoji_area__has-safe-bottom':''}}" style="padding-left: {{padding}}px; padding-right: {{padding}}px;">
        <view hidden="{{!showHistory||history.length===0}}">
            <view class="weui-emotion_head">最近使用</view>
            <view bindtap="insertEmoji" class="weui-emotion_item" data-idx="{{item}}" style="margin-right: {{(index+1)%perLine?extraPadding:0}}px" wx:for="{{history}}" wx:key="*this">
                <view class="weui-icon_emotion {{emotions[item].style}}" style="background-image: url({{source}});"></view>
            </view>
        </view>
        <view class="weui-emotion_head" style="margin-top: 8px;">所有表情</view>
        <view bindtap="insertEmoji" class="weui-emotion_item" data-idx="{{index}}" style="padding-right: {{(index+1)%perLine?extraPadding:0}}px" wx:for="{{emotions}}" wx:key="*this">
            <view class="weui-icon_emotion {{item.style}}" style="background-image: url({{source}});"></view>
        </view>
    </view>
    <view class="weui-emoji__operation">
        <view bindtap="deleteEmoji" class="weui-emoji__operation__delete" wx:if="{{showDel}}">
            <image class="weui-emotion_del_btn" src="./icon_emotion_del.png"></image>
        </view>
        <view bindtap="send" class="weui-emoji__operation__send" wx:if="{{showSend}}">发送</view>
    </view>
</view>
