module.exports = require("../_commons/0.js")([{
  ids: [16],
  modules: {
    14: function(e, t, s) {
      e.exports = s(142)
    },
    142: function(e, t) {
      Component({
        options: {
          addGlobalClass: !0,
          multipleSlots: !0
        },
        properties: {
          title: {
            type: String,
            value: ""
          },
          type: {
            type: String,
            value: ""
          },
          icon: {
            type: String,
            value: ""
          },
          desc: {
            type: String,
            value: ""
          },
          extClass: {
            type: String,
            value: ""
          },
          size: {
            type: Number,
            value: 64
          }
        },
        data: {}
      })
    }
  },
  entries: [
    [14, 0]
  ]
}]);