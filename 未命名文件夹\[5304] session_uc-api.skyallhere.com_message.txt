GET /miniprogram/tp/duiba-nologin?dbredirect=https%3A%2F%2F74367-1-activity.m.dexfu.cn%2Fsign%2Fcomponent%2Fpage%3FsignOperatingId%3D303855131763271 HTTP/1.1
Host: uc-api.skyallhere.com
Connection: keep-alive
App-Path: /pages-user/get-wd/get-wd
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJvcGVuaWQiOiJvTVNTZzRnbXBwOWp5ajJaS2F4OGc3TmhsUEJJIiwidW5pb25pZCI6Im9BdWVHamp0T1hUQmRuS2lhUDk2U0VUcUZiNTQiLCJ1c2VyaWQiOjE5NDYwMzk3LCJ1c2VyX2NvZGUiOiI0MmQ0MDY2MGRkOTIyOGMwIiwidXNlcl9waG9uZSI6IjE4MTExNDU1NjI3Iiwibmlja19uYW1lIjoiIiwiZXhwIjoxNzU0MDYxOTY4fQ.WSELB3eRXwxIL-ux1XyDzhrWgjt3vgGOXF3DmquKEiQ
User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185
Content-Type: application/json
App-System: Windows 10 x64
xweb_xhr: 1
App-Model: microsoft
App-Sdkversion: 3.8.12
App-Version: 3.9.12
Accept: */*
Sec-Fetch-Site: cross-site
Sec-Fetch-Mode: cors
Sec-Fetch-Dest: empty
Referer: https://servicewechat.com/wxff438d3c60c63fb6/342/page-frame.html
Accept-Encoding: gzip, deflate, br
Accept-Language: zh-CN,zh;q=0.9



HTTP/1.1 200 OK
Date: Fri, 01 Aug 2025 14:56:40 GMT
Content-Type: application/json; charset=utf-8
Content-Length: 544
Connection: keep-alive
X-Request-Id: e02767db26f1226d6185f3ed3765fa9f
Access-Control-Allow-Origin: *
Access-Control-Allow-Credentials: true

{"code":0,"data":"https://74367-1-activity.m.dexfu.cn/autoLogin/autologin?credits=300&timestamp=1754060200915&redirect=https%3A%2F%2F74367-1-activity.m.dexfu.cn%2Fsign%2Fcomponent%2Fpage%3FsignOperatingId%3D303855131763271&dcustom=avatar%3Dhttps%253A%252F%252Fobs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com%252Fmember-center%252Fstatic%252Favatar-default.png%26nickname%3D%25E5%25BE%25AE%25E4%25BF%25A1%25E7%2594%25A8%25E6%2588%25B7&appKey=4KXh6ZM4FM8JuSBdanaBi61wK9w7&sign=195289957b3cc30c52e5398ffcb4f2af&uid=42d40660dd9228c0","msg":"ok"}