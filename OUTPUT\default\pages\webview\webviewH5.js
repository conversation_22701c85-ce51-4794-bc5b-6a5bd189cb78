(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/webview/webviewH5"], {
    "19da": function da(e, t, o) {


      (function(e, t) {
        var n = o("47a9");
        o("5c38");
        n(o("3240"));
        var r = n(o("bfb8"));
        e.__webpack_require_UNI_MP_PLUGIN__ = o, t(r.default);
      }).call(this, o("3223")["default"], o("df3c")["createPage"]);
    },
    "796b": function b(e, t, o) {


      (function(e, n) {
        var r = o("47a9");
        Object.defineProperty(t, "__esModule", {
          value: !0
        }), t.default = void 0;
        var c = r(o("7eb4")),
          a = r(o("ee10")),
          i = r(o("7ca3")),
          u = o("8f59"),
          f = r(o("a23b")),
          l = r(o("bdc5"));

        function s(e, t) {
          var o = Object.keys(e);
          if (Object.getOwnPropertySymbols) {
            var n = Object.getOwnPropertySymbols(e);
            t && (n = n.filter(function(t) {
              return Object.getOwnPropertyDescriptor(e, t).enumerable;
            })), o.push.apply(o, n);
          }
          return o;
        }
        var d = {
          data: function data() {
            return {
              isFirst: !0,
              isClickBack: !1,
              url: "",
              backgroundColor: "#ffffff",
              frontColor: "#000000",
              tag: "",
              needToken: !1,
              serviceIcon: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/icon_contact.png"
            };
          },
          computed: function(e) {
            for (var t = 1; t < arguments.length; t++) {
              var o = null != arguments[t] ? arguments[t] : {};
              t % 2 ? s(Object(o), !0).forEach(function(t) {
                (0, i.default)(e, t, o[t]);
              }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(o)) : s(Object(o)).forEach(function(t) {
                Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(o, t));
              });
            }
            return e;
          }({}, (0, u.mapState)(["profile"])),
          onShareAppMessage: function onShareAppMessage() {
            var e = "/pages/webview/webviewH5?url=".concat(encodeURIComponent(this.url), "&backgroundColor=").concat(this.backgroundColor, "&fontColor=").concat(this.frontColor);
            return this.tag && (e = "/pages/webview/webviewH5?tag=".concat(this.tag)), f.default.generateShareInfo({
              path: e,
              imageUrl: "https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/share_logo.jpg"
            });
          },
          onShow: function onShow() {
            f.default.log("Webview onShow", "当前 URL ".concat(this.url)), this.isClickBack = !1, this.isFirst || this.refresh();
          },
          onHide: function onHide() {
            this.url = "", this.isClickBack, f.default.log("Webview onHide", "当前 URL ".concat(this.url));
          },
          methods: {
            navbarBack: function navbarBack() {
              this.clickBack = !0, e.navigateBack({
                delta: 1
              });
            },
            contactService: function contactService() {
              console.log("onPostMessage"), n.openCustomerServiceChat ? n.openCustomerServiceChat({
                extInfo: {
                  url: "https://work.weixin.qq.com/kfid/kfcb1cba237e6cd90d3"
                },
                corpId: "ww48df30f06c821750"
              }) : n.showModal({
                title: "提示",
                content: "当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。"
              });
            },
            fetch: function fetch() {
              var e = f.default.getRoute().options || {};
              f.default.log("Webview fetch 当前 query 为", "".concat(e)), this.init(e);
            },
            getTokenUrl: function getTokenUrl() {},
            afterFetch: function afterFetch() {
              this.isFirst = !1;
            },
            init: function init() {
              var t = arguments,
                o = this;
              return (0, a.default)(c.default.mark(function n() {
                var r, a, i, u, s, d;
                return c.default.wrap(function(n) {
                  while (1) switch (n.prev = n.next) {
                    case 0:
                      if (r = t.length > 0 && void 0 !== t[0] ? t[0] : {}, a = decodeURIComponent(r.url || ""), i = r.backgroundColor || "#ffffff", u = r.frontColor || "#000000", r.from, console.log("h5页面URL:", a), s = /token=token/, !s.test(a)) {
                        n.next = 13;
                        break;
                      }
                      return n.next = 10, l.default.getCache("token");
                    case 10:
                      d = n.sent, a = a.replace(s, "token=".concat(d)), console.log("h5页面URL+token后:", a);
                    case 13:
                      o.url = a, o.backgroundColor = i, o.frontColor = u, e.setNavigationBarColor({
                        frontColor: u,
                        backgroundColor: i,
                        fail: function fail(e) {
                          f.default.log("Webview: setNavigationBarColor fail", e);
                        }
                      });
                    case 17:
                    case "end":
                      return n.stop();
                  }
                }, n);
              }))();
            }
          }
        };
        t.default = d;
      }).call(this, o("df3c")["default"], o("3223")["default"]);
    },
    9185: function _(e, t, o) {


      o.d(t, "b", function() {
        return r;
      }), o.d(t, "c", function() {
        return c;
      }), o.d(t, "a", function() {
        return n;
      });
      var n = {
          compPage: function compPage() {
            return Promise.all([o.e("common/vendor"), o.e("components/comp-page/comp-page")]).then(o.bind(null, "fccb"));
          }
        },
        r = function r() {
          var e = this.$createElement;
          this._self._c;
        },
        c = [];
    },
    ab83: function ab83(e, t, o) {


      o.r(t);
      var n = o("796b"),
        r = o.n(n);
      for (var c in n)["default"].indexOf(c) < 0 && function(e) {
        o.d(t, e, function() {
          return n[e];
        });
      }(c);
      t["default"] = r.a;
    },
    bfb8: function bfb8(e, t, o) {


      o.r(t);
      var n = o("9185"),
        r = o("ab83");
      for (var c in r)["default"].indexOf(c) < 0 && function(e) {
        o.d(t, e, function() {
          return r[e];
        });
      }(c);
      o("c1dc");
      var a = o("828b"),
        i = Object(a["a"])(r["default"], n["b"], n["c"], !1, null, null, null, !1, n["a"], void 0);
      t["default"] = i.exports;
    },
    c1dc: function c1dc(e, t, o) {


      var n = o("c7bf"),
        r = o.n(n);
      r.a;
    },
    c7bf: function c7bf(e, t, o) {}
  },
  [
    ["19da", "common/runtime", "common/vendor"]
  ]
]);