.main.data-v-226817f2 {
    background-image: url(https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/login-bg.png);
    background-size: cover;
    bottom: 0;
    position: fixed;
    top: 0;
    width: 100%
}

.hd.data-v-226817f2 {
    padding-top: 40rpx
}

.name.data-v-226817f2 {
    font-size: var(--font-size-54)
}

.name.data-v-226817f2,.subname.data-v-226817f2 {
    font-weight: var(--font-weight-bold);
    margin-left: 62rpx
}

.subname.data-v-226817f2 {
    color: var(--color-text-subtitle);
    font-size: var(--font-size-34);
    margin-top: 24rpx;
    padding-left: 30rpx;
    position: relative
}

.subname wx-text.data-v-226817f2 {
    color: var(--color-brand-1)
}

.subname.data-v-226817f2::before {
    background: var(--color-brand-1);
    border-radius: 50%;
    content: "";
    height: 10rpx;
    left: 0;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 10rpx
}

.bd.data-v-226817f2 {
    padding: 252rpx 40rpx 0
}

.btn.data-v-226817f2 {
    background: var(--color-brand-1)
}

.btn.data-v-226817f2,.disbtn.data-v-226817f2 {
    border-radius: 8rpx;
    color: var(--color-fill-grey-inverse);
    font-size: var(--font-size-34);
    padding: 24rpx
}

.disbtn.data-v-226817f2 {
    background: var(--color-text-weak)
}

.btn__name.data-v-226817f2 {
    margin-right: 10rpx
}

.tip.data-v-226817f2 {
    color: var(--color-text-weak);
    font-size: var(--font--size-26);
    line-height: 72rpx;
    padding-bottom: 58rpx
}

.tip__btn.data-v-226817f2 {
    color: var(--color-brand-1);
    margin: 0 6rpx
}
