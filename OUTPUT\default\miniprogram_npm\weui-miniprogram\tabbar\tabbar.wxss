[data-weui-theme="light"],page {
    --weui-BG-0: #ededed;
    --weui-BG-1: #f7f7f7;
    --weui-BG-2: #fff;
    --weui-BG-3: #f7f7f7;
    --weui-BG-4: #4c4c4c;
    --weui-BG-5: #fff;
    --weui-FG-0: rgba(0,0,0,0.9);
    --weui-FG-HALF: rgba(0,0,0,0.9);
    --weui-FG-1: rgba(0,0,0,0.5);
    --weui-FG-2: rgba(0,0,0,0.3);
    --weui-FG-3: rgba(0,0,0,0.1);
    --weui-FG-4: rgba(0,0,0,0.15);
    --weui-RED: #fa5151;
    --weui-REDORANGE: #ff6146;
    --weui-ORANGE: #fa9d3b;
    --weui-YELLOW: #ffc300;
    --weui-GREEN: #91d300;
    --weui-LIGHTGREEN: #95ec69;
    --weui-BRAND: #07c160;
    --weui-BLUE: #10aeff;
    --weui-INDIGO: #1485ee;
    --weui-PURPLE: #6467f0;
    --weui-WHITE: #fff;
    --weui-LINK: #576b95;
    --weui-TEXTGREEN: #06ae56;
    --weui-FG: #000;
    --weui-BG: #fff;
    --weui-TAG-TEXT-ORANGE: #fa9d3b;
    --weui-TAG-BACKGROUND-ORANGE: rgba(250,157,59,0.1);
    --weui-TAG-TEXT-GREEN: #06ae56;
    --weui-TAG-BACKGROUND-GREEN: rgba(6,174,86,0.1);
    --weui-TAG-TEXT-BLUE: #10aeff;
    --weui-TAG-BACKGROUND-BLUE: rgba(16,174,255,0.1);
    --weui-TAG-TEXT-BLACK: rgba(0,0,0,0.5);
    --weui-TAG-BACKGROUND-BLACK: rgba(0,0,0,0.05)
}

[data-weui-theme="dark"] {
    --weui-BG-0: #111;
    --weui-BG-1: #1e1e1e;
    --weui-BG-2: #191919;
    --weui-BG-3: #202020;
    --weui-BG-4: #404040;
    --weui-BG-5: #2c2c2c;
    --weui-FG-0: hsla(0,0%,100%,0.8);
    --weui-FG-HALF: hsla(0,0%,100%,0.6);
    --weui-FG-1: hsla(0,0%,100%,0.5);
    --weui-FG-2: hsla(0,0%,100%,0.3);
    --weui-FG-3: hsla(0,0%,100%,0.1);
    --weui-FG-4: hsla(0,0%,100%,0.15);
    --weui-RED: #fa5151;
    --weui-REDORANGE: #ff6146;
    --weui-ORANGE: #c87d2f;
    --weui-YELLOW: #cc9c00;
    --weui-GREEN: #74a800;
    --weui-LIGHTGREEN: #3eb575;
    --weui-BRAND: #07c160;
    --weui-BLUE: #10aeff;
    --weui-INDIGO: #1196ff;
    --weui-PURPLE: #8183ff;
    --weui-WHITE: hsla(0,0%,100%,0.8);
    --weui-LINK: #7d90a9;
    --weui-TEXTGREEN: #259c5c;
    --weui-FG: #fff;
    --weui-BG: #000;
    --weui-TAG-TEXT-ORANGE: rgba(250,157,59,0.6);
    --weui-TAG-BACKGROUND-ORANGE: rgba(250,157,59,0.1);
    --weui-TAG-TEXT-GREEN: rgba(6,174,86,0.6);
    --weui-TAG-BACKGROUND-GREEN: rgba(6,174,86,0.1);
    --weui-TAG-TEXT-BLUE: rgba(16,174,255,0.6);
    --weui-TAG-BACKGROUND-BLUE: rgba(16,174,255,0.1);
    --weui-TAG-TEXT-BLACK: hsla(0,0%,100%,0.5);
    --weui-TAG-BACKGROUND-BLACK: hsla(0,0%,100%,0.05)
}

[data-weui-theme="light"][data-weui-mode="care"],body[data-weui-mode="care"] {
    --weui-BG-0: #ededed;
    --weui-BG-1: #f7f7f7;
    --weui-BG-2: #fff;
    --weui-BG-3: #f7f7f7;
    --weui-BG-4: #4c4c4c;
    --weui-BG-5: #fff;
    --weui-FG-0: #000;
    --weui-FG-HALF: #000;
    --weui-FG-1: rgba(0,0,0,0.6);
    --weui-FG-2: rgba(0,0,0,0.42);
    --weui-FG-3: rgba(0,0,0,0.1);
    --weui-FG-4: rgba(0,0,0,0.15);
    --weui-RED: #dc3636;
    --weui-REDORANGE: #ff6146;
    --weui-ORANGE: #e17719;
    --weui-YELLOW: #bb8e00;
    --weui-GREEN: #4f8400;
    --weui-LIGHTGREEN: #2e8800;
    --weui-BRAND: #018942;
    --weui-BLUE: #007dbb;
    --weui-INDIGO: #0075e2;
    --weui-PURPLE: #6265f1;
    --weui-WHITE: #fff;
    --weui-LINK: #576b95;
    --weui-TEXTGREEN: #06ae56;
    --weui-FG: #000;
    --weui-BG: #fff;
    --weui-TAG-TEXT-ORANGE: #e17719;
    --weui-TAG-BACKGROUND-ORANGE: rgba(225,119,25,0.1);
    --weui-TAG-TEXT-GREEN: #06ae56;
    --weui-TAG-BACKGROUND-GREEN: rgba(6,174,86,0.1);
    --weui-TAG-TEXT-BLUE: #007dbb;
    --weui-TAG-BACKGROUND-BLUE: rgba(0,125,187,0.1);
    --weui-TAG-TEXT-BLACK: rgba(0,0,0,0.5);
    --weui-TAG-BACKGROUND-BLACK: rgba(0,0,0,0.05)
}

[data-weui-theme="dark"][data-weui-mode="care"] {
    --weui-BG-0: #111;
    --weui-BG-1: #1e1e1e;
    --weui-BG-2: #191919;
    --weui-BG-3: #202020;
    --weui-BG-4: #404040;
    --weui-BG-5: #2c2c2c;
    --weui-FG-0: hsla(0,0%,100%,0.85);
    --weui-FG-HALF: hsla(0,0%,100%,0.65);
    --weui-FG-1: hsla(0,0%,100%,0.55);
    --weui-FG-2: hsla(0,0%,100%,0.35);
    --weui-FG-3: hsla(0,0%,100%,0.1);
    --weui-FG-4: hsla(0,0%,100%,0.15);
    --weui-RED: #fa5151;
    --weui-REDORANGE: #ff6146;
    --weui-ORANGE: #c87d2f;
    --weui-YELLOW: #cc9c00;
    --weui-GREEN: #74a800;
    --weui-LIGHTGREEN: #3eb575;
    --weui-BRAND: #07c160;
    --weui-BLUE: #10aeff;
    --weui-INDIGO: #1196ff;
    --weui-PURPLE: #8183ff;
    --weui-WHITE: hsla(0,0%,100%,0.8);
    --weui-LINK: #7d90a9;
    --weui-TEXTGREEN: #259c5c;
    --weui-FG: #fff;
    --weui-BG: #000;
    --weui-TAG-TEXT-ORANGE: rgba(250,157,59,0.6);
    --weui-TAG-BACKGROUND-ORANGE: rgba(250,157,59,0.1);
    --weui-TAG-TEXT-GREEN: rgba(6,174,86,0.6);
    --weui-TAG-BACKGROUND-GREEN: rgba(6,174,86,0.1);
    --weui-TAG-TEXT-BLUE: rgba(16,174,255,0.6);
    --weui-TAG-BACKGROUND-BLUE: rgba(16,174,255,0.1);
    --weui-TAG-TEXT-BLACK: hsla(0,0%,100%,0.5);
    --weui-TAG-BACKGROUND-BLACK: hsla(0,0%,100%,0.05)
}

[data-weui-theme="light"],page {
    --weui-BG-COLOR-ACTIVE: #ececec
}

[data-weui-theme="dark"] {
    --weui-BG-COLOR-ACTIVE: #373737
}

[data-weui-theme="light"],page {
    --weui-BTN-DISABLED-FONT-COLOR: rgba(0,0,0,0.2)
}

[data-weui-theme="dark"] {
    --weui-BTN-DISABLED-FONT-COLOR: hsla(0,0%,100%,0.2)
}

[data-weui-theme="light"],page {
    --weui-BTN-DEFAULT-BG: #f2f2f2
}

[data-weui-theme="dark"] {
    --weui-BTN-DEFAULT-BG: hsla(0,0%,100%,0.08)
}

[data-weui-theme="light"],page {
    --weui-BTN-DEFAULT-COLOR: #06ae56
}

[data-weui-theme="dark"] {
    --weui-BTN-DEFAULT-COLOR: hsla(0,0%,100%,0.8)
}

[data-weui-theme="light"],page {
    --weui-BTN-DEFAULT-ACTIVE-BG: #e6e6e6
}

[data-weui-theme="dark"] {
    --weui-BTN-DEFAULT-ACTIVE-BG: hsla(0,0%,100%,0.126)
}

[data-weui-theme="light"],page {
    --weui-BTN-ACTIVE-MASK: rgba(0,0,0,0.1)
}

[data-weui-theme="dark"] {
    --weui-BTN-ACTIVE-MASK: hsla(0,0%,100%,0.05)
}

[data-weui-theme="light"][data-weui-mode="care"],body[data-weui-mode="care"] {
    --weui-BTN-DEFAULT-COLOR: #018942
}

[data-weui-theme="dark"][data-weui-mode="care"] {
    --weui-BTN-DEFAULT-COLOR: hsla(0,0%,100%,0.8)
}

[data-weui-theme="light"],page {
    --weui-DIALOG-LINE-COLOR: rgba(0,0,0,0.1)
}

[data-weui-theme="dark"] {
    --weui-DIALOG-LINE-COLOR: hsla(0,0%,100%,0.1)
}

@media only screen and (min-width:450px) {
    .weui-tabbar__reactive {
        -webkit-flex-direction: column;
        flex-direction: column;
        width: 60px;
        height: 100%
    }

    .weui-tabbar__reactive:before {
        left: unset;
        height: unset;
        content: " ";
        position: absolute;
        right: 0;
        top: 0;
        width: 1px;
        bottom: 0;
        border-right: 1px solid var(--weui-FG-3);
        color: var(--weui-FG-3);
        -webkit-transform-origin: 100% 0;
        transform-origin: 100% 0;
        -webkit-transform: scaleX(.5);
        transform: scaleX(.5)
    }

    .weui-tabbar__reactive .weui-tabbar__item {
        -webkit-flex: none;
        flex: none
    }
}