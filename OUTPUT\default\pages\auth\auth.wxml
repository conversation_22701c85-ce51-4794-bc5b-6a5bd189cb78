<comp-page bind:__l="__l" class="data-v-aa69973e vue-ref" data-ref="page" isBrand="{{false}}" isShare="{{false}}" navbarBackground="#E8EEFB" navbarIsBack="{{back}}" navbarTitle="授权登录" vueId="5502c3c0-1" vueSlots="{{['default','ft']}}">
    <view class="main data-v-aa69973e" style="{{'padding-top:'+navbarHeight+'px'+';'}}">
        <view class="hd flex-col col-center flex-one data-v-aa69973e">
            <comp-image bind:__l="__l" class="data-v-aa69973e" height="327rpx" name="login-center" vueId="{{'5502c3c0-2'+','+'5502c3c0-1'}}" width="360rpx"></comp-image>
            <view class="name data-v-aa69973e">创维用户中心</view>
        </view>
    </view>
    <view class="safe-bottom data-v-aa69973e" slot="ft">
        <view class="btn__wrap data-v-aa69973e">
            <comp-button bind:__l="__l" class="data-v-aa69973e" openType="getUserInfo" userInfoIsAuth="{{false}}" userInfoIsRegister="{{true}}" vueId="{{'5502c3c0-3'+','+'5502c3c0-1'}}" vueSlots="{{['default']}}">
                <view class="btn flex-row col-center row-center data-v-aa69973e">
                    <view class="btn__name data-v-aa69973e">授权用户信息</view>
                </view>
            </comp-button>
        </view>
        <view class="tip flex-row row-center data-v-aa69973e">请完成授权以继续使用</view>
    </view>
</comp-page>
