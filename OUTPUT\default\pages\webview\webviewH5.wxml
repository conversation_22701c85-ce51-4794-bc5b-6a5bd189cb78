<comp-page bind:__l="__l" bind:navbarBack="__e" class="vue-ref" data-event-opts="{{[ [ '^navbarBack',[ ['navbarBack'] ] ] ]}}" data-ref="page" isBrand="{{false}}" isMain="{{false}}" isNavbar="{{false}}" isSkeleton="{{false}}" navbarBackAuto="{{false}}" vueId="1e4e8e02-1" vueSlots="{{['default']}}">
    <web-view bindonPostMessage="__e" data-event-opts="{{[ [ 'onPostMessage',[ [ 'contactService',['$event'] ] ] ] ]}}" src="{{url}}" wx:if="{{url}}">
        <cover-view class="tool-bar">
            <cover-image mode="scaleToFill" src="/static/icon-discover-active.png" style="width:160rpx;height:160rpx;"></cover-image>
            <cover-image mode="scaleToFill" src="{{serviceIcon}}" style="width:160rpx;height:160rpx;"></cover-image>
        </cover-view>
    </web-view>
    <button style="position:absolute;top:20px;right:20px;">按钮</button>
</comp-page>
