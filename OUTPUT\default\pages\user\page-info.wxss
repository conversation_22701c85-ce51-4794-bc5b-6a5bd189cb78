.no-token.data-v-67ab8b30 {
    -webkit-align-items: center;
    align-items: center;
    background: #e0f0ff;
    border-radius: 40rpx 40rpx 0 0;
    display: -webkit-flex;
    display: flex;
    font-size: 28rpx;
    font-weight: 600;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    margin: 0 32rpx;
    padding: 20rpx 20rpx 0
}

.no-token .no-token-l.data-v-67ab8b30 {
    color: #000
}

.no-token .no-token-r.data-v-67ab8b30 {
    -webkit-align-items: center;
    align-items: center;
    color: rgba(0,0,0,.3);
    display: -webkit-flex;
    display: flex
}

.top-name.data-v-67ab8b30 {
    display: -webkit-flex;
    display: flex;
    margin-left: 15px;
    position: relative
}

.signClass.data-v-67ab8b30 {
    position: absolute;
    right: 0rpx;
    top: 56rpx
}

.page-info.data-v-67ab8b30 {
    background: url(https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/user-bg-new.png);
    background-position: top;
    background-repeat: no-repeat;
    background-size: contain;
    background-size: 100%;
    padding-bottom: 20rpx
}

.page-info__main.data-v-67ab8b30 {
    padding: 24rpx 40rpx 20rpx;
    position: relative
}

.page-info__main .main__name.data-v-67ab8b30 {
    background: transparent;
    font-size: 44rpx;
    font-weight: var(--font-weight-bold);
    line-height: 54rpx
}

.page-info__main .main__subname.data-v-67ab8b30 {
    font-size: var(--font-size-26);
    margin-top: 8rpx
}

.page-income.data-v-67ab8b30 {
    color: #2e3641;
    font-size: 36rpx;
    margin-bottom: 20rpx;
    margin-right: 30rpx;
    text-align: center;
    width: 112rpx
}

.income_money.data-v-67ab8b30 {
    color: var(--color-brand-1);
    color: #306dff;
    font-family: PingFangSC-Medium;
    font-size: 28rpx;
    font-weight: 500;
    height: 40rpx;
    line-height: 40rpx
}

.avatar-wrap.data-v-67ab8b30 {
    position: relative
}

.avatar-wrap .main__set.data-v-67ab8b30 {
    bottom: 0;
    position: absolute;
    right: 0
}

.page-info__member.data-v-67ab8b30 {
    background-size: cover!important;
    height: 100rpx;
    margin: 0 32rpx;
    padding: 0 15rpx
}

.page-info__member-bg-1.data-v-67ab8b30 {
    background: url(https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/card_user_bg_6.png);
    background-repeat: no-repeat;
    background-size: contain
}

.page-info__member-bg-2.data-v-67ab8b30 {
    background: url(https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/card_user_bg_2.png);
    background-repeat: no-repeat;
    background-size: contain
}

.page-info__member-bg-3.data-v-67ab8b30 {
    background: url(https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/card_user_bg_3.png);
    background-repeat: no-repeat;
    background-size: contain
}

.page-info__member-bg-4.data-v-67ab8b30 {
    background: url(https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/card_user_bg_4.png);
    background-repeat: no-repeat;
    background-size: contain
}

.page-info__member-bg-5.data-v-67ab8b30 {
    background: url(https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/card_user_bg_5.png);
    background-repeat: no-repeat;
    background-size: contain
}

.page-info__member-bg-6.data-v-67ab8b30 {
    background: url(https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/mp_user_mine_bar_bg-6.png);
    background-repeat: no-repeat;
    background-size: contain
}

.growth-color-1.data-v-67ab8b30 {
    color: #0d72fb
}

.growth-color-2.data-v-67ab8b30 {
    color: #676767
}

.growth-color-3.data-v-67ab8b30 {
    color: #ce7a1b
}

.growth-color-4.data-v-67ab8b30 {
    color: #6457ff
}

.growth-color-5.data-v-67ab8b30,.growth-color-6.data-v-67ab8b30 {
    color: #ffcf75
}

.process-1.data-v-67ab8b30 {
    background: linear-gradient(81deg,#d7e8ff,#0d72fb)
}

.process-2.data-v-67ab8b30 {
    background: linear-gradient(81deg,#e8e8e8,#9c9c9c)
}

.process-3.data-v-67ab8b30 {
    background: linear-gradient(81deg,#ffd4a3,#ce7a1b)
}

.process-4.data-v-67ab8b30 {
    background: linear-gradient(135deg,#c7ebff,#648cf9 43%,#9a63ff)
}

.process-5.data-v-67ab8b30 {
    background: linear-gradient(81deg,#fff8c7,#f9a964 43%,#ffce63)
}

.process-6.data-v-67ab8b30 {
    background: linear-gradient(90deg,#fff8c7,#f9a964,#ffce63)
}

.page-info__member .badge__name.data-v-67ab8b30 {
    font-size: var(--font-size-26)
}

.page-info__member .member__growth.data-v-67ab8b30 {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex;
    font-size: var(--font-size-22);
    margin: 0 auto;
    position: relative;
    width: 100%
}

.page-info__member .member__growth .member__growth_left.data-v-67ab8b30 {
    -webkit-flex: 1;
    flex: 1;
    margin-left: 20rpx
}

.page-info__member .member__growth .member__growth_left .left_top.data-v-67ab8b30 {
    font-size: 22rpx;
    margin: 24rpx 0rpx 16rpx;
    text-align: right
}

.page-info__member .member__growth .member__growth_right.data-v-67ab8b30 {
    border-radius: 24rpx;
    color: #fff;
    font-size: 24rpx;
    font-weight: 700;
    height: 48rpx;
    line-height: 48rpx;
    margin-left: 34rpx;
    text-align: center;
    width: 120rpx
}

.page-info__member .member__growth .btn-color-1.data-v-67ab8b30 {
    background: linear-gradient(146deg,#24dfff,#0d72fb)
}

.page-info__member .member__growth .btn-color-2.data-v-67ab8b30 {
    background: linear-gradient(146deg,#b8c2d9,#404e5d)
}

.page-info__member .member__growth .btn-color-3.data-v-67ab8b30 {
    background: linear-gradient(146deg,#ffd756,#ffac1d)
}

.page-info__member .member__growth .btn-color-4.data-v-67ab8b30 {
    background: linear-gradient(146deg,#d3bcff,#8892ff)
}

.page-info__member .member__growth .btn-color-5.data-v-67ab8b30 {
    background: linear-gradient(146deg,#ffec5e,#ffcb76)
}

.progress_bar.data-v-67ab8b30 {
    background: #eaeaea;
    margin: 0 auto 26rpx;
    overflow: hidden;
    width: 510rpx
}

.progress_bar .progress.data-v-67ab8b30,.progress_bar.data-v-67ab8b30 {
    border-radius: 3rpx;
    height: 6rpx
}

.growth__rights.data-v-67ab8b30 {
    margin: 26rpx auto 16rpx
}

.page-info__member .member__growth .growth__subname.data-v-67ab8b30 {
    margin: 0 4rpx 0 12rpx
}

.info-box.data-v-67ab8b30 {
    display: -webkit-flex;
    display: flex;
    margin: 0 32rpx;
    padding-left: 30rpx
}

.small.data-v-67ab8b30 {
    color: #2e3641;
    font-family: PingFangSC-Regular;
    font-size: 24rpx;
    font-weight: 400;
    height: 33rpx;
    line-height: 33rpx
}

.page-header__sign.data-v-67ab8b30 {
    background: var(--color-brand-1);
    border-radius: 36rpx 0 0 36rpx;
    color: var(--color-fill-grey-inverse);
    font-size: var(--font-size-28);
    font-weight: var(--font-weight-bold);
    height: 60rpx;
    line-height: 1;
    margin-left: 20rpx;
    padding: 0 25rpx
}

.page-header__sign .sign__name.data-v-67ab8b30 {
    margin-left: 7rpx
}
