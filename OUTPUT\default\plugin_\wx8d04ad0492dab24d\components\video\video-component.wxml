<view class="container" style="height:{{options.height?options.height:heightNum}}rpx;width:{{options.width?options.width:widthNum}}rpx">
    <video autoplay="{{autoplay}}" bindended="videoEnd" binderror="videoErrorCallback" bindplay="videoPlay" class="video_demo" enablePlayGesture="{{true}}" id="myVideo" loop="{{true}}" objectFit="{{mode}}" playBtnPosition="center" showCenterPlayBtn="{{false}}" showFullscreenBtn="{{false}}" src="{{videoUrl}}"></video>
    <view bindtap="videoScale" class="video-scale" style="width:{{containerWidth}}rpx;height:{{containerBot+bottomMargin}}rpx;padding-left:{{containerPadding}}rpx;padding-right:{{containerPadding}}rpx;padding-bottom:{{bottomMargin}}rpx;">
        <image mode="aspectFill" src="{{scaleIconState?scaleSmall:scaleBig}}" style="width:{{containerBot/3}}rpx;height:{{containerBot/3}}rpx;margin-bottom:{{containerBot/4}}rpx"></image>
    </view>
    <view class="container-user" style="width:{{containerWidth}}rpx;position:absolute;right: 0rpx;bottom: {{containerBot+bottomMargin}}rpx;padding:{{containerPadding}}rpx">
        <view bindtap="getPortrait" class="portrait" wx:if="{{avatar}}">
            <image class="user-image" mode="aspectFill" src="{{options.avatar}}" style="width:{{userImageWid}}rpx;height:{{userImageHei}}rpx"></image>
        </view>
        <view bindtap="getLikeInfo" class="user-like" style="margin-top:{{marginTop}}rpx" wx:if="{{userLikeState}}">
            <image class="user-like-icon" mode="aspectFill" src="{{likeState?likeSelectIcon:likeIcon}}" style="width:{{userLikeWid}}rpx;height:{{userLikeHei}}rpx"></image>
            <view class="user-like-num" wx:if="{{miLinIconState}}">{{likeNum}}</view>
        </view>
        <view bindtap="getShareSDK" class="user-share" style="margin-top:{{marginTop}}rpx" wx:if="{{shareSDK}}">
            <image class="user-share-icon" mode="aspectFill" src="{{weChatIcon}}" style="width:{{userShareWid}}rpx;height:{{userShareHei}}rpx"></image>
            <view class="user-share-info" wx:if="{{miLinIconState}}">微信分享</view>
        </view>
        <view bindtap="getCircleOfFriends" class="user-friends-circle" style="margin-top:{{marginTop}}rpx" wx:if="{{circleOfFriends}}">
            <image class="user-friends-circle-icon" mode="aspectFill" src="{{friendCircle}}" style="width:{{userFriendWid}}rpx;height:{{userFriendHei}}rpx"></image>
            <view class="user-friends-info" wx:if="{{miLinIconState}}">朋友圈分享</view>
        </view>
        <view class="mi-lin-company" style="margin-top:{{marginTop}}rpx" wx:if="{{iconShow}}">
            <image class="mi-lin-icon" mode="aspectFill" src="{{miLinIcon}}" style="width:{{miLinWid}}rpx;height:{{miLinHei}}rpx"></image>
            <view class="mi-lin-info" wx:if="{{miLinIconState}}">秘邻提供</view>
        </view>
    </view>
    <view class="bottom-info" style="position: absolute;left: 0rpx;bottom: {{containerBot-bottomMargin}}rpx;width: calc(100% - {{containerWidth}}rpx);" wx:if="{{userName||describe}}">
        <view class="bottom-title" style="font-size:{{titleFont}}rpx" wx:if="{{userName}}">
            <view class="title-icon">@</view>
            <view class="title-info">{{userName}}</view>
        </view>
        <view class="bottom-direction" style="font-size:{{directionFont}}rpx" wx:if="{{describe}}">{{describe}}</view>
    </view>
</view>
