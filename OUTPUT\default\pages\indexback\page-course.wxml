<view class="page-course data-v-7d7d98fd">
    <comp-split bind:__l="__l" class="data-v-7d7d98fd" vueId="2173f8a8-1"></comp-split>
    <page-panel bind:__l="__l" bind:clickMore="__e" class="data-v-7d7d98fd" data-event-opts="{{[ [ '^clickMore',[ ['clickMore'] ] ] ]}}" title="视频教程" vueId="2173f8a8-2" vueSlots="{{['bd']}}">
        <scroll-view class="banner data-v-7d7d98fd" scrollX="{{true}}" slot="bd">
            <view class="list flex-row data-v-7d7d98fd" style="{{'width:'+205*$root.g0+28+'px'+';'}}">
                <view bindtap="__e" class="{{['item','data-v-7d7d98fd',index===$root.g1-1?'is-last':'']}}" data-event-opts="{{[ [ 'tap',[ [ 'clickCard',['$0'],[ [ ['list','',index] ] ] ] ] ] ]}}" wx:for="{{list}}" wx:key="index">
                    <comp-vertical-card bind:__l="__l" class="data-v-7d7d98fd" img="{{item.imgs[0]&&item.imgs[0].img}}" isVideo="{{true}}" name="{{item.name}}" subname="{{item.subname}}" type="{{10}}" vueId="{{'2173f8a8-3-'+index+','+'2173f8a8-2'}}" width="205px"></comp-vertical-card>
                </view>
            </view>
        </scroll-view>
    </page-panel>
</view>
