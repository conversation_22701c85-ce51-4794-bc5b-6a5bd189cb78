(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/indexback/page-header"], {
    "2dcc": function dcc(e, t, n) {


      var r = n("47a9");
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.default = void 0;
      var c = r(n("7ca3")),
        o = (r(n("8b9c")), r(n("a23b")), r(n("5e82"))),
        u = n("8f59");

      function a(e, t) {
        var n = Object.keys(e);
        if (Object.getOwnPropertySymbols) {
          var r = Object.getOwnPropertySymbols(e);
          t && (r = r.filter(function(t) {
            return Object.getOwnPropertyDescriptor(e, t).enumerable;
          })), n.push.apply(n, r);
        }
        return n;
      }
      var i = {
        props: {
          banner: {
            type: Array,
            default: function _default() {
              return [];
            }
          },
          score: {
            type: Number,
            default: 0
          },
          signTokenUrl: {
            type: String,
            default: ""
          }
        },
        computed: function(e) {
          for (var t = 1; t < arguments.length; t++) {
            var n = null != arguments[t] ? arguments[t] : {};
            t % 2 ? a(Object(n), !0).forEach(function(t) {
              (0, c.default)(e, t, n[t]);
            }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : a(Object(n)).forEach(function(t) {
              Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
            });
          }
          return e;
        }({}, (0, u.mapState)(["navbarHeight"])),
        methods: {
          clickScore: function clickScore() {
            o.default.to({
              page: "userScore"
            });
          },
          clickSign: function clickSign() {
            o.default.toWebviewByTokenUrl({
              tokenUrl: this.signTokenUrl
            });
          }
        }
      };
      t.default = i;
    },
    "4c93": function c93(e, t, n) {


      var r = n("f5ea"),
        c = n.n(r);
      c.a;
    },
    75888: function _(e, t, n) {


      n.r(t);
      var r = n("d382"),
        c = n("d64d");
      for (var o in c)["default"].indexOf(o) < 0 && function(e) {
        n.d(t, e, function() {
          return c[e];
        });
      }(o);
      n("4c93");
      var u = n("828b"),
        a = Object(u["a"])(c["default"], r["b"], r["c"], !1, null, "1717b550", null, !1, r["a"], void 0);
      t["default"] = a.exports;
    },
    d382: function d382(e, t, n) {


      n.d(t, "b", function() {
        return c;
      }), n.d(t, "c", function() {
        return o;
      }), n.d(t, "a", function() {
        return r;
      });
      var r = {
          compSwiper: function compSwiper() {
            return n.e("components/comp-swiper/comp-swiper").then(n.bind(null, "8ae3"));
          }
        },
        c = function c() {
          var e = this.$createElement;
          this._self._c;
        },
        o = [];
    },
    d64d: function d64d(e, t, n) {


      n.r(t);
      var r = n("2dcc"),
        c = n.n(r);
      for (var o in r)["default"].indexOf(o) < 0 && function(e) {
        n.d(t, e, function() {
          return r[e];
        });
      }(o);
      t["default"] = c.a;
    },
    f5ea: function f5ea(e, t, n) {}
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/indexback/page-header-create-component', {
    'pages/indexback/page-header-create-component': function pagesIndexbackPageHeaderCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("75888"));
    }
  },
  [
    ['pages/indexback/page-header-create-component']
  ]
]);