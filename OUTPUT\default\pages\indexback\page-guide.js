(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/indexback/page-guide"], {
    "3f55": function f55(t, n, e) {


      var c = e("47a9");
      Object.defineProperty(n, "__esModule", {
        value: !0
      }), n.default = void 0;
      c(e("a23b"));
      var a = c(e("5e82")),
        u = {
          components: {
            PagePanel: function PagePanel() {
              e.e("pages/indexback/page-panel").then(function() {
                return resolve(e("f4ec"));
              }.bind(null, e)).catch(e.oe);
            }
          },
          props: {
            list: {
              type: Array,
              default: function _default() {
                return [];
              }
            }
          },
          methods: {
            clickMore: function clickMore() {
              a.default.to({
                page: "community"
              });
            },
            clickItem: function clickItem(t) {
              t.url && a.default.to({
                fullPath: t.url
              });
            }
          }
        };
      n.default = u;
    },
    "5de1": function de1(t, n, e) {


      e.r(n);
      var c = e("3f55"),
        a = e.n(c);
      for (var u in c)["default"].indexOf(u) < 0 && function(t) {
        e.d(n, t, function() {
          return c[t];
        });
      }(u);
      n["default"] = a.a;
    },
    c49b: function c49b(t, n, e) {


      var c = e("d091"),
        a = e.n(c);
      a.a;
    },
    d091: function d091(t, n, e) {},
    e4d3: function e4d3(t, n, e) {


      e.r(n);
      var c = e("f590"),
        a = e("5de1");
      for (var u in a)["default"].indexOf(u) < 0 && function(t) {
        e.d(n, t, function() {
          return a[t];
        });
      }(u);
      e("c49b");
      var r = e("828b"),
        i = Object(r["a"])(a["default"], c["b"], c["c"], !1, null, "46b1b01c", null, !1, c["a"], void 0);
      n["default"] = i.exports;
    },
    f590: function f590(t, n, e) {


      e.d(n, "b", function() {
        return a;
      }), e.d(n, "c", function() {
        return u;
      }), e.d(n, "a", function() {
        return c;
      });
      var c = {
          compVerticalCard: function compVerticalCard() {
            return e.e("components/comp-vertical-card/comp-vertical-card").then(e.bind(null, "ed84"));
          }
        },
        a = function a() {
          var t = this.$createElement,
            n = (this._self._c, this.list.length),
            e = this.list.length;
          this.$mp.data = Object.assign({}, {
            $root: {
              g0: n,
              g1: e
            }
          });
        },
        u = [];
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/indexback/page-guide-create-component', {
    'pages/indexback/page-guide-create-component': function pagesIndexbackPageGuideCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("e4d3"));
    }
  },
  [
    ['pages/indexback/page-guide-create-component']
  ]
]);