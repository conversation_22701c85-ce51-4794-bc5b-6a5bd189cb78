.comp-task-card.data-v-5724aeea {
    padding: 32rpx 40rpx
}

.hd.data-v-5724aeea {
    margin-right: 16rpx
}

.bd.data-v-5724aeea {
    overflow: hidden
}

.name.data-v-5724aeea {
    font-size: var(--font-size-28);
    font-weight: var(--font-weight-bold);
    line-height: 40rpx
}

.subname.data-v-5724aeea {
    color: var(--color-text-weak);
    font-size: var(--font-size-24);
    line-height: 33rpx;
    margin-top: 8rpx
}

.ft.data-v-5724aeea {
    padding-left: 16rpx
}

.btn.data-v-5724aeea {
    border: 2rpx solid transparent;
    border-radius: 32rpx;
    font-size: var(--font-size-26);
    height: 64rpx;
    min-width: 148rpx;
    padding: 0 20rpx
}

.btn.is-status-0.data-v-5724aeea,.btn.is-status-2.data-v-5724aeea {
    background: linear-gradient(101deg,#f2d8a7,#eac075);
    color: #1d1d1d;
    font-weight: var(--font-weight-bold)
}

.btn.is-status-1.data-v-5724aeea {
    border-color: #f1f1f1;
    color: var(--color-text-sub)
}
