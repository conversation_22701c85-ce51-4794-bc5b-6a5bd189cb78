<view class="comp-recommand data-v-e456d562">
    <view class="hd flex-row space-between data-v-e456d562">
        <view class="comp_rights_title data-v-e456d562"></view>
        <view bindtap="__e" class="more flex-row col-center data-v-e456d562" data-event-opts="{{[ [ 'tap',[ [ 'clickMore',['$event'] ] ] ] ]}}" wx:if="{{$root.g0}}">
            <view class="data-v-e456d562">更多</view>
            <comp-icon bind:__l="__l" class="data-v-e456d562" color="rgba(0, 0, 0, 0.30)" icon="iconarrow_right_o" size="24rpx" vueId="6477135e-1"></comp-icon>
        </view>
    </view>
    <view class="bd data-v-e456d562">
        <view class="empty__wrap data-v-e456d562" wx:if="{{!$root.g1}}">
            <comp-empty bind:__l="__l" class="data-v-e456d562" subTitle="暂无数据" vueId="6477135e-2"></comp-empty>
        </view>
        <view class="product__wrap flex flex-row space-between col-center data-v-e456d562" wx:else>
            <view bindtap="__e" class="goods-card data-v-e456d562" data-event-opts="{{[ [ 'tap',[ [ 'clickAct',['$0'],[ [ ['list','',index] ] ] ] ] ] ]}}" wx:if="{{item.isShow}}" wx:for="{{list}}" wx:key="index">
                <view class="goods-img data-v-e456d562">
                    <comp-image bind:__l="__l" class="data-v-e456d562" height="307rpx; border-radius: 16rpx 16rpx 0rpx 0rpx;" isCustomSrc="{{true}}" mode="aspectFill" url="{{item.bannerUrl}}" vueId="{{'6477135e-3-'+index}}" width="100%"></comp-image>
                </view>
                <view class="goods-name data-v-e456d562">{{item.bannerTitle}}</view>
                <view class="score-row flex flex-row space-between col-center data-v-e456d562">
                    <view class="score data-v-e456d562">{{(item.requiredPoints||0)+'维豆'}}</view>
                    <view class="exchange data-v-e456d562">去兑换</view>
                </view>
            </view>
        </view>
    </view>
</view>
