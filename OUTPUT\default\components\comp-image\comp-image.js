(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["components/comp-image/comp-image"], {
    "271a": function a(t, e, _a) {},
    "31bf": function bf(t, e, a) {


      a.r(e);
      var i = a("bdba"),
        n = a("fbd4");
      for (var r in n)["default"].indexOf(r) < 0 && function(t) {
        a.d(e, t, function() {
          return n[t];
        });
      }(r);
      a("884e");
      var o = a("828b"),
        u = Object(o["a"])(n["default"], i["b"], i["c"], !1, null, "162b69e2", null, !1, i["a"], void 0);
      e["default"] = u.exports;
    },
    "884e": function e(t, _e, a) {


      var i = a("271a"),
        n = a.n(i);
      n.a;
    },
    bdba: function bdba(t, e, a) {


      a.d(e, "b", function() {
        return i;
      }), a.d(e, "c", function() {
        return n;
      }), a.d(e, "a", function() {});
      var i = function i() {
          var t = this.$createElement;
          this._self._c;
        },
        n = [];
    },
    d27c: function d27c(t, e, a) {


      Object.defineProperty(e, "__esModule", {
        value: !0
      }), e.default = void 0;
      var i = a("b3c5"),
        n = (0, i.getConfig)(),
        r = {
          props: {
            isServer: {
              type: Boolean,
              default: !1
            },
            isCloud: {
              type: Boolean,
              default: !0
            },
            isCustomSrc: {
              type: Boolean,
              default: !1
            },
            url: {
              type: String,
              default: ""
            },
            height: {
              type: String,
              default: "100%"
            },
            width: {
              type: String,
              default: "100%"
            },
            name: {
              type: String,
              default: ""
            },
            suffix: {
              type: String,
              default: ".png"
            },
            ossParam: {
              type: String,
              default: ""
            },
            otherParam: {
              type: String,
              default: ""
            },
            mode: {
              type: String,
              default: "aspectFill"
            },
            isVideoCover: {
              type: Boolean,
              default: !1
            },
            videoIconSize: {
              type: String,
              default: "110rpx"
            },
            radius: {
              type: String,
              default: "0%"
            },
            isBorder: {
              type: Boolean,
              default: !1
            },
            borderColor: {
              type: String,
              default: "#fff"
            },
            borderWidth: {
              type: String,
              default: "2rpx"
            }
          },
          data: function data() {
            return {
              cloudPrefix: "".concat(n.oss).concat(n.staticPath, "/"),
              localPrefix: "../../static/"
            };
          },
          computed: {
            src: function src() {
              if (this.isCustomSrc && this.url) return this.url;
              if (!this.name) return "";
              if (this.isServer) return this.name + "".concat(this.name.indexOf("?") > -1 ? "&" : "?").concat(this.ossParam).concat(this.otherParam);
              var t = this.isCloud ? this.cloudPrefix : this.localPrefix,
                e = this.name + this.suffix;
              return t + e + "?".concat(this.ossParam).concat(this.otherParam);
            }
          }
        };
      e.default = r;
    },
    fbd4: function fbd4(t, e, a) {


      a.r(e);
      var i = a("d27c"),
        n = a.n(i);
      for (var r in i)["default"].indexOf(r) < 0 && function(t) {
        a.d(e, t, function() {
          return i[t];
        });
      }(r);
      e["default"] = n.a;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['components/comp-image/comp-image-create-component', {
    'components/comp-image/comp-image-create-component': function componentsCompImageCompImageCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("31bf"));
    }
  },
  [
    ['components/comp-image/comp-image-create-component']
  ]
]);