(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["components/comp-page/comp-page"], {
    "1fe5": function fe5(e, t, a) {


      (function(e) {
        var n = a("47a9");
        Object.defineProperty(t, "__esModule", {
          value: !0
        }), t.default = void 0;
        var r = n(a("7eb4")),
          o = n(a("ee10")),
          i = n(a("7ca3")),
          c = a("8f59"),
          u = a("b3c5"),
          s = n(a("a23b")),
          p = (n(a("8b9c")), n(a("5e82")));

        function f(e, t) {
          var a = Object.keys(e);
          if (Object.getOwnPropertySymbols) {
            var n = Object.getOwnPropertySymbols(e);
            t && (n = n.filter(function(t) {
              return Object.getOwnPropertyDescriptor(e, t).enumerable;
            })), a.push.apply(a, n);
          }
          return a;
        }

        function l(e) {
          for (var t = 1; t < arguments.length; t++) {
            var a = null != arguments[t] ? arguments[t] : {};
            t % 2 ? f(Object(a), !0).forEach(function(t) {
              (0, i.default)(e, t, a[t]);
            }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(a)) : f(Object(a)).forEach(function(t) {
              Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(a, t));
            });
          }
          return e;
        }
        var d = (0, u.getConfig)(),
          g = {
            name: "page",
            components: {
              CompError: function CompError() {
                a.e("components/comp-page/comp-error").then(function() {
                  return resolve(a("6c42"));
                }.bind(null, a)).catch(a.oe);
              },
              CompSkeleton: function CompSkeleton() {
                a.e("components/comp-page/comp-skeleton").then(function() {
                  return resolve(a("4dba"));
                }.bind(null, a)).catch(a.oe);
              },
              CompContact: function CompContact() {
                a.e("components/comp-contact/comp-contact").then(function() {
                  return resolve(a("0d10"));
                }.bind(null, a)).catch(a.oe);
              }
            },
            props: {
              indexGray: {
                type: Boolean,
                default: !1
              },
              isNavbar: {
                type: Boolean,
                default: !0
              },
              showContact: {
                type: Boolean,
                default: !1
              },
              navbarTitle: {
                type: String,
                default: "创维用户中心"
              },
              navbarColor: {
                type: String,
                default: "#000"
              },
              navbarBackground: {
                type: String,
                default: "#fff"
              },
              navbarIsBack: {
                type: Boolean,
                default: !0
              },
              navbarBackDelta: {
                type: Number,
                default: 1
              },
              navbarBackAuto: {
                type: Boolean,
                default: !0
              },
              navbarIsTransparent: {
                type: Boolean,
                default: !1
              },
              navbarIsLimitTitle: {
                type: Boolean,
                default: !1
              },
              isSkeleton: {
                type: Boolean,
                default: !0
              },
              isBrand: {
                type: Boolean,
                default: !0
              },
              isEmoji: {
                type: Boolean,
                default: !1
              },
              isMain: {
                type: Boolean,
                default: !0
              },
              mainBackground: {
                type: String,
                default: "transparent"
              },
              mainBackgroundSize: {
                type: String,
                default: "cover"
              },
              mainIsCover: {
                type: Boolean,
                default: !1
              },
              hdZindex: {
                type: Number,
                default: 2
              },
              bdBackground: {
                type: String,
                default: "transparent"
              },
              ftZindex: {
                type: Number,
                default: 2
              },
              btnZindex: {
                type: Number,
                default: 2
              },
              btnPaddingBottom: {
                type: String,
                default: "0px"
              },
              brandIcon: {
                type: String,
                default: "footer"
              },
              isShare: {
                type: Boolean,
                default: !0
              },
              brandSafeBottom: {
                type: Boolean,
                default: !0
              },
              brandZindex: {
                type: Number,
                default: -1
              }
            },
            data: function data() {
              return {
                pageLoading: !0,
                pageError: null,
                pageNavbarIsHome: !1,
                pageEmoji: "".concat(d.oss).concat(d.staticPath, "/wechat-emoji.png"),
                pageHdHeight: 0,
                pageFtHeight: 0,
                pageScrollTop: 0,
                statusBarHeight: 0
              };
            },
            created: function created() {
              e.getSystemInfoSync();
            },
            computed: l(l({}, (0, c.mapState)(["navbarHeight"])), {}, {
              pageNavbarHeight: function pageNavbarHeight() {
                return this.navbarHeight || 64;
              },
              pageNavbarTitle: function pageNavbarTitle() {
                return this.navbarTitle;
              },
              pageNavbarBackground: function pageNavbarBackground() {
                return this.pageError || this.pageScrollTop > 20 ? "#fff" : this.pageNavbarIsTransparent ? "transparent" : this.pageLoading ? "#fff" : this.navbarBackground;
              },
              pageNavbarColor: function pageNavbarColor() {
                return this.pageError || this.pageLoading || this.pageScrollTop > 20 ? "#000" : this.navbarColor;
              },
              pageNavbarBackDelta: function pageNavbarBackDelta() {
                return this.navbarBackDelta;
              },
              pageNavbarIsBack: function pageNavbarIsBack() {
                return !this.pageLoading && !this.pageNavbarIsHome && this.navbarIsBack;
              },
              pageNavbarIsTransparent: function pageNavbarIsTransparent() {
                return !this.pageError && (console.log(this.pageError, this.navbarIsTransparent), this.navbarIsTransparent);
              },
              pageNavbarIsLimitTitle: function pageNavbarIsLimitTitle() {
                return this.navbarIsLimitTitle;
              },
              pageMainBackground: function pageMainBackground() {
                return this.mainBackground;
              },
              pageMainBackgroundSize: function pageMainBackgroundSize() {
                return this.mainBackgroundSize;
              },
              pageMainMinHeight: function pageMainMinHeight() {
                return this.mainIsCover ? this.pageNavbarIsTransparent ? "100vh" : "calc(100vh - ".concat(this.navbarHeight, "px)") : "auto";
              },
              pageHdZindex: function pageHdZindex() {
                return this.hdZindex;
              },
              pageBdBackground: function pageBdBackground() {
                return this.bdBackground;
              },
              pageFtZindex: function pageFtZindex() {
                return this.ftZindex;
              },
              pageBtnZindex: function pageBtnZindex() {
                return this.btnZindex;
              },
              pageBtnPaddingBottom: function pageBtnPaddingBottom() {
                return this.btnPaddingBottom;
              },
              pageIsShare: function pageIsShare() {
                var t = this.isShare;
                t ? e.showShareMenu() : e.hideShareMenu();
              }
            }),
            mounted: function mounted() {
              var e = this;
              return (0, o.default)(r.default.mark(function t() {
                return r.default.wrap(function(t) {
                  while (1) switch (t.prev = t.next) {
                    case 0:
                      e.checkNavbarIsHome();
                    case 1:
                    case "end":
                      return t.stop();
                  }
                }, t);
              }))();
            },
            methods: {
              setLoading: function setLoading(e) {
                var t = this;
                return (0, o.default)(r.default.mark(function a() {
                  var n;
                  return r.default.wrap(function(a) {
                    while (1) switch (a.prev = a.next) {
                      case 0:
                        return t.pageLoading = e, a.next = 3, Promise.all([s.default.getElementInfo(".hd", t), s.default.getElementInfo(".ft", t)]);
                      case 3:
                        n = a.sent, t.pageHdHeight = n[0].height, t.pageFtHeight = n[1].height;
                      case 6:
                      case "end":
                        return a.stop();
                    }
                  }, a);
                }))();
              },
              setError: function setError(e) {
                this.pageError = e ? {
                  type: e.type || s.default.ERROR_TYPE.DEFAULT,
                  message: e.message || ""
                } : null;
              },
              checkNavbarIsHome: function checkNavbarIsHome() {
                var e = getCurrentPages(),
                  t = e[e.length - 1];
                this.navbarIsBack ? 1 !== e.length || -1 !== d.navbarHomeBlackList.indexOf(t.route) ? this.pageNavbarIsHome = !1 : this.pageNavbarIsHome = !0 : this.pageNavbarIsHome = !1;
              },
              clickNavbarHome: function clickNavbarHome() {
                p.default.reLaunch({
                  page: "index"
                });
              },
              clickNavbarBack: function clickNavbarBack() {
                this.navbarBackAuto ? e.navigateBack({
                  delta: this.navbarBackDelta
                }) : this.$emit("navbarBack");
              }
            }
          };
        t.default = g;
      }).call(this, a("df3c")["default"]);
    },
    "631f": function f(e, t, a) {


      var n = a("ac18"),
        r = a.n(n);
      r.a;
    },
    "78d1": function d1(e, t, a) {


      a.d(t, "b", function() {
        return r;
      }), a.d(t, "c", function() {
        return o;
      }), a.d(t, "a", function() {
        return n;
      });
      var n = {
          compImage: function compImage() {
            return Promise.all([a.e("common/vendor"), a.e("components/comp-image/comp-image")]).then(a.bind(null, "31bf"));
          }
        },
        r = function r() {
          var e = this.$createElement;
          this._self._c;
        },
        o = [];
    },
    "79e4": function e4(e, t, a) {


      a.r(t);
      var n = a("1fe5"),
        r = a.n(n);
      for (var o in n)["default"].indexOf(o) < 0 && function(e) {
        a.d(t, e, function() {
          return n[e];
        });
      }(o);
      t["default"] = r.a;
    },
    ac18: function ac18(e, t, a) {},
    fccb: function fccb(e, t, a) {


      a.r(t);
      var n = a("78d1"),
        r = a("79e4");
      for (var o in r)["default"].indexOf(o) < 0 && function(e) {
        a.d(t, e, function() {
          return r[e];
        });
      }(o);
      a("631f");
      var i = a("828b"),
        c = Object(i["a"])(r["default"], n["b"], n["c"], !1, null, "5dca7865", null, !1, n["a"], void 0);
      t["default"] = c.exports;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['components/comp-page/comp-page-create-component', {
    'components/comp-page/comp-page-create-component': function componentsCompPageCompPageCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("fccb"));
    }
  },
  [
    ['components/comp-page/comp-page-create-component']
  ]
]);