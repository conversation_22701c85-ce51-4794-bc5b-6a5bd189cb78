page {
    background: #f2f2f2!important
}

.bg.data-v-c509e3c4 {
    background: linear-gradient(180deg,#7bc9ff,#f5f5f5);
    height: 1486rpx;
    left: 0rpx;
    position: absolute;
    top: 0rpx;
    width: 100%
}

.page-panel__title.data-v-c509e3c4 {
    color: rgba(0,0,0,.85);
    font-size: var(--font-size-40);
    font-weight: var(--font-weight-bold);
    line-height: 56rpx;
    margin: 40rpx 24rpx 24rpx
}

.toAdd.data-v-c509e3c4 {
    bottom: 60rpx;
    height: 150rpx;
    position: fixed;
    right: 20rpx;
    width: 150rpx
}

.activity-center.data-v-c509e3c4 {
    margin-top: 40rpx;
    padding: 0rpx 24rpx
}

.activity-center .activity-center-top.data-v-c509e3c4 {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex
}

.activity-center .activity-center-top .title.data-v-c509e3c4 {
    color: rgba(0,0,0,.85);
    font-size: 40rpx;
    font-weight: 700;
    margin-right: 10rpx
}

.activity-center .activity-center-top .info.data-v-c509e3c4 {
    background: #ffe7e7;
    border-radius: 23rpx;
    color: rgba(0,0,0,.8);
    -webkit-flex: 1;
    flex: 1;
    font-size: 22rpx;
    height: 46rpx
}

.activity-center .activity-center-top .right.data-v-c509e3c4 {
    margin-left: 36rpx
}

.activity-center .activity-center-top .right .right-txt.data-v-c509e3c4 {
    -webkit-align-items: center;
    align-items: center;
    color: rgba(0,0,0,.3);
    display: -webkit-flex;
    display: flex;
    font-size: 28rpx
}

.activity-center .activity-center-bottom.data-v-c509e3c4 {
    background: #fff;
    border-radius: 24rpx;
    margin-bottom: 40rpx;
    margin-top: 24rpx;
    padding: 32rpx 24rpx
}

.activity-center .activity-center-bottom .activity-center-item.data-v-c509e3c4 {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    margin-bottom: 24rpx
}

.activity-center .activity-center-bottom .activity-center-item.data-v-c509e3c4:last-child {
    margin-bottom: 0rpx
}

.activity-center .activity-center-bottom .activity-center-item .item-left.data-v-c509e3c4 {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex
}

.activity-center .activity-center-bottom .activity-center-item .item-left .img.data-v-c509e3c4 {
    border-radius: 12rpx;
    height: 148rpx;
    margin-right: 24rpx;
    width: 170rpx
}

.activity-center .activity-center-bottom .activity-center-item .item-left .item-left-info.data-v-c509e3c4 {
    -webkit-flex: 1;
    flex: 1
}

.activity-center .activity-center-bottom .activity-center-item .item-left .item-left-info .name.data-v-c509e3c4 {
    color: rgba(0,0,0,.8);
    font-size: 32rpx;
    font-weight: 700;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 300rpx
}

.activity-center .activity-center-bottom .activity-center-item .item-left .item-left-info .status.data-v-c509e3c4 {
    color: rgba(0,0,0,.4);
    font-size: 26rpx;
    margin: 10rpx 0rpx
}

.activity-center .activity-center-bottom .activity-center-item .item-left .item-left-info .time.data-v-c509e3c4 {
    color: rgba(0,0,0,.4);
    font-size: 26rpx
}

.activity-center .activity-center-bottom .activity-center-item .item-right .btn.data-v-c509e3c4 {
    background: #306dff;
    border-radius: 26rpx;
    color: #fff;
    font-size: 24rpx;
    height: 54rpx;
    line-height: 54rpx;
    text-align: center;
    width: 144rpx
}

.tablist.data-v-c509e3c4 {
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: space-around;
    justify-content: space-around;
    margin-top: -15rpx
}

.tablist .imgItem.data-v-c509e3c4 {
    height: 218rpx;
    position: relative;
    width: 218rpx
}

.tablist .imgItem wx-image.data-v-c509e3c4 {
    height: 100%;
    width: 100%
}

.tablist .imgItem .imgItemTitle.data-v-c509e3c4 {
    color: rgba(0,0,0,.8);
    font-family: PingFang-SC;
    font-size: 28rpx;
    font-style: normal;
    font-weight: 700;
    left: 50%;
    line-height: 40rpx;
    position: absolute;
    text-align: right;
    text-align: center;
    top: 24rpx;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 190rpx
}

.imgList.data-v-c509e3c4 {
    margin-top: 40rpx
}

.imgList .imgListTitle.data-v-c509e3c4 {
    color: rgba(0,0,0,.85);
    font-family: PingFang-SC;
    font-size: 40rpx;
    font-style: normal;
    font-weight: 700;
    line-height: 56rpx;
    margin-left: 24rpx;
    text-align: left
}

.imgList .subjectList.data-v-c509e3c4 {
    display: -webkit-flex;
    display: flex;
    margin-left: 5rpx;
    margin-top: 20rpx;
    position: relative
}

.imgList .subjectList .subjectItem.data-v-c509e3c4 {
    padding: 0 20rpx;
    text-align: center
}

.imgList .subjectList .subjectItem .subject-i.data-v-c509e3c4 {
    height: 73rpx;
    left: 50%;
    position: absolute;
    top: -27rpx;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 128rpx
}

.imgList .subjectList .subjectItem .subject-t.data-v-c509e3c4 {
    color: rgba(0,0,0,.3);
    font-family: PingFang-SC;
    font-size: 28rpx;
    font-style: normal;
    font-weight: 500;
    line-height: 40rpx;
    text-align: center;
    white-space: nowrap
}

.imgList .subjectList .subjectItem .subject-t-active.data-v-c509e3c4 {
    color: #306dff;
    font-size: 32rpx
}

.inspiration-section.data-v-c509e3c4 {
    margin: 40rpx 0 0;
    padding: 0 24rpx
}

.inspiration-section .inspiration-title.data-v-c509e3c4 {
    color: rgba(0,0,0,.85);
    font-family: PingFang-SC;
    font-size: 40rpx;
    font-weight: 700;
    line-height: 56rpx;
    margin-bottom: 24rpx
}

.inspiration-section .inspiration-list.data-v-c509e3c4 {
    display: -webkit-flex;
    display: flex;
    gap: 24rpx
}

.inspiration-section .inspiration-column.data-v-c509e3c4 {
    display: -webkit-flex;
    display: flex;
    -webkit-flex: 1;
    flex: 1;
    -webkit-flex-direction: column;
    flex-direction: column;
    gap: 24rpx
}

.inspiration-section .inspiration-card.data-v-c509e3c4 {
    background: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,.04);
    -webkit-flex-direction: column;
    flex-direction: column;
    overflow: hidden;
    width: 100%
}

.inspiration-section .inspiration-img.data-v-c509e3c4 {
    border-radius: 24rpx 24rpx 0rpx 0rpx;
    display: block;
    height: 480rpx;
    object-fit: cover;
    width: 100%
}

.inspiration-section .inspiration-info.data-v-c509e3c4 {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    gap: 8rpx;
    padding: 16rpx 16rpx 12rpx
}

.inspiration-section .inspiration-title-text.data-v-c509e3c4 {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    color: #222;
    display: -webkit-box;
    font-size: 28rpx;
    font-weight: 500;
    line-height: 1.4;
    margin-bottom: 8rpx;
    min-height: 40rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all
}

.inspiration-section .inspiration-user.data-v-c509e3c4 {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex
}

.inspiration-section .inspiration-user .inspiration-avatar.data-v-c509e3c4 {
    border-radius: 50%;
    height: 36rpx;
    margin-right: 8rpx;
    width: 36rpx
}

.inspiration-section .inspiration-user .inspiration-username.data-v-c509e3c4 {
    color: #666;
    font-size: 24rpx
}
