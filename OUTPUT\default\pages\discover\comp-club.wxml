<view class="pic-row data-v-3937ab41">
    <view class="data-v-3937ab41" wx:if="{{item.isShow}}" wx:for="{{list}}" wx:key="index">
        <comp-button bind:__l="__l" bind:onClick="__e" class="data-v-3937ab41" data-event-opts="{{[ [ '^onClick',[ [ 'clickAct',['$0'],[ [ ['list','',index] ] ] ] ] ] ]}}" vueId="{{'ad167ac2-1-'+index}}" vueSlots="{{['default']}}">
            <view class="{{['card','data-v-3937ab41',index<2?'marb':'']}}" style="{{'background:'+'url('+item.columnIcon+') no-repeat'+';'+'background-size:'+'110rpx'+';'+'background-color:'+item.backgroundColor+';'+'background-position:'+'209rpx 36rpx'+';'}}">
                <view class="name data-v-3937ab41">{{item.columnName}}</view>
            </view>
        </comp-button>
    </view>
</view>
