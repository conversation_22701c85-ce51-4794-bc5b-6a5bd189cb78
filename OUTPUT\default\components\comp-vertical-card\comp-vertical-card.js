(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["components/comp-vertical-card/comp-vertical-card"], {
    4745: function _(t, e, n) {


      var a = n("9e08"),
        i = n.n(a);
      i.a;
    },
    4787: function _(t, e, n) {


      n.r(e);
      var a = n("fa92"),
        i = n.n(a);
      for (var r in a)["default"].indexOf(r) < 0 && function(t) {
        n.d(e, t, function() {
          return a[t];
        });
      }(r);
      e["default"] = i.a;
    },
    "9e08": function e08(t, e, n) {},
    c591: function c591(t, e, n) {


      n.d(e, "b", function() {
        return i;
      }), n.d(e, "c", function() {
        return r;
      }), n.d(e, "a", function() {
        return a;
      });
      var a = {
          compImage: function compImage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-image/comp-image")]).then(n.bind(null, "31bf"));
          }
        },
        i = function i() {
          var t = this.$createElement;
          this._self._c;
        },
        r = [];
    },
    ed84: function ed84(t, e, n) {


      n.r(e);
      var a = n("c591"),
        i = n("4787");
      for (var r in i)["default"].indexOf(r) < 0 && function(t) {
        n.d(e, t, function() {
          return i[t];
        });
      }(r);
      n("4745");
      var u = n("828b"),
        c = Object(u["a"])(i["default"], a["b"], a["c"], !1, null, "0780e7da", null, !1, a["a"], void 0);
      e["default"] = c.exports;
    },
    fa92: function fa92(t, e, n) {


      Object.defineProperty(e, "__esModule", {
        value: !0
      }), e.default = void 0;
      var a = {
        props: {
          type: {
            type: Number,
            default: 10
          },
          isVideo: {
            type: Boolean,
            default: !1
          },
          img: {
            type: String,
            default: ""
          },
          name: {
            type: String,
            default: ""
          },
          subname: {
            type: String,
            default: ""
          },
          height: {
            type: String,
            default: ""
          },
          width: {
            type: String,
            default: ""
          }
        },
        methods: {
          clickCard: function clickCard() {
            this.$emit("clickCard");
          }
        }
      };
      e.default = a;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['components/comp-vertical-card/comp-vertical-card-create-component', {
    'components/comp-vertical-card/comp-vertical-card-create-component': function componentsCompVerticalCardCompVerticalCardCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("ed84"));
    }
  },
  [
    ['components/comp-vertical-card/comp-vertical-card-create-component']
  ]
]);