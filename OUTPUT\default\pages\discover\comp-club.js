(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/discover/comp-club"], {
    "039a": function a(t, e, n) {


      var r = n("a60a"),
        a = n.n(r);
      a.a;
    },
    "16d8": function d8(t, e, n) {


      n.d(e, "b", function() {
        return a;
      }), n.d(e, "c", function() {
        return u;
      }), n.d(e, "a", function() {
        return r;
      });
      var r = {
          compButton: function compButton() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-button/comp-button")]).then(n.bind(null, "ca5a"));
          }
        },
        a = function a() {
          var t = this.$createElement;
          this._self._c;
        },
        u = [];
    },
    5141: function _(t, e, n) {


      var r = n("47a9");
      Object.defineProperty(e, "__esModule", {
        value: !0
      }), e.default = void 0;
      var a = r(n("7eb4")),
        u = r(n("ee10")),
        c = r(n("5e82")),
        i = (r(n("a23b")), {
          components: {},
          props: {
            banner: {
              type: Array,
              default: function _default() {
                return [];
              }
            },
            list: {
              type: Array,
              default: function _default() {
                return [];
              }
            }
          },
          data: function data() {
            return {
              isExpand: !1,
              current: 0
            };
          },
          computed: {
            innerList: function innerList() {
              var t = this.list;
              return this.isExpand ? t : t.slice(0, 3);
            }
          },
          methods: {
            change: function change(t) {
              this.current = t.detail.current;
            },
            clickAct: function clickAct(t) {
              return (0, u.default)(a.default.mark(function e() {
                return a.default.wrap(function(e) {
                  while (1) switch (e.prev = e.next) {
                    case 0:
                      if ("用户好评" !== t.columnName) {
                        e.next = 3;
                        break;
                      }
                      return c.default.to({
                        page: "activityComment"
                      }), e.abrupt("return");
                    case 3:
                      if ("玩转电视" !== t.columnName) {
                        e.next = 6;
                        break;
                      }
                      return c.default.to({
                        page: "course"
                      }), e.abrupt("return");
                    case 6:
                      c.default.to({
                        page: "templateArticle",
                        query: {
                          id: t.id,
                          title: t.columnName,
                          articleType: t.articleType,
                          columnLabel: t.columnLabel
                        }
                      });
                    case 7:
                    case "end":
                      return e.stop();
                  }
                }, e);
              }))();
            }
          }
        });
      e.default = i;
    },
    "66a7": function a7(t, e, n) {


      n.r(e);
      var r = n("5141"),
        a = n.n(r);
      for (var u in r)["default"].indexOf(u) < 0 && function(t) {
        n.d(e, t, function() {
          return r[t];
        });
      }(u);
      e["default"] = a.a;
    },
    "95fc": function fc(t, e, n) {


      n.r(e);
      var r = n("16d8"),
        a = n("66a7");
      for (var u in a)["default"].indexOf(u) < 0 && function(t) {
        n.d(e, t, function() {
          return a[t];
        });
      }(u);
      n("039a");
      var c = n("828b"),
        i = Object(c["a"])(a["default"], r["b"], r["c"], !1, null, "3937ab41", null, !1, r["a"], void 0);
      e["default"] = i.exports;
    },
    a60a: function a60a(t, e, n) {}
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/discover/comp-club-create-component', {
    'pages/discover/comp-club-create-component': function pagesDiscoverCompClubCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("95fc"));
    }
  },
  [
    ['pages/discover/comp-club-create-component']
  ]
]);