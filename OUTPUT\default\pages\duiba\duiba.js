(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/duiba/duiba"], {
    "001c": function c(e, t, a) {


      a.d(t, "b", function() {
        return n;
      }), a.d(t, "c", function() {
        return r;
      }), a.d(t, "a", function() {});
      var n = function n() {
          var e = this.$createElement;
          this._self._c;
        },
        r = [];
    },
    2437: function _(e, t, a) {


      a.r(t);
      var n = a("001c"),
        r = a("8f80");
      for (var u in r)["default"].indexOf(u) < 0 && function(e) {
        a.d(t, e, function() {
          return r[e];
        });
      }(u);
      var o = a("828b"),
        c = Object(o["a"])(r["default"], n["b"], n["c"], !1, null, null, null, !1, n["a"], void 0);
      t["default"] = c.exports;
    },
    "644e": function e(_e, t, a) {


      (function(e) {
        var n = a("47a9");
        Object.defineProperty(t, "__esModule", {
          value: !0
        }), t.default = void 0;
        var r = n(a("7eb4")),
          u = n(a("34cf")),
          o = n(a("ee10")),
          c = n(a("7ca3")),
          i = a("8f59"),
          d = n(a("8b9c")),
          f = n(a("5e82")),
          l = n(a("bdc5")),
          b = n(a("a23b"));

        function s(e, t) {
          var a = Object.keys(e);
          if (Object.getOwnPropertySymbols) {
            var n = Object.getOwnPropertySymbols(e);
            t && (n = n.filter(function(t) {
              return Object.getOwnPropertyDescriptor(e, t).enumerable;
            })), a.push.apply(a, n);
          }
          return a;
        }
        var g = {
          data: function data() {
            return {
              tag: "",
              duibaUrl: "",
              url: ""
            };
          },
          computed: function(e) {
            for (var t = 1; t < arguments.length; t++) {
              var a = null != arguments[t] ? arguments[t] : {};
              t % 2 ? s(Object(a), !0).forEach(function(t) {
                (0, c.default)(e, t, a[t]);
              }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(a)) : s(Object(a)).forEach(function(t) {
                Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(a, t));
              });
            }
            return e;
          }({}, (0, i.mapState)(["remoteConfig"])),
          onShareAppMessage: function onShareAppMessage() {
            var e = "/pages/duiba/duiba?channel=mp&duibaUrl=".concat(encodeURIComponent(this.duibaUrl));
            return this.tag && (e = "/pages/duiba/duiba?channel=mp&tag=".concat(this.tag)), b.default.generateShareInfo({
              path: e
            });
          },
          onLoad: function onLoad() {
            var e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
              t = e.tag,
              a = e.duibaUrl;
            this.tag = t || "", this.duibaUrl = a ? decodeURIComponent(a) : "";
          },
          onShow: function onShow() {
            var t = this;
            return (0, o.default)(r.default.mark(function a() {
              var n, o, c, i, s, g, p;
              return r.default.wrap(function(a) {
                while (1) switch (a.prev = a.next) {
                  case 0:
                    if (!t.url) {
                      a.next = 2;
                      break;
                    }
                    return a.abrupt("return");
                  case 2:
                    if (t.tag || t.duibaUrl) {
                      a.next = 4;
                      break;
                    }
                    return a.abrupt("return");
                  case 4:
                    if (e.showLoading({
                        icon: "none",
                        title: "加载中"
                      }), n = "", !t.tag) {
                      a.next = 16;
                      break;
                    }
                    return b.default.log("Duiba onShow 使用 tag 进行分享"), a.next = 10, l.default.getRemoteConfigData();
                  case 10:
                    if (o = b.default.get(t.remoteConfig, "duiba", {}) || {}, Object.keys(o).length && o[t.tag]) {
                      a.next = 15;
                      break;
                    }
                    return b.default.log("Duiba: onShow 兑吧链接获取异常"), e.showToast({
                      icon: "none",
                      title: "兑吧链接配置异常"
                    }), a.abrupt("return");
                  case 15:
                    n = o[t.tag];
                  case 16:
                    return b.default.log("Duiba onShow 使用 duibaUrl 进行分享"), t.duibaUrl && (n = t.duibaUrl), a.next = 20, e.login();
                  case 20:
                    return c = a.sent, i = (0, u.default)(c, 2), s = i[0], g = i[1], s && b.default.log("Duiba: onShow 获取 Code 异常"), a.next = 27, d.default.anyjump({
                      code: g.code,
                      jumpUrl: n
                    });
                  case 27:
                    if (p = a.sent, e.hideLoading(), 0 === b.default.get(p, "data.code", 0)) {
                      a.next = 33;
                      break;
                    }
                    return b.default.log("Duiba: onShow 服务器返回 Code !== 0", p), f.default.to({
                      page: "auth",
                      query: {
                        back: !0
                      }
                    }), a.abrupt("return");
                  case 33:
                    b.default.log("Duiba: onShow 服务器返回正常跳转兑吧链接", p), t.url = b.default.get(p, "data.data", "") || "";
                  case 35:
                  case "end":
                    return a.stop();
                }
              }, a);
            }))();
          }
        };
        t.default = g;
      }).call(this, a("df3c")["default"]);
    },
    "8f80": function f80(e, t, a) {


      a.r(t);
      var n = a("644e"),
        r = a.n(n);
      for (var u in n)["default"].indexOf(u) < 0 && function(e) {
        a.d(t, e, function() {
          return n[e];
        });
      }(u);
      t["default"] = r.a;
    },
    c40d: function c40d(e, t, a) {


      (function(e, t) {
        var n = a("47a9");
        a("5c38");
        n(a("3240"));
        var r = n(a("2437"));
        e.__webpack_require_UNI_MP_PLUGIN__ = a, t(r.default);
      }).call(this, a("3223")["default"], a("df3c")["createPage"]);
    }
  },
  [
    ["c40d", "common/runtime", "common/vendor"]
  ]
]);