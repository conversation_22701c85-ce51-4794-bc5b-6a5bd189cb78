$gwx_XC_16=function(_,_v,_n,_p,_s,_wp,_wl,$gwn,$gwl,$gwh,wh,$gstack,$gwrt,gra,grb,TestTest,wfor,_ca,_da,_r,_rz,_o,_oz,_1,_1z,_2,_2z,_m,_mz,nv_getDate,nv_getRegExp,nv_console,nv_parseInt,nv_parseFloat,nv_isNaN,nv_isFinite,nv_decodeURI,nv_decodeURIComponent,nv_encodeURI,nv_encodeURIComponent,$gdc,nv_JSON,_af,_gv,_ai,_grp,_gd,_gapi,$ixc,_ic,_w,_ev,_tsd){return function(path,global){
if(typeof global==='undefined'){if (typeof __GWX_GLOBAL__==='undefined')global={};else global=__GWX_GLOBAL__;}var __WXML_GLOBAL__={};{
}__WXML_GLOBAL__.modules = __WXML_GLOBAL__.modules || {};
var e_={}
if(typeof(global.entrys)==='undefined')global.entrys={};e_=global.entrys;
var d_={}
if(typeof(global.defines)==='undefined')global.defines={};d_=global.defines;
var f_={}
if(typeof(global.modules)==='undefined')global.modules={};f_=global.modules || {};
var p_={}
__WXML_GLOBAL__.ops_cached = __WXML_GLOBAL__.ops_cached || {}
__WXML_GLOBAL__.ops_set = __WXML_GLOBAL__.ops_set || {};
__WXML_GLOBAL__.ops_init = __WXML_GLOBAL__.ops_init || {};
var z=__WXML_GLOBAL__.ops_set.$gwx_XC_16 || [];
function gz$gwx_XC_16_1(){
if( __WXML_GLOBAL__.ops_cached.$gwx_XC_16_1)return __WXML_GLOBAL__.ops_cached.$gwx_XC_16_1
__WXML_GLOBAL__.ops_cached.$gwx_XC_16_1=[];
(function(z){var a=11;function Z(ops){z.push(ops)}
Z([a,[3,'weui-navigation-bar__inner '],[[2,'?:'],[[7],[3,'ios']],[1,'ios'],[1,'android']]])
Z([a,[3,'padding-top: '],[[7],[3,'statusBarHeight']],[3,'px; color: '],[[7],[3,'color']],[3,';background: '],[[7],[3,'background']],[3,';'],[[7],[3,'displayStyle']],[3,';'],[[7],[3,'innerPaddingRight']],[3,';'],[[7],[3,'innerWidth']],[3,';']])
Z([3,'weui-navigation-bar__left'])
Z([[7],[3,'leftWidth']])
Z([[7],[3,'back']])
Z([3,'left'])
Z([3,'weui-navigation-bar__center'])
Z([[7],[3,'loading']])
Z([[7],[3,'title']])
Z([3,'center'])
Z([3,'right'])
})(__WXML_GLOBAL__.ops_cached.$gwx_XC_16_1);return __WXML_GLOBAL__.ops_cached.$gwx_XC_16_1
}
__WXML_GLOBAL__.ops_set.$gwx_XC_16=z;
__WXML_GLOBAL__.ops_init.$gwx_XC_16=true;
var x=['./miniprogram_npm/weui-miniprogram/navigation-bar/navigation-bar.wxml'];d_[x[0]]={}
var m0=function(e,s,r,gg){
var z=gz$gwx_XC_16_1()
var hKD=_mz(z,'view',['class',0,'style',1],[],e,s,gg)
var oLD=_mz(z,'view',['class',2,'style',1],[],e,s,gg)
var cMD=_v()
_(oLD,cMD)
if(_oz(z,4,e,s,gg)){cMD.wxVkey=1
}
else{cMD.wxVkey=2
var oND=_n('slot')
_rz(z,oND,'name',5,e,s,gg)
_(cMD,oND)
}
cMD.wxXCkey=1
_(hKD,oLD)
var lOD=_n('view')
_rz(z,lOD,'class',6,e,s,gg)
var aPD=_v()
_(lOD,aPD)
if(_oz(z,7,e,s,gg)){aPD.wxVkey=1
}
var tQD=_v()
_(lOD,tQD)
if(_oz(z,8,e,s,gg)){tQD.wxVkey=1
}
else{tQD.wxVkey=2
var eRD=_n('slot')
_rz(z,eRD,'name',9,e,s,gg)
_(tQD,eRD)
}
aPD.wxXCkey=1
tQD.wxXCkey=1
_(hKD,lOD)
var bSD=_n('slot')
_rz(z,bSD,'name',10,e,s,gg)
_(hKD,bSD)
_(r,hKD)
return r
}
e_[x[0]]=e_[x[0]]||{f:m0,j:[],i:[],ti:[],ic:[]}
if(path&&e_[path]){
return function(env,dd,global){$gwxc=0;var root={"tag":"wx-page"};root.children=[]
;g="$gwx_XC_16";var main=e_[path].f
if (typeof global==="undefined")global={};global.f=$gdc(f_[path],"",1);
try{
main(env,{},root,global);
_tsd(root)
}catch(err){
console.log(err)
}
;g="";
return root;
}
}
}
}(__g.a,__g.b,__g.c,__g.d,__g.e,__g.f,__g.g,__g.h,__g.i,__g.j,__g.k,__g.l,__g.m,__g.n,__g.o,__g.p,__g.q,__g.r,__g.s,__g.t,__g.u,__g.v,__g.w,__g.x,__g.y,__g.z,__g.A,__g.B,__g.C,__g.D,__g.E,__g.F,__g.G,__g.H,__g.I,__g.J,__g.K,__g.L,__g.M,__g.N,__g.O,__g.P,__g.Q,__g.R,__g.S,__g.T,__g.U,__g.V,__g.W,__g.X,__g.Y,__g.Z,__g.aa);if(__vd_version_info__.delayedGwx||false)$gwx_XC_16();	if (__vd_version_info__.delayedGwx) __wxAppCode__['miniprogram_npm/weui-miniprogram/navigation-bar/navigation-bar.wxml'] = [$gwx_XC_16, './miniprogram_npm/weui-miniprogram/navigation-bar/navigation-bar.wxml'];else __wxAppCode__['miniprogram_npm/weui-miniprogram/navigation-bar/navigation-bar.wxml'] = $gwx_XC_16( './miniprogram_npm/weui-miniprogram/navigation-bar/navigation-bar.wxml' );
	;__wxRoute = "miniprogram_npm/weui-miniprogram/navigation-bar/navigation-bar";__wxRouteBegin = true;__wxAppCurrentFile__="miniprogram_npm/weui-miniprogram/navigation-bar/navigation-bar.js";define("miniprogram_npm/weui-miniprogram/navigation-bar/navigation-bar.js",function(require,module,exports,window,document,frames,self,location,navigator,localStorage,history,Caches,screen,alert,confirm,prompt,XMLHttpRequest,WebSocket,Reporter,webkit,WeixinJSCore){
"use strict";module.exports=require("../_commons/0.js")([{ids:[17],modules:{3:function(t,e,a){t.exports=a(58)},58:function(t,e){Component({options:{multipleSlots:!0,addGlobalClass:!0},properties:{extClass:{type:String,value:""},title:{type:String,value:""},background:{type:String,value:""},color:{type:String,value:""},back:{type:Boolean,value:!0},loading:{type:Boolean,value:!1},animated:{type:Boolean,value:!0},show:{type:Boolean,value:!0,observer:"_showChange"},delta:{type:Number,value:1}},data:{displayStyle:""},attached:function(){var t=this,e=!!wx.getMenuButtonBoundingClientRect,a=wx.getMenuButtonBoundingClientRect?wx.getMenuButtonBoundingClientRect():null;wx.getSystemInfo({success:function(n){var i=!!(n.system.toLowerCase().search("ios")+1);t.setData({ios:i,statusBarHeight:n.statusBarHeight,innerWidth:e?"width:".concat(a.left,"px"):"",innerPaddingRight:e?"padding-right:".concat(n.windowWidth-a.left,"px"):"",leftWidth:e?"width:".concat(n.windowWidth-a.left,"px"):""})}})},methods:{_showChange:function(t){var e="";e=this.data.animated?"opacity: ".concat(t?"1":"0",";-webkit-transition:opacity 0.5s;transition:opacity 0.5s;"):"display: ".concat(t?"":"none"),this.setData({displayStyle:e})},back:function(){var t=this.data;t.delta&&wx.navigateBack({delta:t.delta}),this.triggerEvent("back",{delta:t.delta},{})}}})}},entries:[[3,0]]}]);
},{isPage:false,isComponent:true,currentFile:'miniprogram_npm/weui-miniprogram/navigation-bar/navigation-bar.js'});require("miniprogram_npm/weui-miniprogram/navigation-bar/navigation-bar.js");