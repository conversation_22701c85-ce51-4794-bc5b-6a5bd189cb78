<view class="{{['comp_rights_wrap','data-v-56065417','wrap-bg-'+grade]}}">
    <scroll-view class="data-v-56065417" scrollX="{{true}}" showScrollbar="false">
        <view class="{{['comp_rights','flex-row','flex-wrap','data-v-56065417',!showAll?'comp_rights_collape':'']}}" style="display:flex;flex-wrap:nowrap;">
            <view bindtap="__e" class="comp_rights__item data-v-56065417" data-event-opts="{{[ [ 'tap',[ [ 'clickNav',['$0',index],[ [ ['benefits','',index] ] ] ] ] ] ]}}" openType="getUserInfo" wx:for="{{benefits}}" wx:for-item="nav" wx:key="index">
                <view class="flex-col row-center col-center data-v-56065417" style="width:160rpx;position:relative;">
                    <view class="comp_rights_lock data-v-56065417" wx:if="{{nav.level>grade&&token}}">待解锁</view>
                    <image alt class="comp_rights__icon data-v-56065417" src="{{baseUrl+'icon_'+nav.icon+'_'+grade+'.png'}}"></image>
                    <view class="comp_rights__name data-v-56065417" style="{{'color:'+(grade==5?'#fff':'#000')}}">{{nav.text}}</view>
                </view>
            </view>
        </view>
    </scroll-view>
</view>
