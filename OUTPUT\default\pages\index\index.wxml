<comp-page bind:__l="__l" class="data-v-4fd79b5a vue-ref" data-ref="page" isBrand="{{false}}" navbarColor="{{currentLevel===5?'#fff':'#000'}}" navbarIsBack="{{false}}" navbarIsTransparent="{{true}}" navbarTitle="创维会员中心" vueId="8dd740cc-1" vueSlots="{{['default']}}">
    <view class="myauth-container _div data-v-4fd79b5a">
        <view class="{{['_div','data-v-4fd79b5a','man-back-bg man-back-'+n+1]}}" style="{{'opacity:'+(currentLevel===n+1&&profile?1:0)+';'+'z-index:'+0+';'+'pointer-events:'+'none'+';'}}" wx:for="{{5}}" wx:for-index="__i0__" wx:for-item="n" wx:key="*this"></view>
        <view class="man-back-bg base-bg _div data-v-4fd79b5a" wx:if="{{!profile}}"></view>
        <view class="content-area _div data-v-4fd79b5a" style="{{'padding-top:'+topGap+'px'+';'}}">
            <comp-button bind:__l="__l" class="data-v-4fd79b5a" openType="getUserInfo" vueId="{{'8dd740cc-2'+','+'8dd740cc-1'}}" vueSlots="{{['default']}}">
                <view class="img-name _div data-v-4fd79b5a" style="margin-left:24rpx;margin-top:64rpx;" wx:if="{{!profile}}">
                    <image alt="avatar" class="avatar _img data-v-4fd79b5a" src="https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/avatar-default.png"></image>
                    <view class="_div data-v-4fd79b5a" style="color:#fff;font-size:40rpx;font-weight:800;">点击登录/注册</view>
                </view>
            </comp-button>
            <swiper bindchange="__e" class="data-v-4fd79b5a" current="{{currentLevel-1}}" data-event-opts="{{[ [ 'change',[ [ 'changeCard',['$event'] ] ] ] ]}}" style="{{'height:'+420+'rpx'+';'}}" wx:if="{{profile}}">
                <swiper-item class="data-v-4fd79b5a" style="width:100%;" wx:for="{{levelList}}" wx:key="index">
                    <view class="member-info _div data-v-4fd79b5a">
                        <view class="member-header _div data-v-4fd79b5a">
                            <view class="img-name _div data-v-4fd79b5a">
                                <image alt="avatar" class="avatar data-v-4fd79b5a" mode="aspectFill" src="{{profile.avatar}}"></image>
                                <view class="{{['_div','data-v-4fd79b5a','level-'+item.id,'level']}}">{{item.name}}</view>
                            </view>
                            <view class="info _div data-v-4fd79b5a">
                                <view class="{{['_div','data-v-4fd79b5a','growth-'+item.id,'growth']}}">{{(profile.grade>=currentLevel?'当前成长值'+profile.growth:'完成任务可升级')+''}}</view>
                                <view bindtap="__e" class="{{['_div','data-v-4fd79b5a','upgrade-btn-'+item.id,'upgrade-btn']}}" data-event-opts="{{[ [ 'tap',[ [ 'toGetGrowth',['$event'] ] ] ] ]}}">去提升</view>
                            </view>
                        </view>
                        <view class="wangzuo _div data-v-4fd79b5a" wx:if="{{profile}}">
                            <image alt="宝石" class="baoshi _img data-v-4fd79b5a" src="{{'https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/pic_bs_'+item.id+'.png'}}"></image>
                            <image alt="底座" class="dizuo _img data-v-4fd79b5a" src="{{'https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/pic_dz_'+item.id+'.png'}}"></image>
                        </view>
                    </view>
                    <image alt class="level-img _img data-v-4fd79b5a" src="{{'https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/img_gl_'+item.id+'.png'}}"></image>
                </swiper-item>
            </swiper>
            <view class="member-benefits _div data-v-4fd79b5a" style="{{'background-color:'+(currentLevel==5?'rgba(0, 0, 0, 0.3)':'rgba(255, 255, 255, 0.3)')+';'}}">
                <view class="benefits-title _div data-v-4fd79b5a">
                    <span class="data-v-4fd79b5a" style="{{'color:'+(currentLevel==5?'#fff':'#000000cc')+';'}}">会员权益</span>
                    <span bindtap="__e" class="more data-v-4fd79b5a" data-event-opts="{{[ [ 'tap',[ [ 'toMore',['$event'] ] ] ] ]}}" style="{{'color:'+(currentLevel==5?'rgba(255, 255, 255, 0.3)':'rgba(0, 0, 0, 0.3)')+';'}}">更多<comp-icon bind:__l="__l" class="cell__arrow data-v-4fd79b5a" color="{{$root.a0}}" icon="iconarrow_right_o" size="24rpx" vueId="{{'8dd740cc-3'+','+'8dd740cc-1'}}"></comp-icon>
                    </span>
                </view>
                <view class="benefits-list _div data-v-4fd79b5a">
                    <view bindtap="__e" class="benefit-item _div data-v-4fd79b5a" data-event-opts="{{[ [ 'tap',[ [ 'toAuthDetail',[idx] ] ] ] ]}}" wx:for="{{benefits}}" wx:for-index="idx" wx:key="idx">
                        <image class="benefit-icon _img data-v-4fd79b5a" src="{{'https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/icon_'+item.icon+'_'+currentLevel+'.png'}}"></image>
                        <view class="benefit-lock _div data-v-4fd79b5a" wx:if="{{item.level>currentLevel&&profile}}">待解锁</view>
                        <view class="benefit-text _div data-v-4fd79b5a" style="{{'color:'+(currentLevel==5?'#fff':'#000000cc')+';'}}">{{item.text+''}}</view>
                    </view>
                </view>
            </view>
            <view class="upgrade-card-section _div data-v-4fd79b5a">
                <view class="upgrade-card-section-title _div data-v-4fd79b5a">权益升级</view>
                <view class="upgrade-card-box _div data-v-4fd79b5a">
                    <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-4fd79b5a" data-event-opts="{{[ [ '^getUserInfo',[ ['getWd'] ] ] ]}}" openType="getUserInfo" style="flex:1;" vueId="{{'8dd740cc-4'+','+'8dd740cc-1'}}" vueSlots="{{['default']}}">
                        <view class="upgrade-card left _div data-v-4fd79b5a">
                            <view class="upgrade-main _div data-v-4fd79b5a">
                                <view class="upgrade-title blue _div data-v-4fd79b5a">赚维豆</view>
                                <view class="upgrade-desc _div data-v-4fd79b5a">可以当钱花</view>
                            </view>
                        </view>
                    </comp-button>
                    <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-4fd79b5a" data-event-opts="{{[ [ '^getUserInfo',[ ['getCzz'] ] ] ]}}" openType="getUserInfo" style="flex:1;" vueId="{{'8dd740cc-5'+','+'8dd740cc-1'}}" vueSlots="{{['default']}}">
                        <view class="upgrade-card right _div data-v-4fd79b5a">
                            <view class="upgrade-main _div data-v-4fd79b5a">
                                <view class="upgrade-title red _div data-v-4fd79b5a">赚成长值</view>
                                <view class="upgrade-desc _div data-v-4fd79b5a">解锁权益</view>
                            </view>
                        </view>
                    </comp-button>
                </view>
            </view>
            <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-4fd79b5a" data-event-opts="{{[ [ '^getUserInfo',[ ['jumpMore'] ] ] ]}}" openType="getUserInfo" vueId="{{'8dd740cc-6'+','+'8dd740cc-1'}}" vueSlots="{{['default']}}">
                <view class="gift-section _div data-v-4fd79b5a" wx:if="{{adData[0]}}">
                    <view class="gift-title _div data-v-4fd79b5a">维豆兑好礼</view>
                    <view class="banner-section _div data-v-4fd79b5a">
                        <image alt="banner" class="banner-img data-v-4fd79b5a" mode="aspectFill" src="{{adData[0].img}}"></image>
                    </view>
                </view>
            </comp-button>
            <comp-recommand bind:__l="__l" bind:clickMore="__e" class="data-v-4fd79b5a" data-event-opts="{{[ [ '^clickMore',[ ['jumpMore'] ] ] ]}}" list="{{recommandList}}" vueId="{{'8dd740cc-7'+','+'8dd740cc-1'}}" wx:if="{{$root.g0}}"></comp-recommand>
            <view class="_div data-v-4fd79b5a" style="height:150rpx;"></view>
        </view>
    </view>
</comp-page>
