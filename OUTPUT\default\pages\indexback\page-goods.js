(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/indexback/page-goods"], {
    "1c85": function c85(e, n, t) {},
    "1c96": function c96(e, n, t) {


      var o = t("1c85"),
        c = t.n(o);
      c.a;
    },
    "3ce5": function ce5(e, n, t) {


      t.r(n);
      var o = t("6ba4"),
        c = t("8bb2");
      for (var r in c)["default"].indexOf(r) < 0 && function(e) {
        t.d(n, e, function() {
          return c[e];
        });
      }(r);
      t("1c96");
      var u = t("828b"),
        a = Object(u["a"])(c["default"], o["b"], o["c"], !1, null, "48650d30", null, !1, o["a"], void 0);
      n["default"] = a.exports;
    },
    "6ba4": function ba4(e, n, t) {


      t.d(n, "b", function() {
        return c;
      }), t.d(n, "c", function() {
        return r;
      }), t.d(n, "a", function() {
        return o;
      });
      var o = {
          compButton: function compButton() {
            return Promise.all([t.e("common/vendor"), t.e("components/comp-button/comp-button")]).then(t.bind(null, "ca5a"));
          },
          compImage: function compImage() {
            return Promise.all([t.e("common/vendor"), t.e("components/comp-image/comp-image")]).then(t.bind(null, "31bf"));
          }
        },
        c = function c() {
          var e = this.$createElement;
          this._self._c;
        },
        r = [];
    },
    "8bb2": function bb2(e, n, t) {


      t.r(n);
      var o = t("ab84"),
        c = t.n(o);
      for (var r in o)["default"].indexOf(r) < 0 && function(e) {
        t.d(n, e, function() {
          return o[e];
        });
      }(r);
      n["default"] = c.a;
    },
    ab84: function ab84(e, n, t) {


      var o = t("47a9");
      Object.defineProperty(n, "__esModule", {
        value: !0
      }), n.default = void 0;
      var c = o(t("7eb4")),
        r = o(t("ee10")),
        u = (o(t("8b9c")), o(t("a23b")), o(t("5e82"))),
        a = {
          components: {
            PagePanel: function PagePanel() {
              t.e("pages/indexback/page-panel").then(function() {
                return resolve(t("f4ec"));
              }.bind(null, t)).catch(t.oe);
            }
          },
          props: {
            goodsTokenUrl: {
              type: String,
              default: ""
            },
            list: {
              type: Array,
              default: function _default() {
                return [];
              }
            }
          },
          methods: {
            clickMore: function clickMore() {
              u.default.toWebviewByTokenUrl({
                tokenUrl: this.goodsTokenUrl
              });
            },
            clickItem: function clickItem(e) {
              return (0, r.default)(c.default.mark(function n() {
                return c.default.wrap(function(n) {
                  while (1) switch (n.prev = n.next) {
                    case 0:
                      u.default.toWebviewByTokenUrl({
                        tokenUrl: e.tokenUrl
                      });
                    case 1:
                    case "end":
                      return n.stop();
                  }
                }, n);
              }))();
            }
          }
        };
      n.default = a;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/indexback/page-goods-create-component', {
    'pages/indexback/page-goods-create-component': function pagesIndexbackPageGoodsCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("3ce5"));
    }
  },
  [
    ['pages/indexback/page-goods-create-component']
  ]
]);