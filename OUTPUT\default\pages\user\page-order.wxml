<view class="order-panel data-v-18fd57ba">
    <view class="order-header data-v-18fd57ba">
        <view class="order-title data-v-18fd57ba">我的订单</view>
        <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-18fd57ba" data-event-opts="{{[ [ '^getUserInfo',[ ['toAllOrder'] ] ] ]}}" openType="getUserInfo" vueId="c66bbf0a-1" vueSlots="{{['default']}}">
            <view class="order-to data-v-18fd57ba">
                <view class="order-all data-v-18fd57ba">全部订单</view>
                <comp-icon bind:__l="__l" class="data-v-18fd57ba" color="rgb(181, 181, 181)" icon="iconarrow_right_o" size="24rpx" vueId="{{'c66bbf0a-2'+','+'c66bbf0a-1'}}"></comp-icon>
            </view>
        </comp-button>
    </view>
    <view class="order-list data-v-18fd57ba">
        <view bindtap="__e" class="order-item data-v-18fd57ba" data-event-opts="{{[ [ 'tap',[ [ 'onOrderType',['$0'],[ [ ['orderTypes','key',item[$orig].key] ] ] ] ] ] ]}}" wx:for="{{$root.l0}}" wx:for-index="__i0__" wx:key="key">
            <view class="order-icon-box data-v-18fd57ba">
                <image class="order-icon data-v-18fd57ba" src="{{item[$orig].icon}}"></image>
                <view class="order-badge data-v-18fd57ba" wx:if="{{item.m0}}">{{''+(item.m1>99?'99+':item.m2)+''}}</view>
            </view>
            <view class="order-label data-v-18fd57ba">{{item[$orig].label}}</view>
        </view>
    </view>
</view>
