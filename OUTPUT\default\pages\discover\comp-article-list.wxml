<view class="article-row data-v-b223e146">
    <comp-button bind:__l="__l" bind:onClick="__e" class="data-v-b223e146" data-event-opts="{{[ [ '^onClick',[ [ 'clickArticle',['$0'],[ [ ['list','',index] ] ] ] ] ] ]}}" vueId="{{'780344a0-1-'+index}}" vueSlots="{{['default']}}" wx:for="{{$root.l0}}" wx:key="index">
        <view class="act-card data-v-b223e146">
            <view class="left-box data-v-b223e146">
                <view class="title data-v-b223e146">{{item[$orig].name}}</view>
                <view class="sub-title data-v-b223e146">{{item[$orig].articleDesc}}</view>
                <view class="btn data-v-b223e146">立即查看</view>
            </view>
            <view class="right-box data-v-b223e146">
                <view class="pic-wrap data-v-b223e146" wx:if="{{item.g0}}">
                    <comp-image bind:__l="__l" class="data-v-b223e146" height="148rpx" isCustomSrc="{{true}}" url="{{item[$orig].imgs[0].img}}" vueId="{{'780344a0-2-'+index+','+'780344a0-1-'+index}}" width="198rpx" wx:if="{{item[$orig].imgs[0]}}"></comp-image>
                </view>
            </view>
        </view>
    </comp-button>
</view>
