(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/user/page-product"], {
    "4c51": function c51(e, t, n) {},
    "617f": function f(e, t, n) {


      var r = n("4c51"),
        o = n.n(r);
      o.a;
    },
    6488: function _(e, t, n) {


      n.r(t);
      var r = n("e899"),
        o = n("81c6");
      for (var u in o)["default"].indexOf(u) < 0 && function(e) {
        n.d(t, e, function() {
          return o[e];
        });
      }(u);
      n("617f");
      var c = n("828b"),
        i = Object(c["a"])(o["default"], r["b"], r["c"], !1, null, "07af3e95", null, !1, r["a"], void 0);
      t["default"] = i.exports;
    },
    "81c6": function c6(e, t, n) {


      n.r(t);
      var r = n("b30e"),
        o = n.n(r);
      for (var u in r)["default"].indexOf(u) < 0 && function(e) {
        n.d(t, e, function() {
          return r[e];
        });
      }(u);
      t["default"] = o.a;
    },
    b30e: function b30e(e, t, n) {


      (function(e) {
        var r = n("47a9");
        Object.defineProperty(t, "__esModule", {
          value: !0
        }), t.default = void 0;
        var o = r(n("7eb4")),
          u = r(n("ee10")),
          c = r(n("7ca3")),
          i = n("8f59"),
          a = r(n("8b9c")),
          l = r(n("a23b")),
          f = r(n("5e82")),
          s = n("b3c5");

        function p(e, t) {
          var n = Object.keys(e);
          if (Object.getOwnPropertySymbols) {
            var r = Object.getOwnPropertySymbols(e);
            t && (r = r.filter(function(t) {
              return Object.getOwnPropertyDescriptor(e, t).enumerable;
            })), n.push.apply(n, r);
          }
          return n;
        }

        function d(e) {
          for (var t = 1; t < arguments.length; t++) {
            var n = null != arguments[t] ? arguments[t] : {};
            t % 2 ? p(Object(n), !0).forEach(function(t) {
              (0, c.default)(e, t, n[t]);
            }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : p(Object(n)).forEach(function(t) {
              Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
            });
          }
          return e;
        }
        var b = (0, s.getConfig)(),
          g = {
            data: function data() {
              return {
                count: 0
              };
            },
            computed: d(d({}, (0, i.mapState)(["profile"])), {}, {
              list: function list() {
                return this.profile ? this.profile.product.slice(0, 6) : [];
              },
              btns: function btns() {
                if (!this.profile) return [];
                var e = [{
                  id: "dzsms",
                  icon: "iconmanual_o",
                  name: "电视系统说明书",
                  url: b.instructionBookUrl
                }, {
                  id: "dzbxk",
                  icon: "iconwarranty_o",
                  name: "电子保修卡"
                }, {
                  id: "shfw",
                  icon: "iconhelp_o",
                  name: "售后服务",
                  url: "/pages/service/proxy"
                }];
                return this.profile.isCanUploadInvoice || e.unshift({
                  id: "wjgl",
                  icon: "iconraiders_o",
                  name: "玩机攻略",
                  url: "/pages-community/community/community?tabLabel=fun"
                }), e;
              }
            }),
            methods: {
              clickMore: function clickMore() {
                f.default.to({
                  page: "productList"
                });
              },
              clickAdd: function clickAdd() {
                f.default.to({
                  page: "productAdd"
                });
              },
              clickProduct: function clickProduct() {
                f.default.to({
                  page: "productList"
                });
              },
              clickBtn: function clickBtn(e, t) {
                return (0, u.default)(o.default.mark(function n() {
                  var r, u;
                  return o.default.wrap(function(n) {
                    while (1) switch (n.prev = n.next) {
                      case 0:
                        if ("dzsms" !== e.id) {
                          n.next = 3;
                          break;
                        }
                        return f.default.to({
                          page: "webviewH5",
                          query: {
                            url: b.instructionBookUrl
                          }
                        }), n.abrupt("return");
                      case 3:
                        if (!e.url) {
                          n.next = 6;
                          break;
                        }
                        return f.default.to({
                          fullPath: e.url
                        }), n.abrupt("return");
                      case 6:
                        if ("scfp" !== e.id) {
                          n.next = 16;
                          break;
                        }
                        return n.next = 9, a.default.invoiceList({
                          page: 1,
                          pageSize: 20
                        });
                      case 9:
                        if (r = n.sent, u = l.default.get(r, "data.data.list", []) || [], !(u.length > 0)) {
                          n.next = 14;
                          break;
                        }
                        return f.default.to({
                          page: "commonSimpleList",
                          query: {
                            type: "invoice"
                          }
                        }), n.abrupt("return");
                      case 14:
                        return f.default.to({
                          page: "invoiceAdd"
                        }), n.abrupt("return");
                      case 16:
                        if ("dzbxk" !== e.id) {
                          n.next = 22;
                          break;
                        }
                        if (!t.no) {
                          n.next = 20;
                          break;
                        }
                        return f.default.to({
                          page: "guaranteeRedirect",
                          query: {
                            sn: t.no,
                            guaranteeDeviceBack: 1
                          }
                        }), n.abrupt("return");
                      case 20:
                        return f.default.to({
                          page: "guaranteeDevice",
                          query: {
                            guaranteeDeviceBack: 1
                          }
                        }), n.abrupt("return");
                      case 22:
                      case "end":
                        return n.stop();
                    }
                  }, n);
                }))();
              },
              openDebug: function openDebug() {
                this.count++, 20 === this.count && (this.count = 0, e.setEnableDebug({
                  enableDebug: !0
                }));
              },
              openDevelop: function openDevelop() {
                "prod" !== b.env && f.default.to({
                  page: "commonDevelop"
                });
              }
            }
          };
        t.default = g;
      }).call(this, n("df3c")["default"]);
    },
    e899: function e899(e, t, n) {


      n.d(t, "b", function() {
        return o;
      }), n.d(t, "c", function() {
        return u;
      }), n.d(t, "a", function() {
        return r;
      });
      var r = {
          compIcon: function compIcon() {
            return n.e("components/comp-icon/comp-icon").then(n.bind(null, "84bb2"));
          },
          compButton: function compButton() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-button/comp-button")]).then(n.bind(null, "ca5a"));
          },
          compImage: function compImage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-image/comp-image")]).then(n.bind(null, "31bf"));
          }
        },
        o = function o() {
          var e = this,
            t = e.$createElement,
            n = (e._self._c, e.list.length),
            r = e.list.length,
            o = r ? e.list.length : null,
            u = r ? e.__map(e.list, function(t, n) {
              var r = e.__get_orig(t),
                o = e.list.length;
              return {
                $orig: r,
                g3: o
              };
            }) : null;
          e.$mp.data = Object.assign({}, {
            $root: {
              g0: n,
              g1: r,
              g2: o,
              l0: u
            }
          });
        },
        u = [];
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/user/page-product-create-component', {
    'pages/user/page-product-create-component': function pagesUserPageProductCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("6488"));
    }
  },
  [
    ['pages/user/page-product-create-component']
  ]
]);