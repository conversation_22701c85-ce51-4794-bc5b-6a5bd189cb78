(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/indexback/page-ad"], {
    "111d": function d(n, e, t) {


      t.r(e);
      var r = t("e66e"),
        u = t.n(r);
      for (var a in r)["default"].indexOf(a) < 0 && function(n) {
        t.d(e, n, function() {
          return r[n];
        });
      }(a);
      e["default"] = u.a;
    },
    "80f1": function f1(n, e, t) {


      t.d(e, "b", function() {
        return u;
      }), t.d(e, "c", function() {
        return a;
      }), t.d(e, "a", function() {
        return r;
      });
      var r = {
          compSwiper: function compSwiper() {
            return t.e("components/comp-swiper/comp-swiper").then(t.bind(null, "8ae3"));
          }
        },
        u = function u() {
          var n = this.$createElement;
          this._self._c;
        },
        a = [];
    },
    "872a": function a(n, e, t) {},
    9306: function _(n, e, t) {


      var r = t("872a"),
        u = t.n(r);
      u.a;
    },
    b466: function b466(n, e, t) {


      t.r(e);
      var r = t("80f1"),
        u = t("111d");
      for (var a in u)["default"].indexOf(a) < 0 && function(n) {
        t.d(e, n, function() {
          return u[n];
        });
      }(a);
      t("9306");
      var i = t("828b"),
        c = Object(i["a"])(u["default"], r["b"], r["c"], !1, null, "1fc0995b", null, !1, r["a"], void 0);
      e["default"] = c.exports;
    },
    e66e: function e66e(n, e, t) {


      Object.defineProperty(e, "__esModule", {
        value: !0
      }), e.default = void 0;
      var r = {
        props: {
          list: {
            type: Array,
            default: function _default() {
              return [];
            }
          }
        }
      };
      e.default = r;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/indexback/page-ad-create-component', {
    'pages/indexback/page-ad-create-component': function pagesIndexbackPageAdCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("b466"));
    }
  },
  [
    ['pages/indexback/page-ad-create-component']
  ]
]);