{"component": true, "usingComponents": {"comp-button": "components\\comp-swiper\\components\\comp-button\\comp-button", "comp-image": "components\\comp-swiper\\components\\comp-image\\comp-image", "duiba-pay": "components\\comp-swiper\\wxcomponents\\duiba-miniprogram-pay\\index", "painter": "components\\comp-swiper\\wxcomponents\\painter\\painter", "mp-action-sheet": "components\\comp-swiper\\miniprogram_npm\\weui-miniprogram\\actionsheet\\actionsheet", "mp-cell": "components\\comp-swiper\\miniprogram_npm\\weui-miniprogram\\cell\\cell", "mp-cells": "components\\comp-swiper\\miniprogram_npm\\weui-miniprogram\\cells\\cells", "mp-emoji": "components\\comp-swiper\\wxcomponents\\emoji\\index", "mp-half-screen-dialog": "components\\comp-swiper\\miniprogram_npm\\weui-miniprogram\\half-screen-dialog\\half-screen-dialog", "mp-icon": "components\\comp-swiper\\miniprogram_npm\\weui-miniprogram\\icon\\icon", "mp-loading": "components\\comp-swiper\\miniprogram_npm\\weui-miniprogram\\loading\\loading", "mp-navigation-bar": "components\\comp-swiper\\miniprogram_npm\\weui-miniprogram\\navigation-bar\\navigation-bar", "mp-slideview": "components\\comp-swiper\\miniprogram_npm\\weui-miniprogram\\slideview\\slideview", "mi-lin-video": ".\\components\\comp-swiper\\plugin:\\wx8d04ad0492dab24d\\miLin-video"}}