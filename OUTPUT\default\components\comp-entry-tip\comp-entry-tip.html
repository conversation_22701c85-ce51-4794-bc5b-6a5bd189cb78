	<style> </style> 	<page></page> 	<script> 	var __setCssStartTime__ = Date.now();			__wxAppCode__['components/comp-entry-tip/comp-entry-tip.wxss']();	
	var __setCssEndTime__ = Date.now();	(function() { 		var gf = $gwx( './components/comp-entry-tip/comp-entry-tip.wxml' ); 		if (window.__wxAppCodeReadyCallback__) { 			window.__wxAppCodeReadyCallback__(gf); 		} else { 			document.dispatchEvent(new CustomEvent( "generateFuncReady", { 				detail: { 					generateFunc: gf 			}})); 		} 	})(); 	</script> 	 