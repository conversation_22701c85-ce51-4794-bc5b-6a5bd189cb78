<view class="comp-big-pic-card data-v-2026a632">
    <view bindtap="__e" class="is-card--10 data-v-2026a632" data-event-opts="{{[ [ 'tap',[ [ 'clickCard',['$event'] ] ] ] ]}}" wx:if="{{type===10}}">
        <view catchtap="__e" class="card__hd data-v-2026a632" data-event-opts="{{[ [ 'tap',[ [ 'clickCardHd',['$event'] ] ] ] ]}}">
            <comp-image bind:__l="__l" class="data-v-2026a632" height="502rpx" isServer="{{true}}" isVideoCover="{{isVideo}}" name="{{imgs[0].img}}" radius="16rpx" vueId="44baa944-1" width="670rpx"></comp-image>
        </view>
        <view class="card__bd data-v-2026a632">
            <view class="card__name multi-nowrap data-v-2026a632">{{name}}</view>
        </view>
        <view class="card__ft flex-row col-center data-v-2026a632">
            <view class="flex-row data-v-2026a632">
                <view class="card__like flex-row col-center data-v-2026a632">
                    <view class="card__count data-v-2026a632">
                        <text class="data-v-2026a632">{{viewCount||0}}</text>
                        <text class="spacing data-v-2026a632">阅读量</text>
                        <text class="data-v-2026a632">{{likedCount||0}}</text>
                        <text class="spacing data-v-2026a632">赞</text>
                    </view>
                </view>
            </view>
            <view class="flex-one flex-row row-end data-v-2026a632">
                <view class="card__time data-v-2026a632" wx:if="{{time}}">{{time}}</view>
                <view class="card__tag flex-row row-center col-center data-v-2026a632" wx:if="{{item.g0}}" wx:for="{{$root.l0}}" wx:for-index="i" wx:key="i">{{''+item[$orig].name+''}}</view>
            </view>
        </view>
    </view>
</view>
