(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["pages/discover/comp-article-list"], {
    "099c": function c(e, t, n) {


      var r = n("8e12"),
        o = n.n(r);
      o.a;
    },
    6182: function _(e, t, n) {


      n.r(t);
      var r = n("c1e5"),
        o = n("b28a");
      for (var c in o)["default"].indexOf(c) < 0 && function(e) {
        n.d(t, e, function() {
          return o[e];
        });
      }(c);
      n("099c");
      var u = n("828b"),
        a = Object(u["a"])(o["default"], r["b"], r["c"], !1, null, "b223e146", null, !1, r["a"], void 0);
      t["default"] = a.exports;
    },
    "8e12": function e12(e, t, n) {},
    b28a: function b28a(e, t, n) {


      n.r(t);
      var r = n("fc72"),
        o = n.n(r);
      for (var c in r)["default"].indexOf(c) < 0 && function(e) {
        n.d(t, e, function() {
          return r[e];
        });
      }(c);
      t["default"] = o.a;
    },
    c1e5: function c1e5(e, t, n) {


      n.d(t, "b", function() {
        return o;
      }), n.d(t, "c", function() {
        return c;
      }), n.d(t, "a", function() {
        return r;
      });
      var r = {
          compButton: function compButton() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-button/comp-button")]).then(n.bind(null, "ca5a"));
          },
          compImage: function compImage() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-image/comp-image")]).then(n.bind(null, "31bf"));
          }
        },
        o = function o() {
          var e = this,
            t = e.$createElement,
            n = (e._self._c, e.__map(e.list, function(t, n) {
              var r = e.__get_orig(t),
                o = t.imgs.length;
              return {
                $orig: r,
                g0: o
              };
            }));
          e.$mp.data = Object.assign({}, {
            $root: {
              l0: n
            }
          });
        },
        c = [];
    },
    fc72: function fc72(e, t, n) {


      var r = n("47a9");
      Object.defineProperty(t, "__esModule", {
        value: !0
      }), t.default = void 0;
      var o = r(n("7eb4")),
        c = r(n("ee10")),
        u = r(n("5e82")),
        a = {
          components: {},
          props: {
            list: {
              type: Array,
              default: function _default() {
                return [];
              }
            }
          },
          data: function data() {
            return {};
          },
          methods: {
            clickArticle: function clickArticle(e) {
              return (0, c.default)(o.default.mark(function t() {
                return o.default.wrap(function(t) {
                  while (1) switch (t.prev = t.next) {
                    case 0:
                      u.default.to({
                        page: 1 === e.type ? "article" : "video",
                        query: {
                          id: e.id
                        }
                      });
                    case 1:
                    case "end":
                      return t.stop();
                  }
                }, t);
              }))();
            }
          }
        };
      t.default = a;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['pages/discover/comp-article-list-create-component', {
    'pages/discover/comp-article-list-create-component': function pagesDiscoverCompArticleListCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("6182"));
    }
  },
  [
    ['pages/discover/comp-article-list-create-component']
  ]
]);