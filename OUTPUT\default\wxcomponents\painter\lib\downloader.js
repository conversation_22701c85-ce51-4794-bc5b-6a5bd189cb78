Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _typeof2 = require("../../../@babel/runtime/helpers/typeof");
var _createForOfIteratorHelper2 = require("../../../@babel/runtime/helpers/createForOfIteratorHelper");
var _classCallCheck2 = require("../../../@babel/runtime/helpers/classCallCheck");
var _createClass2 = require("../../../@babel/runtime/helpers/createClass");
/**
 * LRU 文件存储，使用该 downloader 可以让下载的文件存储在本地，下次进入小程序后可以直接使用
 * 详细设计文档可查看 https://juejin.im/post/5b42d3ede51d4519277b6ce3
 */
var util = require('./util');
var SAVED_FILES_KEY = 'savedFiles';
var KEY_TOTAL_SIZE = 'totalSize';
var KEY_PATH = 'path';
var KEY_TIME = 'time';
var KEY_SIZE = 'size';

// 可存储总共为 6M，目前小程序可允许的最大本地存储为 10M
var MAX_SPACE_IN_B = 6 * 1024 * 1024;
var savedFiles = {};
var Dowloader = exports.default = /*#__PURE__*/ function() {
  function Dowloader() {
    _classCallCheck2(this, Dowloader);
    // app 如果设置了最大存储空间，则使用 app 中的
    if (getApp().PAINTER_MAX_LRU_SPACE) {
      MAX_SPACE_IN_B = getApp().PAINTER_MAX_LRU_SPACE;
    }
    wx.getStorage({
      key: SAVED_FILES_KEY,
      success: function success(res) {
        if (res.data) {
          savedFiles = res.data;
        }
      }
    });
  }

  /**
   * 下载文件，会用 lru 方式来缓存文件到本地
   * @param {String} url 文件的 url
   */
  return _createClass2(Dowloader, [{
    key: "download",
    value: function download(url) {
      return new Promise(function(resolve, reject) {
        if (!(url && util.isValidUrl(url))) {
          resolve(url);
          return;
        }
        var file = getFile(url);
        if (file) {
          // 检查文件是否正常，不正常需要重新下载
          wx.getSavedFileInfo({
            filePath: file[KEY_PATH],
            success: function success(res) {
              resolve(file[KEY_PATH]);
            },
            fail: function fail(error) {
              console.error("the file is broken, redownload it, ".concat(JSON.stringify(error)));
              downloadFile(url).then(function(path) {
                resolve(path);
              }, function() {
                reject();
              });
            }
          });
        } else {
          downloadFile(url).then(function(path) {
            resolve(path);
          }, function() {
            reject();
          });
        }
      });
    }
  }]);
}();

function downloadFile(url) {
  return new Promise(function(resolve, reject) {
    wx.downloadFile({
      url: url,
      success: function success(res) {
        if (res.statusCode !== 200) {
          console.error("downloadFile ".concat(url, " failed res.statusCode is not 200"));
          reject();
          return;
        }
        var tempFilePath = res.tempFilePath;
        wx.getFileInfo({
          filePath: tempFilePath,
          success: function success(tmpRes) {
            var newFileSize = tmpRes.size;
            doLru(newFileSize).then(function() {
              saveFile(url, newFileSize, tempFilePath).then(function(filePath) {
                resolve(filePath);
              });
            }, function() {
              resolve(tempFilePath);
            });
          },
          fail: function fail(error) {
            // 文件大小信息获取失败，则此文件也不要进行存储
            console.error("getFileInfo ".concat(res.tempFilePath, " failed, ").concat(JSON.stringify(error)));
            resolve(res.tempFilePath);
          }
        });
      },
      fail: function fail(error) {
        console.error("downloadFile failed, ".concat(JSON.stringify(error), " "));
        reject();
      }
    });
  });
}

function saveFile(key, newFileSize, tempFilePath) {
  return new Promise(function(resolve, reject) {
    wx.saveFile({
      tempFilePath: tempFilePath,
      success: function success(fileRes) {
        var totalSize = savedFiles[KEY_TOTAL_SIZE] ? savedFiles[KEY_TOTAL_SIZE] : 0;
        savedFiles[key] = {};
        savedFiles[key][KEY_PATH] = fileRes.savedFilePath;
        savedFiles[key][KEY_TIME] = new Date().getTime();
        savedFiles[key][KEY_SIZE] = newFileSize;
        savedFiles['totalSize'] = newFileSize + totalSize;
        wx.setStorage({
          key: SAVED_FILES_KEY,
          data: savedFiles
        });
        resolve(fileRes.savedFilePath);
      },
      fail: function fail(error) {
        console.error("saveFile ".concat(key, " failed, then we delete all files, ").concat(JSON.stringify(error)));
        // 由于 saveFile 成功后，res.tempFilePath 处的文件会被移除，所以在存储未成功时，我们还是继续使用临时文件
        resolve(tempFilePath);
        // 如果出现错误，就直接情况本地的所有文件，因为你不知道是不是因为哪次lru的某个文件未删除成功
        reset();
      }
    });
  });
}

/**
 * 清空所有下载相关内容
 */
function reset() {
  wx.removeStorage({
    key: SAVED_FILES_KEY,
    success: function success() {
      wx.getSavedFileList({
        success: function success(listRes) {
          removeFiles(listRes.fileList);
        },
        fail: function fail(getError) {
          console.error("getSavedFileList failed, ".concat(JSON.stringify(getError)));
        }
      });
    }
  });
}

function doLru(size) {
  if (size > MAX_SPACE_IN_B) {
    return Promise.reject();
  }
  return new Promise(function(resolve, reject) {
    var totalSize = savedFiles[KEY_TOTAL_SIZE] ? savedFiles[KEY_TOTAL_SIZE] : 0;
    if (size + totalSize <= MAX_SPACE_IN_B) {
      resolve();
      return;
    }
    // 如果加上新文件后大小超过最大限制，则进行 lru
    var pathsShouldDelete = [];
    // 按照最后一次的访问时间，从小到大排序
    var allFiles = JSON.parse(JSON.stringify(savedFiles));
    delete allFiles[KEY_TOTAL_SIZE];
    var sortedKeys = Object.keys(allFiles).sort(function(a, b) {
      return allFiles[a][KEY_TIME] - allFiles[b][KEY_TIME];
    });
    var _iterator = _createForOfIteratorHelper2(sortedKeys),
      _step;
    try {
      for (_iterator.s(); !(_step = _iterator.n()).done;) {
        var sortedKey = _step.value;
        totalSize -= savedFiles[sortedKey].size;
        pathsShouldDelete.push(savedFiles[sortedKey][KEY_PATH]);
        delete savedFiles[sortedKey];
        if (totalSize + size < MAX_SPACE_IN_B) {
          break;
        }
      }
    } catch (err) {
      err = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(err);
      _iterator.e(err);
    } finally {
      _iterator.f();
    }
    savedFiles['totalSize'] = totalSize;
    wx.setStorage({
      key: SAVED_FILES_KEY,
      data: savedFiles,
      success: function success() {
        // 保证 storage 中不会存在不存在的文件数据
        if (pathsShouldDelete.length > 0) {
          removeFiles(pathsShouldDelete);
        }
        resolve();
      },
      fail: function fail(error) {
        console.error("doLru setStorage failed, ".concat(JSON.stringify(error)));
        reject();
      }
    });
  });
}

function removeFiles(pathsShouldDelete) {
  var _iterator2 = _createForOfIteratorHelper2(pathsShouldDelete),
    _step2;
  try {
    var _loop = function _loop() {
      var pathDel = _step2.value;
      var delPath = pathDel;
      if (_typeof2(pathDel) === 'object') {
        delPath = pathDel.filePath;
      }
      wx.removeSavedFile({
        filePath: delPath,
        fail: function fail(error) {
          console.error("removeSavedFile ".concat(pathDel, " failed, ").concat(JSON.stringify(error)));
        }
      });
    };
    for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
      _loop();
    }
  } catch (err) {
    err = VM2_INTERNAL_STATE_DO_NOT_USE_OR_PROGRAM_WILL_FAIL.handleException(err);
    _iterator2.e(err);
  } finally {
    _iterator2.f();
  }
}

function getFile(key) {
  if (!savedFiles[key]) {
    return;
  }
  savedFiles[key]['time'] = new Date().getTime();
  wx.setStorage({
    key: SAVED_FILES_KEY,
    data: savedFiles
  });
  return savedFiles[key];
}