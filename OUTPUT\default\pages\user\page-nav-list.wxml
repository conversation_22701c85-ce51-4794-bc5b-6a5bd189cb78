<view class="user-nav-list data-v-21b573b7">
    <view class="list-item data-v-21b573b7" wx:for="{{navList}}">
        <comp-button bind:__l="__l" bind:getUserInfo="__e" class="data-v-21b573b7" data-event-opts="{{[ [ '^getUserInfo',[ [ 'handleClick',['$0'],[ [ ['navList','',index] ] ] ] ] ] ]}}" openType="getUserInfo" vueId="{{'1447e02a-1-'+index}}" vueSlots="{{['default']}}">
            <view class="item data-v-21b573b7">
                <image alt class="item-img data-v-21b573b7" src="{{''+baseUrl+item.icon+'.png'}}"></image>
                <view class="item-text data-v-21b573b7">{{item.name}}</view>
            </view>
        </comp-button>
    </view>
</view>
