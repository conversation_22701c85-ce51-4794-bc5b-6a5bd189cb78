(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["components/comp-split/comp-split"], {
    9396: function _(t, n, e) {


      e.r(n);
      var u = e("cd63"),
        c = e("c7f2");
      for (var r in c)["default"].indexOf(r) < 0 && function(t) {
        e.d(n, t, function() {
          return c[t];
        });
      }(r);
      var f = e("828b"),
        i = Object(f["a"])(c["default"], u["b"], u["c"], !1, null, null, null, !1, u["a"], void 0);
      n["default"] = i.exports;
    },
    c7f2: function c7f2(t, n, e) {


      e.r(n);
      var u = e("d87e"),
        c = e.n(u);
      for (var r in u)["default"].indexOf(r) < 0 && function(t) {
        e.d(n, t, function() {
          return u[t];
        });
      }(r);
      n["default"] = c.a;
    },
    cd63: function cd63(t, n, e) {


      e.d(n, "b", function() {
        return u;
      }), e.d(n, "c", function() {
        return c;
      }), e.d(n, "a", function() {});
      var u = function u() {
          var t = this.$createElement;
          this._self._c;
        },
        c = [];
    },
    d87e: function d87e(t, n, e) {


      Object.defineProperty(n, "__esModule", {
        value: !0
      }), n.default = void 0;
      var u = {
        props: {
          height: {
            type: Number,
            default: 20
          },
          background: {
            type: String,
            default: "#F5F5F5"
          }
        }
      };
      n.default = u;
    }
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['components/comp-split/comp-split-create-component', {
    'components/comp-split/comp-split-create-component': function componentsCompSplitCompSplitCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("9396"));
    }
  },
  [
    ['components/comp-split/comp-split-create-component']
  ]
]);