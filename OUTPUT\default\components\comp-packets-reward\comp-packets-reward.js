(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
  ["components/comp-packets-reward/comp-packets-reward"], {
    1383: function _(e, t, n) {


      n.r(t);
      var r = n("717e"),
        c = n.n(r);
      for (var o in r)["default"].indexOf(o) < 0 && function(e) {
        n.d(t, e, function() {
          return r[e];
        });
      }(o);
      t["default"] = c.a;
    },
    "1b10": function b10(e, t, n) {


      n.d(t, "b", function() {
        return c;
      }), n.d(t, "c", function() {
        return o;
      }), n.d(t, "a", function() {
        return r;
      });
      var r = {
          compButton: function compButton() {
            return Promise.all([n.e("common/vendor"), n.e("components/comp-button/comp-button")]).then(n.bind(null, "ca5a"));
          },
          compCheckbox: function compCheckbox() {
            return n.e("components/comp-checkbox/comp-checkbox").then(n.bind(null, "ce9c"));
          },
          compAgreement: function compAgreement() {
            return n.e("components/comp-agreement/comp-agreement").then(n.bind(null, "2587"));
          }
        },
        c = function c() {
          var e = this,
            t = e.$createElement;
          e._self._c;
          e._isMounted || (e.e0 = function(t) {
            e.checked = !e.checked;
          }, e.e1 = function(t) {
            e.isAgreement = !0;
          }, e.e2 = function(t) {
            e.isAgreement = !1;
          });
        },
        o = [];
    },
    "5ffd": function ffd(e, t, n) {


      n.r(t);
      var r = n("1b10"),
        c = n("1383");
      for (var o in c)["default"].indexOf(o) < 0 && function(e) {
        n.d(t, e, function() {
          return c[e];
        });
      }(o);
      n("ad71");
      var u = n("828b"),
        a = Object(u["a"])(c["default"], r["b"], r["c"], !1, null, "222a3e86", null, !1, r["a"], void 0);
      t["default"] = a.exports;
    },
    "717e": function e(_e, t, n) {


      (function(e) {
        var r = n("47a9");
        Object.defineProperty(t, "__esModule", {
          value: !0
        }), t.default = void 0;
        var c = r(n("7eb4")),
          o = r(n("ee10")),
          u = r(n("7ca3")),
          a = n("8f59"),
          i = n("b3c5"),
          f = r(n("a23b")),
          d = r(n("bdc5")),
          s = r(n("5e82"));

        function p(e, t) {
          var n = Object.keys(e);
          if (Object.getOwnPropertySymbols) {
            var r = Object.getOwnPropertySymbols(e);
            t && (r = r.filter(function(t) {
              return Object.getOwnPropertyDescriptor(e, t).enumerable;
            })), n.push.apply(n, r);
          }
          return n;
        }
        (0, i.getConfig)();
        var l = {
          props: {
            userInfo: {
              type: Object,
              default: function _default() {
                return {};
              }
            },
            from: {
              type: String,
              default: "packets"
            },
            inviteTag: {
              type: String,
              default: ""
            },
            wxOpenid: {
              type: String,
              default: ""
            },
            reportSf: {
              type: String,
              default: ""
            }
          },
          data: function data() {
            return {
              checked: !1,
              isAgreement: !1
            };
          },
          computed: function(e) {
            for (var t = 1; t < arguments.length; t++) {
              var n = null != arguments[t] ? arguments[t] : {};
              t % 2 ? p(Object(n), !0).forEach(function(t) {
                (0, u.default)(e, t, n[t]);
              }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : p(Object(n)).forEach(function(t) {
                Object.defineProperty(e, t, Object.getOwnPropertyDescriptor(n, t));
              });
            }
            return e;
          }({}, (0, a.mapState)(["sf"])),
          methods: {
            clickBtn: function clickBtn(t) {
              var n = this;
              return (0, o.default)(c.default.mark(function r() {
                var u;
                return c.default.wrap(function(r) {
                  while (1) switch (r.prev = r.next) {
                    case 0:
                      return f.default.log("CompPacketsReward: clickBtn"), u = {
                        phoneEncrypted: f.default.get(t, "detail.encryptedData", ""),
                        phoneCode: f.default.get(t, "detail.code", ""),
                        phoneIv: f.default.get(t, "detail.iv", ""),
                        userInfoEncrypted: f.default.get(n.userInfo, "encryptedData", ""),
                        userInfoIv: f.default.get(n.userInfo, "iv", ""),
                        extra: JSON.stringify(n.userInfo)
                      }, n.inviteTag && (u.inviteTag = n.inviteTag), n.wxOpenid && (u.wxOpenid = n.wxOpenid), n.reportSf ? u.sf = n.reportSf : n.sf && (u.sf = n.sf), e.showLoading({
                        icon: "none"
                      }), r.next = 8, d.default.register(u, {
                        isInvite: "invite" === n.from,
                        beforeCacheToken: function() {
                          var e = (0, o.default)(c.default.mark(function e() {
                            return c.default.wrap(function(e) {
                              while (1) switch (e.prev = e.next) {
                                case 0:
                                  return n.$emit("success"), e.next = 3, s.default.to({
                                    page: "userProfile",
                                    query: {
                                      from: n.from
                                    }
                                  });
                                case 3:
                                case "end":
                                  return e.stop();
                              }
                            }, e);
                          }));
                          return function() {
                            return e.apply(this, arguments);
                          };
                        }()
                      });
                    case 8:
                      e.hideLoading();
                    case 9:
                    case "end":
                      return r.stop();
                  }
                }, r);
              }))();
            }
          }
        };
        t.default = l;
      }).call(this, n("df3c")["default"]);
    },
    ad71: function ad71(e, t, n) {


      var r = n("bbd4"),
        c = n.n(r);
      c.a;
    },
    bbd4: function bbd4(e, t, n) {}
  }
]);;
(global["webpackJsonp"] = global["webpackJsonp"] || []).push(['components/comp-packets-reward/comp-packets-reward-create-component', {
    'components/comp-packets-reward/comp-packets-reward-create-component': function componentsCompPacketsRewardCompPacketsRewardCreateComponent(module, exports, __webpack_require__) {
      __webpack_require__('df3c')['createComponent'](__webpack_require__("5ffd"));
    }
  },
  [
    ['components/comp-packets-reward/comp-packets-reward-create-component']
  ]
]);