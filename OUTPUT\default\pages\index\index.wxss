page {
    background: #f5f5f5!important
}

.base-bg.data-v-4fd79b5a {
    background: linear-gradient(0deg,#f5f5f5,#c2e5ff 69%,#45b6fe)!important
}

.card-list.data-v-4fd79b5a {
    display: -webkit-flex;
    display: flex
}

.level-img.data-v-4fd79b5a {
    height: 142rpx;
    margin-top: -27rpx;
    object-fit: cover;
    width: 750rpx
}

.myauth-container.data-v-4fd79b5a {
    background-color: #fff;
    font-family: PingFang SC,Microsoft YaHei,sans-serif;
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
    width: 100vw
}

.man-back-bg.data-v-4fd79b5a {
    bottom: 0;
    left: 0;
    pointer-events: none;
    position: absolute;
    right: 0;
    top: 0;
    transition: opacity 1s ease-in-out;
    width: 100vw;
    z-index: 0
}

.man-back-bg.man-back-1.data-v-4fd79b5a {
    background-image: linear-gradient(0deg,#f5f5f5,#7ac4ff 19%,#beedff 87%,#ccf5ff)!important
}

.man-back-bg.man-back-2.data-v-4fd79b5a {
    background-image: linear-gradient(0deg,#f5f5f5,#ced2dd)!important
}

.man-back-bg.man-back-3.data-v-4fd79b5a {
    background-image: linear-gradient(0deg,#f5f5f5,#fdd793)!important
}

.man-back-bg.man-back-4.data-v-4fd79b5a {
    background-image: linear-gradient(0deg,#f5f5f5,#e6d9fe 62%,#abc3ff)!important
}

.man-back-bg.man-back-5.data-v-4fd79b5a {
    background-image: linear-gradient(0deg,#f5f5f5,#302b63 62%,#1b1b25)!important
}

.member-info.data-v-4fd79b5a {
    display: -webkit-flex;
    display: flex;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    padding: 24px 16px 0
}

.member-header.data-v-4fd79b5a {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    width: 60%
}

.img-name.data-v-4fd79b5a,.wangzuo.data-v-4fd79b5a {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex
}

.wangzuo.data-v-4fd79b5a {
    -webkit-flex-direction: column;
    flex-direction: column
}

.baoshi.data-v-4fd79b5a {
    -webkit-animation: baoshi-float-data-v-4fd79b5a 2.2s ease-in-out infinite alternate;
    animation: baoshi-float-data-v-4fd79b5a 2.2s ease-in-out infinite alternate;
    height: 180rpx;
    width: 180rpx
}

@-webkit-keyframes baoshi-float-data-v-4fd79b5a {
    0% {
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }

    100% {
        -webkit-transform: translateY(-18rpx);
        transform: translateY(-18rpx)
    }
}

@keyframes baoshi-float-data-v-4fd79b5a {
    0% {
        -webkit-transform: translateY(0);
        transform: translateY(0)
    }

    100% {
        -webkit-transform: translateY(-18rpx);
        transform: translateY(-18rpx)
    }
}

.dizuo.data-v-4fd79b5a {
    height: 108rpx;
    margin-top: -50rpx;
    width: 211rpx
}

.avatar.data-v-4fd79b5a {
    background: #fff;
    border: 2px solid #fff;
    border-radius: 50%;
    height: 120rpx;
    margin-right: 12px;
    width: 120rpx
}

.info.data-v-4fd79b5a {
    display: -webkit-flex;
    display: flex;
    margin-top: 24rpx
}

.level.data-v-4fd79b5a {
    color: #2176ff;
    font-size: 44rpx;
    font-weight: 700
}

.level-1.data-v-4fd79b5a {
    color: #0d72fb
}

.level-2.data-v-4fd79b5a {
    color: #414d5d
}

.level-3.data-v-4fd79b5a {
    color: #ffac1d
}

.level-4.data-v-4fd79b5a {
    background: linear-gradient(198.25455739854755deg,#ffc8fa,#e4b2fa 29%,#bea0fc 60%,#8892ff);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent
}

.level-5.data-v-4fd79b5a {
    background: linear-gradient(26.952916355797893deg,#fff0cc,#fde1ae 33%,#fed191 67%,#ffc076);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent
}

.growth.data-v-4fd79b5a {
    -webkit-align-items: center;
    align-items: center;
    border-radius: 30rpx;
    display: -webkit-flex;
    display: flex;
    font-size: 20rpx;
    height: 48rpx;
    -webkit-justify-content: center;
    justify-content: center;
    width: 205rpx
}

.growth-1.data-v-4fd79b5a,.growth.data-v-4fd79b5a {
    background: linear-gradient(90deg,hsla(0,0%,100%,.3),hsla(0,0%,100%,0));
    color: #0d72fb
}

.growth-2.data-v-4fd79b5a {
    color: #414d5d
}

.growth-2.data-v-4fd79b5a,.growth-3.data-v-4fd79b5a {
    background: linear-gradient(90deg,hsla(0,0%,100%,.3),hsla(0,0%,100%,0))
}

.growth-3.data-v-4fd79b5a {
    color: #be7800
}

.growth-4.data-v-4fd79b5a {
    background: linear-gradient(90deg,hsla(0,0%,100%,.3),hsla(0,0%,100%,0));
    color: #5960b0
}

.growth-5.data-v-4fd79b5a {
    background: linear-gradient(90deg,rgba(0,0,0,.3),rgba(1,1,1,0));
    color: #ffea7f
}

.upgrade-btn.data-v-4fd79b5a {
    -webkit-align-items: center;
    align-items: center;
    background: linear-gradient(146deg,#24dfff,#0d72fb);
    border-radius: 24rpx;
    color: #fff;
    display: -webkit-flex;
    display: flex;
    font-size: 24rpx;
    font-weight: 700;
    height: 48rpx;
    -webkit-justify-content: center;
    justify-content: center;
    line-height: 33rpx;
    width: 120rpx
}

.upgrade-btn-5.data-v-4fd79b5a {
    background: linear-gradient(146deg,#ffec5e,#ffcb76);
    color: #473500
}

.upgrade-btn-4.data-v-4fd79b5a {
    background: linear-gradient(146deg,#d3bcff,#8892ff);
    color: #fff
}

.upgrade-btn-3.data-v-4fd79b5a {
    background: linear-gradient(146deg,#ffd756,#ffac1d);
    color: #fff
}

.upgrade-btn-2.data-v-4fd79b5a {
    background: linear-gradient(146deg,#b8c2d9,#404e5d);
    color: #fff
}

.upgrade-btn-1.data-v-4fd79b5a {
    background: linear-gradient(146deg,#24dfff,#0d72fb);
    color: #fff
}

.level-progress.data-v-4fd79b5a {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex;
    margin-top: 16px
}

.level-label.data-v-4fd79b5a {
    color: #b0c4de;
    font-size: 14px
}

.level-label.active.data-v-4fd79b5a {
    color: #2176ff;
    font-weight: 700
}

.progress-bar.data-v-4fd79b5a {
    background: #e3f1ff;
    border-radius: 4px;
    -webkit-flex: 1;
    flex: 1;
    height: 8px;
    margin: 0 8px;
    position: relative
}

.progress.data-v-4fd79b5a {
    background: linear-gradient(90deg,#4fc3ff,#2176ff);
    border-radius: 4px;
    height: 100%
}

.level-values.data-v-4fd79b5a {
    color: #b0c4de;
    display: -webkit-flex;
    display: flex;
    font-size: 12px;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    margin-top: 2px;
    padding: 0 8px
}

.member-benefits.data-v-4fd79b5a {
    background: hsla(0,0%,100%,.3);
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,.03);
    margin: 18px 12px 0;
    padding: 16px 12px 8px
}

.benefits-title.data-v-4fd79b5a {
    color: rgba(0,0,0,.8);
    font-size: 32rpx;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 10rpx 20rpx
}

.benefits-title.data-v-4fd79b5a,.more.data-v-4fd79b5a {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex
}

.more.data-v-4fd79b5a {
    color: rgba(0,0,0,.3);
    font-size: 28rpx;
    font-weight: 500;
    font-weight: 400
}

.benefits-list.data-v-4fd79b5a {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap
}

.benefit-item.data-v-4fd79b5a {
    -webkit-align-items: center;
    align-items: center;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-direction: column;
    flex-direction: column;
    margin-bottom: 16px;
    position: relative;
    width: 25%
}

.benefit-icon.data-v-4fd79b5a {
    height: 80rpx;
    margin-bottom: 6px;
    width: 80rpx
}

.benefit-text.data-v-4fd79b5a {
    color: rgba(0,0,0,.8);
    font-size: 13px;
    text-align: center
}

.upgrade-card-section.data-v-4fd79b5a {
    background: #fff;
    border-radius: 16px;
    margin: 18px 12px 0;
    padding: 18px 12px 12px
}

.upgrade-card-section-title.data-v-4fd79b5a {
    color: #2d3a4a;
    font-size: 32rpx;
    margin-bottom: 12px
}

.upgrade-card-box.data-v-4fd79b5a {
    display: -webkit-flex;
    display: flex;
    gap: 16px
}

.upgrade-card.data-v-4fd79b5a {
    background: #fff;
    border-radius: 12px;
    box-sizing: border-box;
    display: -webkit-flex;
    display: flex;
    -webkit-flex: 1;
    flex: 1;
    -webkit-justify-content: space-between;
    justify-content: space-between;
    min-height: 90px;
    padding: 20rpx 30rpx
}

.upgrade-card.right.data-v-4fd79b5a {
    background: url(https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/bg-qysj-czz.png);
    background-size: cover
}

.upgrade-card.left.data-v-4fd79b5a {
    background: url(https://obs-yonghuyunying.obs.cn-south-1.myhuaweicloud.com/member-center/static/bg-qysj-wd.png);
    background-size: cover
}

.upgrade-title.data-v-4fd79b5a {
    font-size: 28rpx;
    font-weight: 700;
    margin-bottom: 6rpx
}

.upgrade-title.blue.data-v-4fd79b5a {
    color: #0091ff
}

.upgrade-title.red.data-v-4fd79b5a {
    color: #ff5841
}

.upgrade-desc.data-v-4fd79b5a {
    color: rgba(0,0,0,.4);
    font-size: 24rpx
}

.upgrade-img.data-v-4fd79b5a {
    height: 48px;
    margin-left: 12px;
    width: 48px
}

.gift-section.data-v-4fd79b5a {
    background: #f6faff;
    border-radius: 16px;
    margin: 18px 12px 0;
    padding: 18px 12px 12px
}

.gift-title.data-v-4fd79b5a {
    color: #2d3a4a;
    font-size: 32rpx;
    margin-bottom: 12px
}

.banner-section.data-v-4fd79b5a {
    margin: 0
}

.banner-img.data-v-4fd79b5a {
    border-radius: 24rpx;
    box-shadow: 0 2px 8px rgba(0,0,0,.03);
    height: 176rpx;
    width: 654rpx
}

.benefit-lock.data-v-4fd79b5a {
    background: #dbdbdb;
    border-radius: 17rpx;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,.04);
    color: rgba(0,0,0,.4);
    font-size: 16rpx;
    height: 22rpx;
    left: 50%;
    pointer-events: none;
    position: absolute;
    text-align: center;
    top: 60rpx;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    white-space: nowrap;
    width: 68rpx;
    z-index: 2
}
